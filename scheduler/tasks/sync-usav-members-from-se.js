
const {
    MEMBER_TYPE,
    CANCELED_MEMBER_STATUS,
    INVALID_NUMERIC_STATUS,
} = require('../../api/lib/SEUtilsService');

class SyncUsavMembersFromSe {
    constructor() {}

    #MEMBERS_LIMIT = 100;

    async processSync(memberType, season) {
        if(!Object.values(MEMBER_TYPE).includes(memberType)) {
            throw new Error(`Invalid member type: ${memberType}`);
        }

        if(!season) {
            throw new Error(`Invalid season: ${season}`);
        }

        try {
            const membersFromDb = await this.#getMembersToProcess(memberType, season);

            if(!membersFromDb.length) {
                return;
            }

            const adultRoleRules = await SportEngineMemberService.import.process.getAdultMemberRules();

            for(const memberFromDb of membersFromDb) {
                const USAVData = await this.#getMemberUSAVData(memberFromDb);

                let dataForUpdate;

                if(_.isEmpty(USAVData)) {
                    dataForUpdate = this.#prepareNotFoundMember(memberType)
                } else {
                    dataForUpdate = this.#prepareFoundMember(USAVData, memberType, adultRoleRules);
                }

                await this.#updateMemberDbRow(memberFromDb, dataForUpdate);
            }
        } catch (err) {
            throw err;
        }
    }

    #getMembersToProcess(memberType, season) {
        if(memberType === MEMBER_TYPE.STAFF) {
            return this.#getStaffersToProcess(season);
        } else if(memberType === MEMBER_TYPE.ATHLETE) {
            return this.#getAthletesToProcess(season);
        } else {
            throw new Error(`Invalid member type: ${memberType}`);
        }
    }

    async #getAthletesToProcess(season) {
        const query = `
            WITH filtered_roster AS (SELECT DISTINCT master_athlete_id
                                       FROM roster_athlete
                                       WHERE deleted IS NULL
                                         AND deleted_by_user IS NULL
                                         AND roster_team_id IS NOT NULL),
                   filtered_events AS (SELECT event_id, MIN(date_start) AS nearest_event_start
                                       FROM event
                                       WHERE deleted IS NULL
                                         AND date_start > CURRENT_DATE
                                       GROUP BY event_id)
                  SELECT 'master_athlete'::text                                     AS update_table,
                         'master_athlete_id'::text                                  AS update_table_id,
                         ma.usav_number                                             AS usav_code,
                         to_char(ma.birthdate, 'YYYY-MM-DD')                        AS birthdate,    
                         EXISTS (SELECT 1
                                 FROM filtered_roster fr
                                 WHERE fr.master_athlete_id = ma.master_athlete_id) AS has_roster,
                         ma.aau_membership_id IS NOT NULL                           AS has_aau,
                         ma.master_athlete_id                                       AS id
                  FROM master_athlete ma
                           LEFT JOIN LATERAL (
                              SELECT MIN(fe.nearest_event_start) AS nearest_event_start
                              FROM filtered_events fe
                                       JOIN roster_athlete ra ON ra.event_id = fe.event_id
                              WHERE ra.master_athlete_id = ma.master_athlete_id
                           ) fe ON TRUE
                  WHERE ma.deleted IS NULL
                    AND ma.season = $1
                    AND ma.usav_number IS NOT NULL
                  ORDER BY ma.sportengine_sync, fe.nearest_event_start NULLS LAST
                  LIMIT $2;
        `;

        const { rows } = await Db.query(query, [season, this.#MEMBERS_LIMIT]);

        return rows;
    }

    async #getStaffersToProcess(season) {
        const query = `
            SELECT 'master_staff'::text AS                                      update_table,
                   'master_staff_id'::text AS                                   update_table_id,
                    ms.usav_number       AS                                     usav_code,
                    to_char(ms.birthdate, 'YYYY-MM-DD') AS                      birthdate,
                    COUNT(COALESCE(rsr.roster_team_id, msr.master_team_id)) > 0 has_roster,
                    ms.aau_membership_id IS NOT NULL AS                         has_aau,
                    ms.master_staff_id   AS                                     id
                 FROM master_staff ms
                          LEFT JOIN master_staff_role msr ON msr.master_staff_id = ms.master_staff_id
                          LEFT JOIN roster_staff_role rsr
                                    ON rsr.master_staff_id = ms.master_staff_id AND rsr.deleted IS NULL AND
                                       rsr.deleted_by_user IS NULL
                          LEFT JOIN roster_team rt ON rt.master_team_id = msr.master_team_id AND rt.deleted IS NULL
                          LEFT JOIN event e ON e.event_id = rt.event_id AND e.deleted IS NULL AND
                                               e.date_start::DATE > CURRENT_DATE
                 WHERE ms.deleted IS NULL
                   AND ms.season = $1
                   AND ms.usav_number IS NOT NULL
                 GROUP BY ms.master_staff_id, ms.sportengine_sync
                 ORDER BY ms.sportengine_sync,  MIN(e.date_start) NULLS LAST
                 LIMIT $2
        `;

        const { rows } = await Db.query(query, [season, this.#MEMBERS_LIMIT]);

        return rows;
    }

    async #getMemberUSAVData(member) {
        const params = {
            usav_code: member.usav_code,
            birthdate: { date: member.birthdate}
        };

        try {
            const { memberData } = await SportEngineMemberService.validation.processMember(
                params,
                SportEngineMemberService.validation.MEMBER_TYPE.junior
            );

            return memberData;
        } catch (err) {
            if(err.validation === 'SportEngine member not found') {
                return [];
            }

            throw err;
        }
    }

    #prepareNotFoundMember(memberType) {
        const common = {
            membership_status: CANCELED_MEMBER_STATUS,
            safesport_statusid: INVALID_NUMERIC_STATUS,
        };


        if(memberType === MEMBER_TYPE.STAFF) {
            common.is_impact = false;
            common.bg_screening = INVALID_NUMERIC_STATUS;
        }

        return common;
    }

    #prepareFoundMember(member, memberType, adultRoleRules) {
        if(memberType === MEMBER_TYPE.ATHLETE) {
            return SportEngineMemberService.import.process.memberImport.membersMapper.athleteFieldsMapper(member);
        } else {
            return SportEngineMemberService.import.process.memberImport.membersMapper.staffFieldsMapper(
                member,
                adultRoleRules
            );
        }
    }

    #updateMemberDbRow(memberFromDb, usavData) {
        let updateData = { ...usavData };

        if(this.#memberShouldBeDeleted(memberFromDb, usavData)) {
            updateData.deleted = knex.fn.now();
        }

        const query = knex(memberFromDb.update_table)
            .update({
                ...updateData,
                sportengine_sync: knex.fn.now(),
            })
            .where(`${memberFromDb.update_table_id}`, memberFromDb.id);

        return Db.query(query);
    }

    #memberShouldBeDeleted(memberFromDb, usavData) {
        return usavData.membership_status === CANCELED_MEMBER_STATUS &&
            !memberFromDb.has_roster &&
            !memberFromDb.has_aau;
    }
}

module.exports = new SyncUsavMembersFromSe();
