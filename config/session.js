const argv  = require('optimist').argv;
const utils = require('../api/lib/swUtils');

const IS_PROD   = argv.prod;
const CONFIG    = utils.tryRequire(`../../../connection/${ IS_PROD ? 'prod' : 'dev' }`, 'session') || {};
const REDIS_URL = utils.getConnection('REDIS_URL');
const CONNECTION_STRING_TYPE = REDIS_URL.startsWith('redis:') ? 'url' : 'socket';
const routesDisabled = [
    '/api/swb/*',
    '/api/ua/*',
    '/api/swr/*',
    '/api/swt/*',
    '/api/ths/*',
    'get /api/official-app/events',
    '/api/kiosk/*',
    '/api/online-checkin/*',
    '/api/custom-payment/*',
    'POST /api/safeguard',
    '/api/event-checkin/*',

    '/api/esw/*',
    '/api/tpc/*',
    '/api/ncsa/*',
    '/refund',

    'GET /api/tickets/events',
    'GET /api/tickets/events/:event',
    'GET /api/tickets/events/:event/types',
    'GET /api/tickets/events/:event/coupons',
    'GET /api/tickets/events/:event/invoice/:invoice',
    'GET /api/tickets/events/:event/invoice/:invoice/type-change',
    'POST /api/tickets/user/check',
    'POST /api/tickets/usav/check',
    'POST /api/tickets/buy/:type',
    'POST /api/tickets/event/:event/invoice/:invoice/pay',
    'POST /api/tickets/coupon/check',
    'GET /api/tickets/discounts/resend',
    'POST /api/tickets/event/:event/change-type/:invoice',
    'POST /api/tickets/events/:event/payment-discount',
    'GET /r/:barcode',
    'GET /event/:event/ticket/:barcode/receipt',
    'GET /tickets/receipt/:code',
    '/api/tickets/duplicates/validation',

    'GET /api/ticket-entry-code/event/:event/code/:code/validation',

    'POST /api/stripe/webhook',
    'POST /api/stripe/webhook-upd',
    'GET /stripe/express_acc_connect',

    'POST /api/vertical-insurance/webhook',

    'POST /api/recover/send',
    'GET /api/recover/check_code',
    'POST /api/recover/reset',

    'POST /api/sms/report',

    'GET /api/home/<USER>',
    'GET /api/home/<USER>/:id',
    'GET /api/home/<USER>/:id/entrance-queue',

    '/api/admin/db/*',
    '/api/admin/club/staff/roles',
    '/api/admin/club/master/athlete/*',
    '/api/admin/club/members/find',
    'POST /api/admin/club/athletes/update-height',
    '/api/admin/club/:club/teams',
    '/api/admin/usav/*',
    '/api/admin/aau/*',
    '/api/admin/webpoint/*',
    '/api/admin/events',
    '/api/admin/event/*',
    '/api/admin/payments',
    '/api/admin/payments/*',
    '/api/admin/team/*',
    '/api/admin/payment/*',
    '/api/admin/integrity/*',
    '/api/admin/teams/*',
    '/api/admin/redis/*',
];
const routesParseData = [
    { regExp: /\*/g, patternData: '.*' }, //for *
    { regExp: /:(.*?)($|\/)/g, patternData: '(.*?)($|\/)' }, //for :variable
];
const routesDisabledParsedArr = utils.parseRoutesArr(routesDisabled, routesParseData);

module.exports.session = {
    adapter     : 'redis',
    secret      : argv['session-secret'] || process.env.SESSION_SECRET || 'f386db9378dffbb5925247a57cbb7191',
    [CONNECTION_STRING_TYPE]: REDIS_URL,
    ttl         : 24 * 60 * 60,
    db          : argv['redis-db'] || process.env.REDIS_DB || IS_PROD ? 0 : 1,
    pass        : argv['redis-pass'] || process.env.REDIS_PASS || undefined,
    prefix      : IS_PROD ? 'sess:' : 'sess-dev:',
    name        : CONFIG.cookieID || 'sw.sid',
    cookie      : {
        /* https://tools.ietf.org/html/rfc6265#section-4.1.2.3 */
        domain  : CONFIG.domain || (void 0),
        maxAge  : 24 * 60 * 60 * 1000,
        // Security settings for production
        secure  : !!IS_PROD,  // Only send over HTTPS in production
        httpOnly: true,                    // Prevent XSS attacks
    },
    isSessionDisabled: req => utils.isSessionDisabled(req, routesDisabledParsedArr)

}
