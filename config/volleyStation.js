module.exports.volleyStation = {
    apiKey: 'vs_dev_2eec4d4f84c8fad36784c3ceb5f2ef6113745fe838356675ba4a3f6837a74dea',

    rateLimit: {
        read: {
            points: 25,
            duration: 5,
            enableSoftTimeout: true,
            softTimeoutMs: 1000,
            execEvenly: true,
        }, // No more than 25 read requests per 5 seconds
        write: {
            points: 10,
            duration: 5,
            enableSoftTimeout: true,
            softTimeoutMs: 1000,
            execEvenly: true,
        } // No more than 10 write requests per 5 seconds
    }
};
