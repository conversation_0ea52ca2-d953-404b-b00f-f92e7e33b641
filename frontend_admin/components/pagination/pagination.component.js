angular.module('SportWrench').component('pagination', {
    templateUrl 	: 'components/pagination/pagination.html',
    bindings 		: {
        page    : '=',
        limit   : '<',
        total   : '<',
        current : '<',
        loading : '='
    },
    controller  : Pagination
});

function Pagination () {
    let self = this;

    this.paginationText = function () {
        let from = (self.page - 1) * self.limit;
        return `${from + 1} - ${from + self.current} of ${self.total}`
    };

    this.decrementPage = function (toFirst) {
        if(self.page <= 1 || self.loading) {
            return;
        }

        self.loading = true;

        if(toFirst) {
            self.page = 1;
        } else {
            self.page--;
        }
    };

    this.incrementPage = function (toLast) {
        if((self.limit > self.current) || self.loading) {
            return;
        }

        self.loading = true;

        if(toLast) {
            self.page = parseInt(self.total / self.limit, 10);
        }

        self.page++;
    };

    this.isToFirstPageButtonDisabled = function () {
        return self.page === 1 || (self.page !== 1 && self.loading);
    };

    this.isDecrementPageDisabled = function () {
        return self.page === 1 || self.loading;
    };

    this.isIncrementPageDisabled = function () {
        return (self.current < self.limit) || self.loading;
    };

    this.isToLastPageButtonDisabled = function () {
        return (self.current < self.limit) || self.loading;
    };
}
