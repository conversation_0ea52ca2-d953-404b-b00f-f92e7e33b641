angular.module('SportWrench').component('swSearchbox', {
    templateUrl: 'components/searchbox/searchbox.html',
    bindings: {
        css                     : '@',
        reload                  : '&',
        inputModel              : '=',
        placeholder             : '@',
        time                    : '@',
        isReadonly              : '&',
        registerOnClearHandler  : '&?regOnClear',
        reloadTime              : '<'
    },
    transclude: {
        info: '?info'
    },
    controller: ['$timeout', '$transclude', function ($timeout, $transclude) {
         var self = this, DEFAULT_RELOAD_MS = self.reloadTime || 500, timer;

        self.onKeypress = function ($event) {
            if(timer) {
                $timeout.cancel(timer);
            }

            if($event.charCode === 13) {
                return self.reload();
            }

            timer = $timeout(function () {
                self.reload();
            }, parseInt(self.time, 10) || DEFAULT_RELOAD_MS);
        };

        self.$onDestroy = function () {
            if(timer) {
                $timeout.cancel(timer);
            }
        };

        self.boxClass = function () {
            return {
                'form-group--custom sw-searchbox': true,
                'increase-mb': $transclude.isSlotFilled('info')
            };
        };

        self.$onInit = function () {
            /*
            * Notes:
            * The handler registration logic can be implemened by calling
            * parent component controller's action.
            * But for now we do not have parent component, so the "handler registrator"
            * have to be passed to the component.
            */
            if(self.registerOnClearHandler) {
                self.registerOnClearHandler({ handler: function () {
                    self.inputModel = '';
                } });
            }
        };
    }]
});
