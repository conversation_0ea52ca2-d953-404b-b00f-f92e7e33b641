angular.module('SportWrench').directive('stickyHeader', stickyHeader);

stickyHeader.$inject = ['$timeout'];

function stickyHeader($timeout) {
    return {
        restrict: 'A',
        link: function($scope, element, attr) {
            $timeout(function() {
                if ($(window).width() >= 768) {
                    element.stickyTableHeaders();
                }
            });
        }
    };
}
