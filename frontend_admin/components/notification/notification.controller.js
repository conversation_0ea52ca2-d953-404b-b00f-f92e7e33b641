angular.module('SportWrench').controller('notificationCtrl', notificationCtrl);

notificationCtrl.$inject = ['$rootScope', '$scope', '$timeout'];

function notificationCtrl($rootScope, $scope, $timeout) {
    $rootScope.notifications = [];
    $rootScope.$on('createNotification', function(event, message, type, time) {
        type = (typeof type === "undefined" ? "warning": type);
        message = message || "Error";
        $rootScope.notifications.push({
            "type": type,
            "message": message
        });
        $timeout(function(){
            $rootScope.notifications.shift();
        }, time ? time*1000 : 9000);
    });

    $scope.closeNotification = function(index) {
        $rootScope.notifications.splice(index, 1);
    };
}
