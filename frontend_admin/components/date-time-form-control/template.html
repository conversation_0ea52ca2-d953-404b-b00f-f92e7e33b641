<div class="input-group date-time-control">
    <input
        type="text" 
        class="white-ro form-control pointer" 
        datetime-picker="{{format}}"
        min-date="minDate"
        max-date="maxDate"
        is-open="settings.isOpen"
        ng-click="toggleOpen()"
        ng-model="date"
        ng-model-options="{ debounce: 400 }"
        name="{{name}}"
        datepicker-options="{showWeeks: false}"
        ng-required="fieldRequired"
        ng-change="change()"
        default-time="{{deftime}}"
        enable-time="disableTimepicker"
        readonly
    >
    <span class="input-group-addon">
        <span class="glyphicon glyphicon-calendar"></span>
    </span>
</div>
