angular.module('SportWrench')

.constant('INTERNAL_ERROR_MSG', 'Server is currently unavailable. Please try again in a few seconds.')

.run([
    '$rootScope', '$templateCache', '$state', '$location', '$uibModalStack',
    function($rootScope, $templateCache, $state, $location, $uibModalStack)
{
    $rootScope.$on('$stateChangeStart', function(e, to) {
        console.log('to.url', to.url);

        var top = $uibModalStack.getTop();
           
        if (top) {
            $uibModalStack.dismiss(top.key);
        }
    });
}])

.config([
    '$httpProvider', '$uiViewScrollProvider', 'cfpLoadingBarProvider', 'INTERNAL_ERROR_MSG', '$provide', 'toastrConfig',
    function($httpProvider, $uiViewScrollProvider, cfpLoadingBarProvider, INTERNAL_ERROR_MSG, $provide, toastrConfig)
{

    cfpLoadingBarProvider.latencyThreshold = 500; 

    $httpProvider.defaults.useXDomain = true;
    delete $httpProvider.defaults.headers.common['X-Requested-With'];

    $uiViewScrollProvider.useAnchorScroll();

    $httpProvider.interceptors.push(['$rootScope', '$q', function($rootScope, $q) {
        var _serverBusy     = 'The server is busy. Please try again later',
            _requestError   = 'Request Error';
        return {
            responseError: function (rejection) {
                var _msg, _type;
                if(rejection) {
                    switch(rejection.status) {
                        case 502:
                            _msg = _serverBusy;
                            _type = 'warning';
                            break;
                        case 400:
                            _msg = (rejection.data && rejection.data.validation) || _requestError;
                            _type = 'danger'
                            break;
                        default:
                            _msg = (rejection.data && rejection.data.error) || rejection.statusText || _requestError;
                            _type = 'warning'
                            break;
                    }
                    $rootScope.$emit('createNotification', _msg, _type);
                }
                return $q.reject(rejection)
            },
        };
    }]);

    $httpProvider.defaults.headers.post[ 'Content-Type' ] = 'application/json;charset=utf-8';
    $httpProvider.defaults.headers.put[ 'Content-Type' ] = 'application/json;charset=utf-8';
    $httpProvider.defaults.headers.get = {
        'Cache-Control': 'no-cache',
        'Pragma': 'no-cache',
        'Content-Type': 'application/json'
    };

    $provide.decorator('$locale', ['$delegate', function($delegate) {
      if($delegate.id === 'en-us') {
        $delegate.NUMBER_FORMATS.PATTERNS[1].negPre = '-\u00A4';
        $delegate.NUMBER_FORMATS.PATTERNS[1].negSuf = '';
      }
      return $delegate;
    }])

    angular.extend(toastrConfig, {
        autoDismiss: false,
        containerId: 'toast-container',
        maxOpened: 0,    
        newestOnTop: true,
        positionClass: 'toast-top-right',
        preventDuplicates: false,
        preventOpenDuplicates: false,
        target: 'body',
        timeOut: 7000
    });
}]);
