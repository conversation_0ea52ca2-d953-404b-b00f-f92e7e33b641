angular.module('SportWrench').component('swStripeStatistics', {
    templateUrl : 'public/stripe-statistics/stripe-statistics-table.html',
    controller  : ['SWStatisticsService', SWStripeStatisticsController]
});

function SWStripeStatisticsController (SWStatisticsService) {
    this.getColumnCls = function (columnName, row) {
        let clsList = [];

        if (this.settings[columnName].needs_sum) {
            clsList.push('text-right');
        }

        /**
         * We assume this a "subtotals" row
         */
        if (row && (row[0] === null)) {
            clsList.push('bg-info');
        }

        return clsList;
    }

    this.showSpacerRow = function (columns) {
        return (columns[0] === null)
    }

    this.reloadStatistics = function (filters) {
        SWStatisticsService.getSWStripeAccStatistics(filters)
        .then(({ stats }) => {
            this.columns    = stats.columns;
            this.rows       = stats.rows;
            this.disputes   = stats.disputes;
            this.totals     = stats.totals;
            this.settings   = stats.settings;

            this.lychref    = stats.last_year_charges_refunds;
            this.chList     = stats.last_year_charges_refunds_list;

            this.adjustedTotals = stats.adjustedTotals;

            this.notMapped = stats.not_mapped_charges;
        });
    }

    this.isNotMapped = function (ch) {
        return _.includes(this.notMapped, ch.id);
    }

    this.reloadStatistics();
}
