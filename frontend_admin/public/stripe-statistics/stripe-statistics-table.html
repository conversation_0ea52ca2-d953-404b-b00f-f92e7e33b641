<statistics-filters-form on-submit="$ctrl.reloadStatistics(filters)"></statistics-filters-form>

<table class="table table-condensed" wait-me="$ctrl.columns" sticky-header>
    <thead>
        <tr>
            <th 
                ng-repeat="colName in $ctrl.columns" 
                ng-bind="colName" 
                ng-class="$ctrl.getColumnCls(colName)" 
                ng-style="{ width: $ctrl.settings[colName].width + 'px' }"
            ></th>
        </tr>
    </thead>
    <tbody>
        <!-- Totals -->
        <tr class="bg-warning text-bold">
            <td ng-repeat="c in $ctrl.columns" ng-bind="$ctrl.totals[$index]" ng-class="$ctrl.getColumnCls(c)"></td>
        </tr>
        <!-- Totals End -->

        <!-- Lost Disputes
        <tr class="bg-danger text-bold">
            <td colspan="3">Lost Disputes Penalty</td>
            <td colspan="{{$ctrl.columns.length - 4}}"></td>
            <td
                ng-bind="$ctrl.disputes"
                class="text-right">
            </td>
        </tr>
        <!-- Last Year Charges Refunded in 2017
        <tr class="bg-danger text-bold">
            <td colspan="5">Stripe Balance Adjustments due to (non 2017) payment Refunds</td>
            <td colspan="{{$ctrl.columns.length - 6}}"></td>
            <td
                ng-bind="$ctrl.lychref"
                class="text-right">
            </td>
        </tr>
        <!-- Last Year Charges Refunded in 2017 END -->


        <!-- Adjusted Income -->
        <tr class="bg-success text-bold">
            <td colspan="5">Adjusted Income</td>
            <td colspan="{{$ctrl.columns.length - 6}}"></td>
            <td
                ng-bind="$ctrl.adjustedTotals"
                class="text-right">
            </td>
        </tr>
        <!-- Adjusted Income End -->
        <tr ng-repeat-start="row in $ctrl.rows">
            <td ng-repeat="c in $ctrl.columns" ng-bind="row[$index]" ng-class="$ctrl.getColumnCls(c, row)"></td>
        </tr>
        <tr ng-repeat-end ng-if="$ctrl.showSpacerRow(row)" style="height: 20px">
            <td colspan="{{$ctrl.columns.length}}"></td>
        </tr>
    </tbody>
</table>

<p class="lead">Adjustments</p>

<table class="table table-condensed">
    <thead>
        <tr>
            <th>Event ID</th>
            <th>Charge ID</th>
            <th>Stripe Acc.</th>
            <th style="width: 500px">Description</th>
            <th>App. Fee Reversal</th>
            <th>Stripe Fee Reversal</th>
            <th>Income Adjustment</th>
        </tr>
    </thead>
    <tbody>
        <tr ng-repeat="ch in $ctrl.chList" ng-class="{ 'bg-danger': $ctrl.isNotMapped(ch) }">
            <td ng-bind="ch.event_id"></td>
            <td ng-bind="ch.charge_id"></td>
            <td ng-bind="ch.stripe_account_name"></td>
            <td ng-bind="ch.description"></td>
            <td class="text-right" ng-bind="ch.application_fee"></td>
            <td class="text-right" ng-bind="ch.stripe_processing_fee"></td>
            <td class="text-right" ng-bind="ch.adjustment"></td>
        </tr>
    </tbody>
</table>
