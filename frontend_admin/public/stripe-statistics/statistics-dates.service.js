angular.module('SportWrench').service('StatisticsDatesService', ['moment', StatisticsDatesService]);


function StatisticsDatesService (moment) {
    this._moment = moment;
}

Object.defineProperty(StatisticsDatesService.prototype, 'MINIMUM_YEAR', {
    value           : 2014,
    writable        : false,
    configurable    : false
});

Object.defineProperty(StatisticsDatesService.prototype, 'DATE_FORMAT', {
    value           : 'YYYY-MM-DD',
    writable        : false,
    configurable    : false
});

/**
 * Produces an array like [2014, 2015, 2016, 2017]
 * 
 * @return {Array}  - An array of years
 */
StatisticsDatesService.prototype.getListOfYears = function () {
    let currentYear = (new Date()).getFullYear();

    let listOfYears = [];

    for (let y = this.MINIMUM_YEAR; y <= currentYear; ++y) {
        listOfYears.push(y);
    }

    return listOfYears;
}

StatisticsDatesService.prototype.formatDate = function (date) {
    if (!(date instanceof Date)) {
        throw new Error('Date object required');
    }

    return this._moment(date).format(this.DATE_FORMAT);
}
