angular.module('SportWrench').component('statisticsFiltersForm', {
    templateUrl : 'public/stripe-statistics/filters-form/filters-form.html',
    bindings: {
        onSubmit: '&'
    },
    controller  : ['StatisticsDatesService', StatisticsFiltersFormController]
});

function StatisticsFiltersFormController (StatisticsDatesService) {

    const DATE_FIELDS_TO_FORMAT = ['after', 'before'];

    this.years = StatisticsDatesService.getListOfYears();
    this.filters = {};

    this.onFormSubmit = function () {

        let preparedFilters = this.formatFiltersDates(this.filters);

        this.onSubmit({ filters: preparedFilters });
    }

    this.formatFiltersDates = function (filters) {
        filters = Object.assign({}, filters);

        DATE_FIELDS_TO_FORMAT.forEach(prop => {
            if (filters[prop]) {
                filters[prop] = StatisticsDatesService.formatDate(filters[prop]);
            }
        });

        return filters;
    }

    this.onYearChanged = function () {
        let year            = this.filters.year;

        if (!year) {
            return;
        }
        
        let isYearEndDate   = true;

        this.filters.after  = this.changeYear(year, this.filters.after);
        this.filters.before = this.changeYear(year, this.filters.before, isYearEndDate);
    }

    this.changeYear = function (year, date, isYearEndDate) {
        if (date) {
            let month   = date.getMonth();
            let day     = date.getDate();

            date = new Date(year, month, day);
        } else {
            date = isYearEndDate ? new Date(year, 11, 31) : new Date(year, 0, 1);
        }

        return date;
    }
}
