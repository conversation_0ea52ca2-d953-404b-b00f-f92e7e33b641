<form class="form-inline" name="$ctrl.StripeAccStatsForm" ng-submit="$ctrl.onFormSubmit()">

    <div class="form-group">
        <label class="sr-only" for="stripe-stats-quick-year">Quick Year</label>
        <select 
            class="form-control" 
            name="year" 
            id="stripe-stats-quick-year"
            ng-model="$ctrl.filters.year"
            ng-change="$ctrl.onYearChanged()"
            ng-options="y as y for y in $ctrl.years"
        >
            <option value="" selected>Choose a year ...</option>
        </select>
    </div>

    <div class="form-group">
        <label class="sr-only" for="stripe-stats-after">After</label>
        <date-time-form-control
            date="$ctrl.filters.after"
            format="MM/dd/yyyy"
            timepicker="false"
            name="after"
            id="stripe-stats-after"
        >
        </date-time-form-control>
    </div>

    <div class="form-group">
        <label class="sr-only" for="stripe-stats-before">Before</label>
        <date-time-form-control
            date="$ctrl.filters.before"
            format="MM/dd/yyyy"
            timepicker="false"
            name="after"
            id="stripe-stats-before"
        >
        </date-time-form-control>
    </div>

    <button type="submit" class="btn btn-default">Apply</button>
</form>
