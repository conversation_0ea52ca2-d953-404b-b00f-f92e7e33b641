<table class="table table-condensed">
    <thead>
        <tr>
            <th>Id</th>
            <th>Name</th>
        </tr>
    </thead>
    <tbody>
        <tr 
            ng-repeat-start="e in data.events" 
            ng-init="e.collapsed = true"
            ng-class="{'alert-info': !e.collapsed}">
            <td>{{e.event_id}}</td>
            <td><a href="" ng-click="e.collapsed = !e.collapsed">{{e.name}}</a></td>
        </tr>
        <tr ng-repeat-end collapse="e.collapsed">
            <td colspan="2">
                <table class="table table-condensed" ng-if="!e.collapsed">
                    <thead>
                        <tr>
                            <th>Id</th>
                            <th>Team Name</th>
                            <th>Athletes Count</th>
                            <th>Staff Count</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr ng-repeat="t in e.invalid_teams">
                            <td>{{t.roster_team_id}}</td>
                            <td>{{t.team_name}}</td>
                            <td>{{t.athletes}}</td>
                            <td>{{t.staff_roles}}</td>
                        </tr>
                    </tbody>
                </table>
            </td>
        </tr>
    </tbody>
</table>
