angular.module('SportWrench').service('SWStatisticsService', ['$http', '$q', 'UtilsService', SWStatisticsService]);

function SWStatisticsService ($http, $q, UtilsService) {
	this._$http 		= $http;
	this._baseURL 		= '/api/admin/sw-statistics/';
	this._$q 			= $q; 
	this._utils 		= UtilsService;
	this._initFields 	= ['booths', 'teams', 'tickets'];

	Object.defineProperty(this, 'TEAMS', {
		value 			: 'teams',
		writable 		: false,
	  	configurable 	: false
	});

	Object.defineProperty(this, 'TICKETS', {
		value 			: 'tickets',
		writable 		: false,
	  	configurable 	: false
	});

	Object.defineProperty(this, 'BOOTHS', {
		value 			: 'booths',
		writable 		: false,
	  	configurable 	: false
	});

	this._allTypes 		= [this.TEAMS, this.TICKETS, this.BOOTHS];
}

SWStatisticsService.prototype.getEventsList = function (params) {
	return this._$http.get(this._baseURL + 'events-list', { params: params }).then(function (resp) {
		return (resp.data && resp.data.events) || []
	})
}

SWStatisticsService.prototype.getEventStatistics = function (eventID, params) {
	return this._$http.get(this._baseURL + 'event/' + eventID + '/stats', { params: params }).then(function (resp) {
		return (resp.data && resp.data.stats) || {};
	});
}

SWStatisticsService.prototype._parseNumbers = function (obj) {
	Object.keys(obj).forEach(function (key) {
		obj[key] = parseFloat(obj[key]);
	});
}

SWStatisticsService.prototype.loadStatsForEventsList = function (eventsList, range, onLoadingStart, onLoadingFinished) {
	var self = this;

	return eventsList.reduce(function (prev, event) {
		return prev.then(function () {

			onLoadingStart(event.event_id);

			return self.getEventStatistics(event.event_id, range)
			.then(function (stats) {
				// debugger;
				self._initFields.forEach(function (key) {
					if (angular.isObject(stats[key])) {
						if (Object.keys(stats[key]).length > 0) {
							event[key] = stats[key]
							// self._parseNumbers(event[key])
						}
					} else {
						event[key] = parseFloat(stats[key]);
					}
				});

				return onLoadingFinished(event, stats)
			})
		})
	}, this._$q.resolve())
}

SWStatisticsService.prototype.initEmptyTotals = function () {
	return {
		teams 	: {
			stripe 	 : {
                taxed_from_sw_real: 0,
                taxed_from_sw: 0,
				taxed_from_eo: 0,
				delta: 0,
                delta_real: 0
			},
			sw_fee 	 : {
				target: 0,
				collected: 0,
                collected_limited: 0,
				balance: 0,
			},
			disputes : 0,
			transfers: 0,
			payments_qty: 0,
			collected_fee: 0
		},
		tickets : {
			stripe 	 : {
                taxed_from_sw_real: 0,
                taxed_from_sw: 0,
				taxed_from_eo: 0,
				delta: 0,
                delta_real: 0
			},
			sw_fee 	 : {
				target: 0,
				collected: 0,
                collected_limited: 0,
				balance: 0
			},
			disputes : 0,
			transfers: 0,
			payments_qty: 0,
			collected_fee: 0
		},
		booths 	: {
			stripe 	 : {
                taxed_from_sw_real: 0,
                taxed_from_sw: 0,
				taxed_from_eo: 0,
				delta: 0,
                delta_real: 0
			},
			sw_fee 	 : {
				target: 0,
				balance: 0,
                ua_delta: 0,
                sw_delta: 0
			},
			disputes : 0,
			transfers: 0,
			payments_qty: 0,
			collected_fee: 0
		},
		income: 0
	}
}

SWStatisticsService.prototype.countEventsTotals = function (eventsList, type, totals) {
	var self = this;

	var totalCollectedFee 	= 0,

		totalStripeSW 		= 0,
        totalStripeSWReal   = 0,
		totalStripeEO 		= 0,
		totalStripeDelta 	= 0,
        totalStripeDeltaReal= 0,

		totalPaymentsQty 	= 0,

		totalSWFeeTarget 	= 0,
		totalSWFeeCollected = 0,
		totalSWFeeBalance 	= 0,

		totalDisputes 		= 0,
		totalTypeTransfers 	= 0,
		totalTypeIncome 	= 0,

		totalIncome 		= 0,

        totalSWFeeUADelta   = 0,
        totalSWFeeSWDelta   = 0;

	eventsList.forEach(function (event) {
		if (!event.is_loaded) {
			return;
		}

		var eventTypeData = event[type];

		totalCollectedFee 	+= parseFloat(eventTypeData.collected_fee);

		totalStripeSWReal 	+= parseFloat(eventTypeData.stripe.taxed_from_sw_real);
        totalStripeSW       += parseFloat(eventTypeData.stripe.taxed_from_sw);
		totalStripeEO 		+= parseFloat(eventTypeData.stripe.taxed_from_eo);
		totalStripeDelta 	+= parseFloat(eventTypeData.stripe.delta);
        totalStripeDeltaReal+= parseFloat(eventTypeData.stripe.delta_real);

		totalPaymentsQty 	+= parseFloat(eventTypeData.payments_qty);

		totalSWFeeTarget 	+= parseFloat(eventTypeData.sw_fee.target);
		totalSWFeeBalance 	+= parseFloat(eventTypeData.sw_fee.balance);

		totalDisputes 		+= parseFloat(eventTypeData.disputes);
		totalTypeTransfers  += parseFloat(eventTypeData.transfers);

		eventTypeData.income = this._getIncome(eventTypeData, type);

		totalTypeIncome 	+= eventTypeData.income;

		if(type === this.BOOTHS) {
            totalSWFeeCollected += parseFloat(eventTypeData.sw_fee.collected);
        } else {
            totalSWFeeCollected += parseFloat(eventTypeData.sw_fee.collected_limited);
        }

		if(type === this.TICKETS) {
            totalSWFeeUADelta   += parseFloat(eventTypeData.sw_fee.ua_delta);
            totalSWFeeSWDelta   += parseFloat(eventTypeData.sw_fee.sw_delta);
        }

		event.income 		= this._allTypes.reduce(function (sum, type) {
			return self._utils.approxNumber(sum + self._getIncome(event[type], type));
		}, 0);

		totalIncome 		 += event.income;
	}, this);

	var typeTotals = totals[type];

	typeTotals.collected_fee 		 	 = this._utils.approxNumber(totalCollectedFee);
	typeTotals.stripe.taxed_from_sw_real = this._utils.approxNumber(totalStripeSWReal);
    typeTotals.stripe.taxed_from_sw      = this._utils.approxNumber(totalStripeSW);
	typeTotals.stripe.taxed_from_eo  	 = this._utils.approxNumber(totalStripeEO);
	typeTotals.stripe.delta 		 	 = this._utils.approxNumber(totalStripeDelta);
    typeTotals.stripe.delta_real 		 = this._utils.approxNumber(totalStripeDeltaReal);
	typeTotals.payments_qty 		 	 = this._utils.approxNumber(totalPaymentsQty);
	typeTotals.sw_fee.target 		 	 = this._utils.approxNumber(totalSWFeeTarget);
	typeTotals.sw_fee.balance 			 = this._utils.approxNumber(totalSWFeeBalance);
	typeTotals.disputes 				 = this._utils.approxNumber(totalDisputes);
	typeTotals.transfers 				 = this._utils.approxNumber(totalTypeTransfers);
	typeTotals.income 					 = this._utils.approxNumber(totalTypeIncome);
	totals.income 						 = this._utils.approxNumber(totalIncome);


    if(type === this.BOOTHS) {
        typeTotals.sw_fee.collected 		= this._utils.approxNumber(totalSWFeeCollected);
    } else {
        typeTotals.sw_fee.collected_limited = this._utils.approxNumber(totalSWFeeCollected);
    }

	if(type === this.TICKETS) {
        typeTotals.sw_fee.ua_delta = this._utils.approxNumber(totalSWFeeUADelta);
        typeTotals.sw_fee.sw_delta = this._utils.approxNumber(totalSWFeeSWDelta);
    }
}

SWStatisticsService.prototype._getIncome = function (data, type) {
	var teamsSWFee = 0;

	if (type === this.TEAMS) {
		/* Marc does not make a profit from teams' reg. SW fee, so we need to substract collected sw fee */
		teamsSWFee = parseFloat(data.sw_fee.collected);
	}

	/* all the disputes are below zero, that is why we add them */
	var result = data.collected_fee - data.stripe.taxed_from_sw_real + data.disputes + data.transfers - teamsSWFee;
	
	return this._utils.approxNumber(result);
}

SWStatisticsService.prototype.getSWStripeAccStatistics = function (params) {
    return this._$http.get(this._baseURL + 'stripe', { params }).then(res => res.data);
}
