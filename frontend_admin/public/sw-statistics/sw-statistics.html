<h4 class="text-center">Payments Statistics</h4>

<div class="row row-space">
	<div class="col-sm-offset-4 col-sm-10">
		<sw-range-picker on-range-changed="$ctrl.rangeChanged(from, to)"></sw-range-picker>
	</div>
</div>

<div class="row">
	<div class="col-sm-offset-4 col-sm-4 well">
		<sw-type-picker on-pick="$ctrl.typeChanged(type)"></sw-type-picker>
	</div>
</div>

<table class="table table-condensed sw-statistics" sticky-header>
	<thead>
		<tr>
			<th rowspan="2" class="col-event-name">
				<a href="" ng-click="$ctrl.applyOrder('name')">Event Name</a>
				<reverse-arrow reverse="$ctrl.listOrder.reverse" show="{{$ctrl.listOrder.col == 'name'}}"></reverse-arrow>
			</th>
			<th rowspan="2">
				<a href="" ng-click="$ctrl.applyOrder('date_start')">Date Start</a>
				<reverse-arrow reverse="$ctrl.listOrder.reverse" show="{{$ctrl.listOrder.col == 'date_start'}}"></reverse-arrow>
			</th>
			<th rowspan="2" class="text-right">Net Profit</th>
			<th colspan="5" class="text-center">Stripe Fee</th>
			<th rowspan="2" class="text-right">
				Qty
			</th>
			<th colspan="{{$ctrl.isTicketsStatistic() ? 5 : 3}}" class="text-center">SW Fee</th>
			<th rowspan="2" class="text-right">Disputes</th>
			<th rowspan="2" class="text-right">Rest</th>
			<th rowspan="2" class="text-right">{{$ctrl.getTypeName()}} Income</th>
			<th rowspan="2" class="text-right">Income</th>
		</tr>
		<tr>
			<!-- Stripe -->
			<th>
                UA
			</th>
            <th>
                &#916;UA
            </th>
            <th>
                SW
            </th>
            <th>
                &#916;SW
            </th>
			<th>
				EO
			</th>
			<!-- SW Fee -->
			<th>
				Target
			</th>
			<th>
				Collected
			</th>
            <th ng-if="$ctrl.isTicketsStatistic()">
                &#916;UA
            </th>
            <th ng-if="$ctrl.isTicketsStatistic()">
                &#916;SW
            </th>
			<th>
				Balance
			</th>
		</tr>
	</thead>
	<tbody>
        <!-- === TOTALS === -->
        <tr class="bg-warning">
            <td colspan="2" class="font-bold not-right">Totals:</td>

            <td ng-bind="$ctrl.getCollectedFee($ctrl.totals)"></td>
            <td ng-bind="$ctrl.getStripeSWPaidReal($ctrl.totals)"></td>
            <td ng-bind="$ctrl.getStripeFeeDeltaReal($ctrl.totals)"></td>
            <td ng-bind="$ctrl.getStripeSWPaid($ctrl.totals)"></td>
            <td ng-bind="$ctrl.getStripeFeeDelta($ctrl.totals)"></td>
            <td ng-bind="$ctrl.getStripeEOPaid($ctrl.totals)"></td>
            <td ng-bind="$ctrl.getPaymentsQty($ctrl.totals)"></td>
            <td ng-bind="$ctrl.getSWFeeTarget($ctrl.totals)"></td>
            <td ng-bind="$ctrl.getSWFeeCollected($ctrl.totals)"></td>
            <td ng-if="$ctrl.isTicketsStatistic()" ng-bind="$ctrl.getSWFeeUADelta($ctrl.totals)"></td>
            <td ng-if="$ctrl.isTicketsStatistic()" ng-bind="$ctrl.getSWFeeSWDelta($ctrl.totals)"></td>
            <td ng-bind="$ctrl.getSWFeeBalance($ctrl.totals)" ng-class="{'text-danger': $ctrl.balanceIsBelowZero($ctrl.totals)}"></td>
            <td ng-bind="$ctrl.getDisputes($ctrl.totals)"></td>
            <td ng-bind="$ctrl.getTransfers($ctrl.totals)"></td>
            <td ng-bind="$ctrl.getTypeIncome($ctrl.totals)"></td>

            <td ng-bind="$ctrl.getIncome($ctrl.totals)" class="bg-success font-bold"></td>
        </tr>
		<tr ng-repeat-start="event in $ctrl.events" ng-if="!event.is_loaded">
			<td class="not-right">
				<a ng-href="{{event.link}}" target="_blank" rel="nofollow noopener" ng-bind="event.name"></a>
			</td>
			<td class="not-right" ng-bind="event.date_start | UTCdate:'DD/MM/YYYY'"></td>

			<td colspan="11">
				<spinner active="true" ng-if="$ctrl.statsProgress[event.event_id]"></spinner>
			</td>

			<td class="bg-success"></td>
		</tr>
		<tr ng-repeat-end ng-if="!$ctrl.statsProgress[event.event_id] && event.is_loaded">
			<td class="not-right">
				<a ng-href="{{event.link}}" target="_blank" rel="nofollow noopener" ng-bind="event.name"></a>
			</td>
			<td class="not-right" ng-bind="event.date_start | UTCdate:'DD/MM/YYYY'"></td>

			<td ng-bind="$ctrl.getCollectedFee(event)" class="bold-text"></td>
			<td ng-bind="$ctrl.getStripeSWPaidReal(event)"></td>
            <td ng-bind="$ctrl.getStripeFeeDeltaReal(event)" class="bold-text"></td>
            <td ng-bind="$ctrl.getStripeSWPaid(event)"></td>
            <td ng-bind="$ctrl.getStripeFeeDelta(event)" class="bold-text"></td>
			<td ng-bind="$ctrl.getStripeEOPaid(event)"></td>
			<td ng-bind="$ctrl.getPaymentsQty(event)"></td>
			<td ng-bind="$ctrl.getSWFeeTarget(event)"></td>
			<td ng-bind="$ctrl.getSWFeeCollected(event)"></td>
            <td ng-if="$ctrl.isTicketsStatistic()" ng-bind="$ctrl.getSWFeeUADelta(event)"></td>
            <td ng-if="$ctrl.isTicketsStatistic()" ng-bind="$ctrl.getSWFeeSWDelta(event)"></td>
            <td ng-bind="$ctrl.getSWFeeBalance(event)" ng-class="{'text-danger': $ctrl.balanceIsBelowZero(event)}"></td>
			<td ng-bind="$ctrl.getDisputes(event)"></td>
			<td ng-bind="$ctrl.getTransfers(event)"></td>
			<td ng-bind="$ctrl.getTypeIncome(event)"></td>

			<td ng-bind="$ctrl.getIncome(event)" class="bg-success font-bold"></td>
		</tr>

		<!-- === TOTALS === -->
		<tr class="bg-warning">
			<td colspan="2" class="font-bold not-right">Totals:</td>

			<td ng-bind="$ctrl.getCollectedFee($ctrl.totals)"></td>
			<td ng-bind="$ctrl.getStripeSWPaidReal($ctrl.totals)"></td>
			<td ng-bind="$ctrl.getStripeFeeDeltaReal($ctrl.totals)"></td>
            <td ng-bind="$ctrl.getStripeSWPaid($ctrl.totals)"></td>
            <td ng-bind="$ctrl.getStripeFeeDelta($ctrl.totals)"></td>
            <td ng-bind="$ctrl.getStripeEOPaid($ctrl.totals)"></td>
			<td ng-bind="$ctrl.getPaymentsQty($ctrl.totals)"></td>
			<td ng-bind="$ctrl.getSWFeeTarget($ctrl.totals)"></td>
			<td ng-bind="$ctrl.getSWFeeCollected($ctrl.totals)"></td>
            <td ng-if="$ctrl.isTicketsStatistic()" ng-bind="$ctrl.getSWFeeUADelta($ctrl.totals)"></td>
            <td ng-if="$ctrl.isTicketsStatistic()" ng-bind="$ctrl.getSWFeeSWDelta($ctrl.totals)"></td>
            <td ng-bind="$ctrl.getSWFeeBalance($ctrl.totals)" ng-class="{'text-danger': $ctrl.balanceIsBelowZero($ctrl.totals)}"></td>
			<td ng-bind="$ctrl.getDisputes($ctrl.totals)"></td>
			<td ng-bind="$ctrl.getTransfers($ctrl.totals)"></td>
			<td ng-bind="$ctrl.getTypeIncome($ctrl.totals)"></td>

			<td ng-bind="$ctrl.getIncome($ctrl.totals)" class="bg-success font-bold"></td>
		</tr>
	</tbody>
</table>
