angular.module('SportWrench').component('swStatistics', {
	templateUrl : 'public/sw-statistics/sw-statistics.html',
	controller 	: ['SWStatisticsService', '$q', 'UtilsService', '$filter', SWStatisticsController]
});

function initEventsList (events) {
	return events.map(function (event) {
		
		event.income = 0;
		event.is_loaded = false;
		
		return event;
	});
}

function SWStatisticsController (SWStatisticsService, $q, UtilsService, $filter) {
	
	var orderBy 			= $filter('orderBy');
	var currencyFilter		= $filter('currency');
	var self 				= this;
	var initialEventsList 	= [];
	var typeName 			= {
		teams 		: 'Teams',
		tickets 	: 'Tickets',
		booths 		: 'Booths'
	};
	var statsRange = {};

	this.events 		= [];
	this.statsProgress 	= {};
	this.totals 		= SWStatisticsService.initEmptyTotals();
	this.listOrder 		= {
		reverse 	: false,
		col 		: 'date_start'
	};

	var loadEventsList = function () {
		SWStatisticsService.getEventsList(statsRange).then(function (events) {
			initialEventsList 	= initEventsList(events);
			this.events 		= this.getOrderedEvents(initialEventsList);	

			loadStatistics(this.events);
		}.bind(this));
	}.bind(this);

	function loadStatistics (eventsList) {
		return SWStatisticsService.loadStatsForEventsList(eventsList, statsRange, function (eventID) {
			self.statsProgress[eventID] = true;
		}, function (event) {

			self.statsProgress[event.event_id] 	= false;
			event.is_loaded 					= true;

			self.recountTotalIncome();
			self.events = self.getOrderedEvents(initialEventsList);
		})
	}

	this.recountTotalIncome = function () {
		SWStatisticsService.countEventsTotals(this.events, this.statisticsType, this.totals);
	}

	this.tdClass = function (amount) {

		amount = parseFloat(amount, 10);

		return {
			'text-danger' 	: (amount < 0),
			'text-grey' 	: (amount === 0)
		}
	}

	this.applyOrder = function (column) {
		if (column === this.listOrder.col) {
			this.listOrder.reverse = !this.listOrder.reverse;
		} else {
			this.listOrder.col = column;
		}

		this.events = this.getOrderedEvents(initialEventsList);
	}

	this.typeChanged = function (type) {
		this.statisticsType = type;
		this.recountTotalIncome();
	}

	this.isTicketsStatistic = function () {
        return this.statisticsType === 'tickets';
    }

	this.rangeChanged = function (from, to) {
		statsRange.from = from;
		statsRange.to = to;
		loadEventsList();
	}

	this.getOrderedEvents = function (eventsList) {
		return orderBy(eventsList, this.listOrder.col, this.listOrder.reverse);
	}

	this.getTypeName = function () {
		return typeName[this.statisticsType];
	}

	/* === */

	this.getCollectedFee = function (event) {
		return currencyFilter(event[this.statisticsType].collected_fee || 0, '');
	}

	this.getStripeSWPaidReal = function (event) {
		return currencyFilter(event[this.statisticsType].stripe.taxed_from_sw_real || 0, '');
	}

    this.getStripeSWPaid = function (event) {
        return currencyFilter(event[this.statisticsType].stripe.taxed_from_sw || 0, '');
    }

	this.getStripeEOPaid = function (event) {
		return currencyFilter(event[this.statisticsType].stripe.taxed_from_eo || 0, '');
	}

	this.getStripeFeeDelta = function (event) {
		return currencyFilter(event[this.statisticsType].stripe.delta || 0, '');
	}

    this.getStripeFeeDeltaReal = function (event) {
        return currencyFilter(event[this.statisticsType].stripe.delta_real || 0, '');
    }

	this.getPaymentsQty = function (event) {
		return event[this.statisticsType].payments_qty || 0;
	}

	this.getSWFeeTarget = function (event) {
		return currencyFilter(event[this.statisticsType].sw_fee.target || 0, '')
	}

	this.getSWFeeCollected = function (event) {
	    let fieldName = 'collected_limited';

	    if(this.statisticsType === 'booths') {
	        fieldName = "collected";
        }

        return currencyFilter(event[this.statisticsType].sw_fee[fieldName] || 0, '')
	}

	this.getSWFeeBalance = function (event) {
		return currencyFilter(event[this.statisticsType].sw_fee.balance || 0, '')
	}

	this.getDisputes = function (event) {
		return currencyFilter(event[this.statisticsType].disputes || 0, '')
	}

	this.getTransfers = function (event) {
		return currencyFilter(event[this.statisticsType].transfers || 0, '')
	}

	this.getTypeIncome = function (event) {
		return currencyFilter(event[this.statisticsType].income || 0, '')
	}

	this.getSWFeeUADelta = function (event) {
        return currencyFilter(event[this.statisticsType].sw_fee.ua_delta || 0, '')
    }

    this.getSWFeeSWDelta = function (event) {
        return currencyFilter(event[this.statisticsType].sw_fee.sw_delta || 0, '')
    }

	this.getIncome = function (event) {
		return currencyFilter(event.income || 0, '')
	}

	this.balanceIsBelowZero = function (event) {
        return (event[this.statisticsType].sw_fee.balance || 0) < 0;
    }
}
