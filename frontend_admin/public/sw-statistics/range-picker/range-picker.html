<form class="form-inline" ng-submit="$ctrl.submit()">
    <div class="form-group pointer" ng-click="$ctrl.toggleFrom()">
    	<input 
    		type="text" 
    		class="form-control" 
    		ng-model="$ctrl.dateFrom"
    		uib-datepicker-popup="MM/dd/yyyy"
    		is-open="$ctrl.isFromOpened" 
    		datepicker-options="$ctrl.fromPickerOpts" 
    		ng-required="true" 
    		close-text="Close" 
    	/>
    </div>
    <div class="form-group pointer" ng-click="$ctrl.toggleTo()">
    	<input 
    		type="text" 
    		class="form-control" 
    		ng-model="$ctrl.dateTo"
            uib-datepicker-popup="MM/dd/yyyy"
    		is-open="$ctrl.isToOpened" 
    		datepicker-options="$ctrl.toPickerOpts" 
    		ng-required="true" 
    		close-text="Close" 
    	/>
    </div>
    <button type="submit" class="btn btn-default">Apply</button>
</form>
