angular.module('SportWrench').component('swRangePicker', {
	templateUrl: 'public/sw-statistics/range-picker/range-picker.html',
	bindings: {
		onRangeChanged: '&'
	},
	controller: [SWRangePickerController]
});

function formatDate (date) {
	return date.getFullYear() + '-' + (date.getMonth() + 1) + '-' + date.getDate()
}

/**
* If current month is January - return previous year
**/
function getCurrentYear () {
	var currentDate 	= new Date();
	var currentYear 	= currentDate.getUTCFullYear();
	var currentMonth 	= currentDate.getUTCMonth();

	return (currentMonth >= 1) ? currentYear : (currentYear - 1)
}

function SWRangePickerController () {

	var currentYear = getCurrentYear();

	this.dateFrom 	= new Date(currentYear, 0, 1);
	this.dateTo 	= new Date(currentYear, 11, 31);

	this.fromPickerOpts = {
		initDate: this.dateFrom
	}

	this.toPickerOpts = {
		initDate: this.dateTo
	}

	this.isFromOpened = false;
	this.isToOpened = false;

	this.toggleFrom = function () {
		this.isFromOpened = !this.isFromOpened;
	}

	this.toggleTo = function () {
		this.isToOpened = !this.isToOpened;
	}

	this.submit = function () {
		var fromStr = formatDate(this.dateFrom);
		var toStr 	= formatDate(this.dateTo);

		this.onRangeChanged({ 
			from 	: fromStr,
			to 	 	: toStr
		});
	}

	this.$onInit = function () {
		this.submit();
	}
}
