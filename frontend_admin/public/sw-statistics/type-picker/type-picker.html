<form class="form-inline">
    <div class="form-group">
        <div class="radio-inline pointer">
            <label>
                <input type="radio" name="sw-stats-type" ng-model="$ctrl.statsType" ng-change="$ctrl.onPick({ type: $ctrl.statsType })" value="teams"> Teams
            </label>
        </div>
        <div class="radio-inline pointer">
            <label>
                <input type="radio" name="sw-stats-type" ng-model="$ctrl.statsType" ng-change="$ctrl.onPick({ type: $ctrl.statsType })" value="tickets"> Tickets
            </label>
        </div>
        <div class="radio-inline pointer">
            <label>
                <input type="radio" name="sw-stats-type" ng-model="$ctrl.statsType" ng-change="$ctrl.onPick({ type: $ctrl.statsType })" value="booths"> Booths
            </label>
        </div>
    </div>
</form>
