<div class="panel panel-default">
    <div class="panel-body">
        <h1 class="page-header">Data Integrity Tests</h1>
        <div class="row">
            <div class="col-md-12">
                <button class="btn btn-default" ng-click="runTests()">Run all tests</button>
            </div>
        </div>
        <br />
        <div class="row">
            <div class="col-md-12">
                <progress>
                    <bar ng-repeat="bar in bars track by $index"
                        value="bar.value" type="{{bar.type}}"
                        style="border-right: solid white 1px">
                        <a href="" ng-click="scroll(bar.test.id)" style="display:block;width:100%;height:100%"
                            tooltip="{{getTooltip(bar.test)}}"></a>
                    </bar>
                </progress>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div 
        ng-repeat="test in tests track by $index" 
        class="col-md-4 col-sm-6" 
        ng-class="[test.colSmClass, test.colMdClass]">
        <div class="panel panel-default test-item" id="test-{{test.id}}">
            <div class="panel-heading">
                <code>test #{{::test.id}}</code>
                <a href ng-click="runTest(test)">
                    <strong>{{test.status ? 'Re-run' : 'Run'}}</strong>
                </a>
                <strong class="pull-right"
                        ng-class="{'text-danger': test.status == 'Error', 'text-success': test.status == 'OK'}">
                    {{test.status}}
                    <a href class="badge" ng-show="test.rowCount" ng-click="showRows(test)">{{test.rowCount}} rows</a>
                    <span class="label label-success pointer" ng-if="showFixBtn(test)" ng-click="fixTest(test)">Fix</span>
                    <i class="fa fa-spinner fa-pulse" ng-if="test.isFixing"></i>
                </strong>
            </div>
            <div class="panel-body">
                <p><strong>{{::test.title}}</strong></p>
                <p>{{::test.description}}</p>                
                <div ng-if="test.showRows">
                    <table class="table">
                        <tr>
                            <th ng-repeat="col in test.resultCols">{{col}}</th>
                        </tr>
                        <tr ng-repeat="row in test.resultRows">
                            <td ng-repeat="col in test.resultCols">{{row[col] === null ? 'NULL' : row[col]}}</td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

