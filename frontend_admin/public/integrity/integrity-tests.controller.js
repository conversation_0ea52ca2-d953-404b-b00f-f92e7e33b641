angular.module('SportWrench').controller('IntegrityTestsController', IntegrityTestsController);

IntegrityTestsController.$inject = ['$scope', '$http', '$uibModal', '$anchorScroll', '$location'];

function IntegrityTestsController($scope, $http, $uibModal, $anchorScroll, $location) {
    $scope.rowsView = false;

    $scope.tests = [];
    $scope.bars = [];

    $http.get('/api/admin/integrity/tests')
    .success(function(data) {
        $scope.tests = data;
    });

    $scope.scroll = function (id) {
        var prev = $location.hash();
        $location.hash('test-' + id);
        $anchorScroll();
        $location.hash(prev);
    }

    $scope.runTest = function(test) {
        test.status = 'Running';
        $http.get('/api/admin/integrity/test/'+test.id+'/count')
        .success(function(data) {
            test.rowCount = Number(data.count);
            test.status = (test.rowCount !== 0)
                ? 'Error'
                : 'OK';
        })
        .error(function(data) {
            test.status = 'Server Error';
        });
    };

    $scope.runTests = function() {
        function run(i, cb) {
            if (i >= $scope.tests.length)
                return;

            var test = $scope.tests[i];

            test.status = 'Running';
            $http.get('/api/admin/integrity/test/'+test.id+'/count')
            .success(function(data) {

                test.rowCount = Number(data.count);
                test.status = (test.rowCount !== 0)
                    ? 'Error'
                    : 'OK';

                $scope.bars.push({
                    test: test,
                    value: 100 / $scope.tests.length,
                    type: test.status === 'Error'
                        ? 'danger'
                        : 'success'
                });

                run(i+1, run);
            })
            .error(function(data) {
                test.status = 'Server Error';

                $scope.bars.push({
                    test: test,
                    value: 100 / $scope.tests.length,
                    type: 'empty'
                });

                run(i+1, run);
            });
        }

        run(0, run);
    };

    $scope.showRows = function(test) {
        test.resultRows = test.resultRows || [];
        test.resultCols = test.resultCols || [];

        test.showRows = !test.showRows;
        test.colSmClass = test.showRows
            ? 'col-sm-12'
            : 'col-sm-6';
        test.colMdClass = test.showRows
            ? 'col-md-12'
            : 'col-md-4';

        if (!test.showRows || test.resultRows.length !== 0) return;

        $http.get('/api/admin/integrity/test/'+test.id+'/rows')
        .success(function (data) {
            if(data && data.length) {
                test.resultRows = data;
                test.resultCols = Object.keys(data[0]);
            } else {
                test.status = 'OK';
                test.rowCount = 0
            }            
        });
    };

    $scope.fixTest = function (test) {
        if(!test.fix) return;
        test.isFixing = true;
        $http.get('/api/admin/integrity/test/' + test.id + '/fix')
        .finally(function () {
            test.isFixing = false;
        });
    }

    $scope.getTooltip = function(test) { 
        var i = test.id;
        var s = test.status;
        var e = test.rowCount;
        var t = test.title;

        var tooltip = '#' + i + ' ' + t;
        if (s === 'Server Error') tooltip += ' (Server Error)';
        if (s === 'OK') tooltip += ' ' + ' (OK)';
        if (s === 'Error') tooltip += ' ('+ e + ' errors)';

        return tooltip;
    };

    $scope.showFixBtn = function (test) {
        return test.rowCount && (test.status !== 'OK') && test.fix && !test.isFixing
    }
}
