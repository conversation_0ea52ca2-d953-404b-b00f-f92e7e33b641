angular.module('SportWrench').service('StripeTransfersService', StripeTransfersService);

StripeTransfersService.$inject = ['$http'];

function StripeTransfersService ($http) {
	this.$http 		= $http;
	this.urlPrefix 	= '/api/admin/payments/stripe/'
}

StripeTransfersService.prototype.getManagedAccounts = function () {
	return this.$http.get(this.urlPrefix + 'connected-accounts')
}

StripeTransfersService.prototype.recipientsMigrate = function () {
	return this.$http.get(this.urlPrefix + 'migrate-to-managed')
}

StripeTransfersService.prototype.createManagedAccount = function (data) {
	return this.$http.post(this.urlPrefix + 'create-managed', { account: data })
}

StripeTransfersService.prototype.createTransfer = function (data) {
	return this.$http.post(this.urlPrefix + 'transfer-amount', data)
}

StripeTransfersService.prototype.saveCheckTransfer = function (data) {
	return this.$http.post(this.urlPrefix + 'save-check', data)
}

StripeTransfersService.prototype.eventsList = function (stripeAccountId) {
	return this.$http.get(this.urlPrefix + stripeAccountId + '/events-list');
}

StripeTransfersService.prototype.eventStatistics = function (eventId) {
	return this.$http.get(this.urlPrefix + 'event/' + eventId + '/stats')
}

StripeTransfersService.prototype.loadAccountHistory = function (id) {
	return this.$http.get(this.urlPrefix + 'account/' + id + '/history')
}

StripeTransfersService.prototype.uploadDocs = function (id, file) {
	var fd = new FormData();
	fd.append('docs-scan', file);
	return this.$http.post(this.urlPrefix + 'account/' + id + '/docs', fd, {
		withCredentials 	: true,
        headers 			: { 'Content-Type': undefined },
        transformRequest 	: angular.identity
	})
}

StripeTransfersService.prototype.removeAccount = function (id) {
	return this.$http.delete(this.urlPrefix + 'account/' + id)
}

StripeTransfersService.prototype.swAccBalance = function () {
	return this.$http.get(this.urlPrefix + 'account/sw-acc/balance')
}
