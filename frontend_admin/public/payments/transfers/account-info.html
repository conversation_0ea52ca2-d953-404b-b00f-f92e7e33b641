<tabset>
	<tab heading="General" active="settings.infoActive">
		<div class="well row-space">
			<dl class="dl-horizontal">
				<dt>Account Type:</dt>
				<dd>{{account.managed?'Managed':'Standalone'}}</dd>
				<dt>Charges Enabled:</dt>
				<dd>
					<i class="fa fa-check text-success" ng-if="account.charges_enabled"></i>
					<i class="fa fa-times text-danger" ng-if="!account.charges_enabled"></i>
				</dd>
				<dt>Email:</dt>
				<dd ng-bind="alias(account.email)" 
					  ng-class="{ 'text-grey': (!account.email) }"></dd>
				<dt>Display Name:</dt>
				<dd ng-bind="alias(account.display_name)" 
				  	  ng-class="{ 'text-grey': (!account.display_name) }"></dd>
				<dt>Transfers Enabled:</dt>
				<dd>
				  	<i class="fa fa-check text-success" ng-if="account.transfers_enabled"></i>
				  	<i class="fa fa-times text-danger" ng-if="!account.transfers_enabled"></i>
				</dd>
				<dt>Business Name:</dt>
				<dd ng-bind="alias(account.business_name)" 
				  	  ng-class="{ 'text-grey': (!account.business_name) }"></dd>
				<dt>Person Name:</dt>
				<dd ng-bind="alias(account.person_name)" 
				  	  ng-class="{ 'text-grey': (!account.person_name) }"></dd>
				<dt>Type:</dt>
				<dd ng-bind="account.type"></dd>
				<dt>Verification:</dt>
				<dd ng-bind="account.verification.status"
				 	  ng-class="{ 'text-success': (account.verification.status === 'verified'), 'text-danger': (account.verification.status === 'unverified') }"></dd>
				<dt>Bank Accounts:</dt>
				<dd ng-bind="alias(account.bank_accounts_last4)"
					ng-class="{ 'text-grey': (!account.bank_accounts_last4) }"></dd>
			</dl>
		</div>
	</tab>
	<tab heading="History" active="settings.historyActive" select="loadHistory()">
		<table class="table table-condensed" ng-show="!loading">
			<thead>
				<tr>
					<th>Type</th>
					<th>Created</th>
					<th>Amount</th>
					<th>Event</th>
					<th>Description</th>
				</tr>
			</thead>
			<tbody>
				<tr ng-repeat="tr in data.transfers">
					<td>
						<i ng-class="{ 'fa': true, 'fa-cc-stripe text-info': tr.type === 'stripe', 'fa-university': tr.type === 'check' }"></i>
					</td>
					<td ng-bind="tr.created"></td>
					<td ng-bind="tr.amount | currency: '$'"></td>
					<td ng-bind="tr.event_name"></td>
					<td ng-bind="tr.description"></td>
				</tr>
				<tr ng-if="data.transfers.length === 0">
					<td colspan="4" class="text-center">No transfers yet.</td>
				</tr>
			</tbody>
		</table>
		<spinner active="loading"></spinner>
	</tab>
</tabset>
