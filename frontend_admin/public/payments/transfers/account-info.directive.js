angular.module('SportWrench').directive('managedAccountInfo', managedAccountInfo)

managedAccountInfo.$inject = ['StripeTransfersService'];

function managedAccountInfo (StripeTransfersService) {
	return {
		restrict: 'E',
		scope: {
			account: '='
		},
		templateUrl: 'public/payments/transfers/account-info.html',
		link: function (scope) {
			scope.data = {
				transfers: []
			}

			scope.settings = {
				infoActive 		: true,
				historyActive 	: false
			}

			scope.$watch('account.id', function () {
				scope.settings.infoActive = true;
				scope.settings.historyActive = false;
			})

			scope.alias = function (value) {
				return value || 'N/A'
			}

			scope.loadHistory = function () {
				scope.loading = true;
				StripeTransfersService.loadAccountHistory(scope.account.id)
				.success(function (data) {
					scope.data.transfers = data.transfers;
				})
				.finally(function () {
					scope.loading = false;
				})
			}
		}
	}
}
