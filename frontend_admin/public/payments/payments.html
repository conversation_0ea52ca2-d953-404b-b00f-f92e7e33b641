<div class="panel panel-default">
    <div class="panel-body">
        <p class="lead">Payment refunds</p>
        <form class="form form-inline">
            <div class="form-group">
                <label>Event:</label>
                <select 
                    class="form-control"
                    ng-model="menu.event" 
                    ng-options="e.id as e.name for e in events" ng-disabled="menu.in_progress"
                    >
                    <option value="">All</option>                  
                </select>
            </div>
            <div class="form-group">
                <label>Type: </label>
                <select class="form-control" ng-model="menu.payment_type" ng-disabled="menu.in_progress">
                    <option value="">All</option>          
                    <option value="check">Check</option>    
                    <option value="card">Card</option>            
                </select>
            </div>
            <button class="btn btn-default" ng-click="load();" ng-disabled="menu.in_progress">Load payments</button>
            <button class="btn btn-primary" ng-click="find_refunded();" ng-if="!menu.in_progress" ng-class="{'selected-row': true, 'show-row': menu.payments_loaded}">Find refunded</button>
            <button class="btn btn-danger" ng-click="stop();" ng-if="menu.in_progress">Stop</button>
        </form>
        <div class="row row-space" ng-class="{'selected-row': true, 'show-row': menu.in_progress}">
            <div class="col-sm-6">
                <progressbar max="menu.progress_bar.max" value="menu.progress_bar.value" type="'info'" animate="menu.progress_bar.animate" class="progress-striped active">
                    <span class="text-center">{{menu.progress_bar.action}}</span>
                </progressbar>
            </div>
        </div>
    </div>

    <table class="table table-condensed" ng-class="{'selected-row': true, 'show-row': menu.payments_loaded}">
        <thead>
            <tr>
                <th>Payment Id</th>
                <th>Created</th>
                <th>Type</th>
                <th>Amount</th>
                <th>Refunded</th>
                <th>Refund</th>
            </tr>
        </thead>
        <tbody>
            <tr ng-repeat="p in payments">
                <td>{{p.id}}</td>
                <td>{{p.created | date: 'MM/dd/yyyy'}}</td>
                <td>{{p.type}}</td>
                <td class="text-right">{{p.amount | currency:'$':2}}</td>
                <td class="text-right">{{p.amount_refunded | currency:'$':2}}</td>
                <td>
                    <refund-data 
                        ng-if="p.type === 'card'"
                        amount="{{p.amount_refunded}}"
                        date="{{p.date_refunded}}"
                    ></refund-data>
                </td>
            </tr>
        </tbody>
    </table>
</div>


