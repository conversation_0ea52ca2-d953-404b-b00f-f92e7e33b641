<form class="form-inline">
  <div class="form-group">
    <label class="sr-only">Events:</label>
    <select
        class="form-control" 
        ng-model="data.selected_event" 
        ng-options="e.id as e.name for e in data.events"
    >                
    </select>
  </div>  
  <button 
    class="btn btn-default" 
    ng-click="load_payments()"
    ng-disabled="!data.selected_event">Load Card Payments</button>

   <button
    class="btn btn-primary" 
    ng-click="run_check()"
    ng-disabled="data.payments.length === 0"
    ng-if="!data.checking_running"
   >Check All</button>

    <button
    class="btn btn-danger" 
    ng-click="stop_queries()"    
    ng-if="data.checking_running"
   >Stop checking</button>
</form>

<table class="table table-condensed">
    <thead>
       <tr>
            <th class="text-center">Created</th>
            <th class="col-sm-1 text-center">Amount</th>
            <th class="col-sm-1 text-center">Refunded</th>
            <th class="text-center">Errors</th>
        </tr> 
    </thead>
    <tbody>
        <tr 
            ng-repeat="p in data.payments" 
            ng-class="{'alert-success': (!p.errors && p.checked), 'alert-danger': (p.errors && p.checked)}">
            <td class="text-center">{{p.created | date: 'MM/dd/yyyy'}}</td>
            <td class="col-sm-1 text-right">{{p.amount | currency:'$':2}}</td>
            <td class="col-sm-1 text-right">{{p.amount_refunded | currency:'$':2}}</td>
            <td class="text-center">
                <buttton 
                    class="btn btn-xs btn-danger" 
                    ng-click="open_errors_modal(p)"
                    ng-if="p.errors">Errors list</buttton>
            </td>
        </tr>
    </tbody>    
</table>
