<form class="form-inline">
  <div class="form-group">
    <label class="sr-only">Events:</label>
    <select 
        class="form-control" 
        ng-model="data.selected_event" 
        ng-options="e.id as e.name for e in data.events"
    >                
    </select>
  </div>  
  <button 
    class="btn btn-default" 
    ng-click="load_teams()"
    ng-disabled="!data.selected_event">Load teams</button>
</form>

<table class="table table-condensed">
    <thead>
        <tr>
            <th>Team Name</th>
            <th>Code</th>
            <th>Division</th>
            <th>Club Name</th>
            <th>Paid status</th>
        </tr>
    </thead>
    <tbody>
        <tr 
            ng-repeat="t in data.teams" 
            ng-click="showCheckingMenu(t)"
            class="pointer">
            <td>{{t.team_name}}</td>
            <td>{{t.organization_code}}</td>
            <td>{{t.division_name}}</td>
            <td>{{t.club_name}}</td>
            <td><status-paid status="{{t.status_paid}}"></status-paid></td>
        </tr>
    </tbody>
</table>
