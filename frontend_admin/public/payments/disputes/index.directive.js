angular.module('SportWrench').directive('stripeDisputes', stripeDisputes);

stripeDisputes.$inject = ['StripeDisputesService', 'toastr'];

function stripeDisputes (StripeDisputesService, toastr) {
	return {
		restrict 		: 'E',
		scope 			: {},
		templateUrl 	: 'public/payments/disputes/index.html',
		link: function (scope) {
			scope.ban = function () {
				StripeDisputesService.banLostDisputes()
				.then(function () {
					toastr.success('OK');
				});
			};
		}
	};
}
