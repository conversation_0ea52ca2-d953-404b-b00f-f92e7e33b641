<div class="modal-header">
<p class="lead">{{rt.team_name}}</p>
</div>
<div class="modal-body collapse-sm">
    <div ng-if="data.team_data">
        <p class="hrm0">Due (card): <span class="lead">${{data.team_data.due_card}}</span></p>
        <p class="hrm0">Due (check): <span class="lead">${{data.team_data.due_check}}</span></p>
        <p class="hrm0">Discount: <span class="lead">${{data.team_data.discount}}</span></p>
        <p class="hrm0">Status: <span class="lead"><status-paid status="{{data.team_data.status_paid}}"></status-paid></span></p>
    </div>
    <div 
        class="panel panel-default"
        ng-repeat="err in errors | orderBy:'type'"
        ng-if="errors.length > 0">
            <div 
                class="panel-heading" 
                ng-init="err.isShown = true" 
                ng-click="err.isShown = !err.isShown">
                <i 
                    ng-class="{'fa': true, 'fa-users': err.type === 'team', 'fa-money': err.type === 'payment', 'fa-university': err.type === 'stripe'}"></i>
                <a href="">{{err.message}}</a>
            </div>
            <div class="panel-body" collapse="err.isShown">
                <p ng-if="err.descr">{{err.descr}}</p>
                <p ng-if="err.hint"><b>Hint: </b><small>{{err.hint}}</small></p>
                <pre class="payments-json" ng-if="err.data">{{formatJson(err.data)}}</pre>
            </div>
    </div>
</div>
<div class="modal-footer">
    <button class="btn btn-default pull-right" ng-click="closeModal()">Close</button>
</div>
