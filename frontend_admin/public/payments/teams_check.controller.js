angular.module('SportWrench').controller('TeamPaymentCheckController', TeamPaymentCheckController);

TeamPaymentCheckController.$inject = ['$scope', '$http', '$uibModal'];

function TeamPaymentCheckController($scope, $http, $uibModal) {
    $scope.data = {
        events: $scope.$parent.events,
        teams: [],
        selected_event: 0
    };

    var _events_watch = $scope.$watch(function () {
        return $scope.$parent.events
    }, function (newVal, oldVal) {
        if(newVal !== oldVal) {
            $scope.data.events = newVal;
            _events_watch();
        }        
    });

    $scope.load_teams = function () {
        $http({
            method: 'GET',
            url: '/api/admin/payments/teams/all?event=' + $scope.data.selected_event
        }).success(function (data) {
            $scope.data.teams = data.teams;
        })
    }

    $scope.showCheckingMenu = function (roster_team) {
        $uibModal.open({
            templateUrl: 'public/payments/team_modal.html',
            controller: ['$scope', '$uibModalInstance', '$http', function ($scope, $uibModalInstance, $http) {
                $scope.data = {};
                $scope.errors = [];
                $scope.rt = roster_team;
                $http({
                    method: 'GET',
                    url: '/api/admin/team/' + roster_team.roster_team_id + '/payments/check'
                }).success(function (data) {                   
                    $scope.data = data.data;
                    if(data.errors) $scope.errors = data.errors;                                   
                });

                $scope.formatJson = function (json_obj) {
                    var _res;
                    try {
                        _res = JSON.stringify(json_obj, null, ' ');
                    } catch (e) {
                        _res = json_obj
                    } finally {
                        return _res;
                    }
                }

                $scope.closeModal = function () {
                    $uibModalInstance.close();
                }
            }]
        })
    }
}
