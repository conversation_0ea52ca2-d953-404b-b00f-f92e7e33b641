angular.module('SportWrench').controller(
    'RedisController',
    ['$scope', 'RedisService', '$filter', 'toastr', RedisController]
);
function RedisController($scope, RedisService, $filter, toastr) {
    $scope.keys = [];
    $scope.opts = {
        show_value_tab: false
    };
    $scope.selection = {};
    $scope.cache_time = [];

    $scope.load_value = function (key) {
        $scope.selection.key = key;
        RedisService
            .getKeyValue(key)
            .success(({ value }) => {
                // TODO: set opts.expires_time
                try {
                    $scope.selection.value = JSON.parse(value);
                    $scope.selection.value = $filter('json')($scope.selection.value);
                } catch (e) {
                    console.log('not json');
                    $scope.selection.value = value;
                }
                $scope.opts.show_value_tab = true;
            })
    };

    $scope.clearStorage = function (param) {
        RedisService.deleteKeys(param).success(({ removed }) => {
            if (param && (param === 'cache' || param === 'all')) {
                $scope.keys.length = 0;
                $scope.opts.show_value_tab = false;
            }
            toastr.success(`Removed ${removed} keys from Redis`);
        })
    };

    function _load () {
        RedisService.getAllKeys('cache')
            .success(({keys, cache_time}) => {
                $scope.keys = keys;
                const getObject = (key, name) => ({
                    value: cache_time[key],
                    key,
                    name,
                });
                $scope.cache_time = [
                    getObject('default', 'Default'),
                    getObject('home_page', 'Home Page'),
                    getObject('swt', 'SWT Api'),
                ];
            });
    }

    _load ();
}
