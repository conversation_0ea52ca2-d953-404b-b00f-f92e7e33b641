<div class="row users-list-search-box">
    <div class="col-sm-3">
        <sw-searchbox
            css="search_teams white-ro"
            reload="$ctrl.filterSearch()"
            input-model="$ctrl.search"
            placeholder="Search ..."
            is-readonly="$ctrl.usersTable.settings().$loading"
            reload-time="750"
        ></sw-searchbox>
    </div>
</div>
<div class="row">
    <div class="col-lg-12" loading-container="$ctrl.usersTable.settings().$loading">
        <table class="table ng-table sw-adaptive-grid table-condensed event-all-teams-table"
               ng-table="$ctrl.usersTable"
               sticky-header>
            <thead>
                <tr>
                    <th ng-class="$ctrl.columnClass('name')" ng-click="$ctrl.sort('name')">
                        <div class="sort-indicator">Name</div>
                    </th>
                    <th ng-class="$ctrl.columnClass('club_name')" ng-click="$ctrl.sort('club_name')">
                        <div class="sort-indicator">Club Name</div>
                    </th>
                    <th ng-class="$ctrl.columnClass('email')" ng-click="$ctrl.sort('email')">
                        <div class="sort-indicator">Email</div>
                    </th>
                    <th ng-class="$ctrl.columnClass('usav_code')" ng-click="$ctrl.sort('usav_code')">
                        <div class="sort-indicator">Club USAV Code</div>
                    </th>
                    <th ng-class="$ctrl.columnClass('created')" ng-click="$ctrl.sort('created')">
                        <div class="sort-indicator">Created</div>
                    </th>
                    <th ng-class="$ctrl.columnClass('region')" ng-click="$ctrl.sort('region')">
                        <div class="sort-indicator">Region</div>
                    </th>
                </tr>
            </thead>
            <tbody>
                <tr ng-repeat="u in $data track by $index" ng-class="{ 'alert-disabled': !u.activated }">
                    <td>
                        <a href=""
                           ng-click="$ctrl.loginAsUser($index)"
                           ng-bind="u.name"
                           ng-class="{'text-grey': !u.activated}"
                        ></a>
                    </td>
                    <td ng-bind="u.club_name"></td>
                    <td><a href="mailto:{{u.email}}" ng-bind="u.email"></a></td>
                    <td ng-bind="u.usav_code"></td>
                    <td ng-bind="u.created"></td>
                    <td ng-bind="u.region"></td>
                </tr>
            </tbody>
        </table>
    </div>
</div>
<pagination
    total="$ctrl.utils.totalRows"
    limit="$ctrl.filters.limit"
    page="$ctrl.filters.page"
    current="$ctrl.users.length"
    loading="$ctrl.dataIsLoading"
></pagination>
