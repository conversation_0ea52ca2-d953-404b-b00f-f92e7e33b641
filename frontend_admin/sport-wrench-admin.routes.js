angular.module('SportWrench').config(routesConfig);

routesConfig.$inject = ['$stateProvider', '$urlRouterProvider'];

function routesConfig($stateProvider, $urlRouterProvider) {

    $urlRouterProvider.otherwise('/');

    $stateProvider.state('dashboard', {
        url: '/',
        views: {
            'rootView': {
                templateUrl: 'public/dashboard/dashboard.html',
                controller: 'DashboardCtrl'
            }
        }
    });

    $stateProvider.state('integrity', {
        url: '/integrity',
        views: {
            'rootView': {
                templateUrl: 'public/integrity/integrity-tests.html',
                controller: 'IntegrityTestsController'
            }
        }
    });

    $stateProvider.state('payments', {
        abstract: true,
        url: '/payments',
        views: {
            'rootView': {
                template: '<ui-view/>',
                controller: 'PaymentsMenuController'  
            }
        }               
    });

    $stateProvider.state('payments.refunds', {
        url: '/refunds',        
        templateUrl: 'public/payments/payments.html',
        controller: 'PaymentsTestController'           
    });

    $stateProvider.state('payments.teams', {
        url: '/teams',        
        templateUrl: 'public/payments/teams_check.html',
        controller: 'TeamPaymentCheckController'
    });

    $stateProvider.state('payments.card', {
        url: '/card',        
        templateUrl: 'public/payments/card-payments.html',
        controller: 'CardPaymentsController'
    });

    $stateProvider.state('payments-transfers', {
        url: '/stripe-transfers',
        views: {
            'rootView': {
                template: '<stripe-transfers></<stripe-transfers>'
            }
        }
    })

    $stateProvider.state('payments-diputes', {
        url: '/stripe-disputes',
        views: {
            'rootView': {
                template: '<stripe-disputes></stripe-disputes>'
            }
        }
    })

    $stateProvider.state('redis', {
        url: '/redis',
        views: {
            'rootView': {
                templateUrl: 'public/redis/redis.html',
                controller: 'RedisController'
            }
        }
    });

    $stateProvider.state('event_teams', {
        url: '/teams',
        views: {
            'rootView': {
                templateUrl: 'public/event_teams/invalid_members_count.html',
                controller: 'InvalidMembersCountController'
            }
        }
    });

    $stateProvider.state('no_rank_teams', {
        url: '/empty/rank',
        views: {
            'rootView': {
                templateUrl: 'public/event_teams/no-rank.html',
                controller: 'NoRankTeamsController'
            }
        }
    });

    $stateProvider.state('db', {
        url: '/db',
        views: {
            'rootView': {
                templateUrl: 'public/db/db_actions.html',
                controller: 'DbActionsController'
            }
        }
    });

    $stateProvider.state('club', {
        abstract: true,
        url: '/club',
        views: {
            'rootView': {
                template: '<ui-view/>',
                controller: 'ClubController'  
            }
        }             
    });

    $stateProvider.state('club.athletes', {
        url: '/athletes',        
        templateUrl: 'public/club/athletes.html',
        controller: 'ClubAthletesController'
    });

    $stateProvider.state('usav', {
        abstract: 'true',
        url: '/usav',
        views: {
            'rootView': {
                template: '<ui-view/>',
                controller: 'USAV.IndexController'  
            }
        }
    });

    $stateProvider.state('usav.safesport', {
        url: '/safesport',
        template: '<safe-sport-import></safe-sport-import>'
    })

    $stateProvider.state('usav.wp-request', {
        url: '/wp-request',
        template: '<wp-api-checker></wp-api-checker>'
    })

    $stateProvider.state('usav.wp-club-request', {
        url: '/wp-club',
        template: '<wp-club></wp-club>'
    })

    $stateProvider.state('usav.wp-parser-run', {
        url: '/wp-parser-run',
        template: '<wp-parser></wp-parser>'
    })

    $stateProvider.state('events', {
        url: '/events',
        views: {
            'rootView': {
                templateUrl: 'public/event/events.html',
                controller: 'Events.EventsController',
                controllerAs: 'eventsRoot'
            }
        },
        resolve: {
            loadData: function ($q, EventsService, $timeout) {
                var defer = $q.defer();
                var timer = $timeout(function () {
                    defer.reject([]);
                }, 2000);
                EventsService.events().then(function (response) {
                    $timeout.cancel(timer);
                    defer.resolve(response.data.events);
                }, function () {
                    $timeout.cancel(timer);
                    defer.reject([]);
                })
                return defer.promise
            }
        }
    })

    $stateProvider.state('events.tickets', {
        url: '/tickets',
        views: {
            '': {
                templateUrl: 'public/event/tickets/tickets.html',
                controller: 'Events.TicketsController'
            }
        }
    });   

    $stateProvider.state('sw-statistics', {
        url: '/statistics',
        views: {
            'rootView': {
                template: '<sw-statistics></sw-statistics>'
            }
        }
    }); 

    $stateProvider.state('stripe-statistics', {
        url: '/stripe-statistics',
        views: {
            'rootView': {
                template: '<sw-stripe-statistics></sw-stripe-statistics>'
            }
        }
    });

    $stateProvider.state('db-copy', {
        url: '/db-copy',
        views: {
            'rootView': {
                template: '<db-copy></db-copy>'
            }
        }
    })

    $stateProvider.state('users', {
        url: '/users',
        views: {
            'rootView': {
                template: '<users-list></users-list>'
            }
        }
    })
}
