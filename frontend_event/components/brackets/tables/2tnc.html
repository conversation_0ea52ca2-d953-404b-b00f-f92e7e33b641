<div class="table-responsive">
   <table class="brackets-table">
      <tr>
         <td class="bd bd-b"><b><span class="brackets-team">{{matches[1].team1}}</span></b></td>
         <td></td>
      </tr>
      <tr>
         <td class="bd bd-r"></td>
         <td></td>
      </tr>
      <tr>
         <td class="bd bd-r">Match 1</td>
         <td></td>
      </tr>
      <tr>
         <td class="bd bd-r"><a ui-sref="events.event.courts.courtdetails({court: matches[1].court_id})">{{matches[1].court_name}}</a></td>
         <td class="bd bd-b">
            <b>
               <a ng-if="matches[1].winning_team_id" ui-sref="events.event.divisions.division.divisionteams.divisionteam({team: matches[1].winning_team_id})">{{matches[1].winning_team_name}}</a>
            </b>
         </td>

      </tr>
      <tr>
         <td class="bd bd-r">{{matches[1].date_start}}</td>
         <td><span class="bracket-scores">{{matches[1].scores}}</span> {{matches[1].winner_place}}</td>
      </tr>
      <tr>
         <td class="bd bd-r"><i><span class="brackets-team">{{matches[1].ref}}</span></i></td>
         <td></td>
      </tr>
      <tr>
         <td class="bd bd-r"></td>
      </tr>
      <tr>
         <td class="bd bd-t"><b><span class="brackets-team">{{matches[1].team2}}</span></b></td>
      </tr>
   </table>
</div>

<br><br>

<div class="future-item" ng-repeat="future in finishes" ng-if="finishes.length">
    <future-item future-item="future.Winner" skip-ref="true" type="Winner"></future-item>
    <future-item future-item="future.Loser" skip-ref="true" type="Loser"></future-item>
</div>
