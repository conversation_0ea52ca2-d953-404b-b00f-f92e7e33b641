angular.module('SportWrench')
    .directive('brackets', brackets)
    .directive('bracketsTeam', bracketsTeam);

brackets.$inject = ['$stateParams'];

function brackets ($stateParams) {
	return {
		restrict: "E",
		scope: {
			matches: '=',
			pool: '=',
			type: '@',
			path: '@'
		},
		controller: ['$scope', '$parse', function($scope, $parse) {
			let remove_extension = function () {
				return $scope.type.replace(/\.html/, "");
			};

			$scope.finishes = getFinishes();

			$scope.type = remove_extension($scope.type) + ".html";
			$scope.contentUrl = ($scope.path || 'components/brackets/tables/') + $scope.type;
			$scope.$watch("type", function(newVal, oldVal) {
				if (newVal && newVal != oldVal) {
					$scope.contentUrl = ($scope.path || 'components/brackets/tables/') + remove_extension(newVal) + ".html";
				}
			});
			$scope.$stateParams = $stateParams;
			$scope.isMatchAvailable = function(future) {
				return (future.next_match && Object.keys(future.next_match).length !== 0);
			};

			$scope.showPlacesTable = !!($scope.pool &&
                $scope.pool.display_name_short &&
                (
                    $scope.pool.display_name_short.indexOf('X') > -1 ||
                    $scope.pool.display_name_short.indexOf('CH') > -1
                ));

			this.matches = $scope.matches;


			$scope.winner = {};

			if ($scope.matches && $scope.matches.length) {
				$scope.matches.forEach(function(match) {
					if (match.team1_name == match.winning_team_name) {
						$scope.winner = {
							name: match.winning_team_name,
							rank: match.team1_rank
						};
						match.team1_rank = null;
					}
					if (match.team2_name == match.winning_team_name) {
						$scope.winner = {
							name: match.winning_team_name,
							rank: match.team2_rank
						};
						match.team2_rank = null;
					}
				});

				$scope.matches.forEach(function(match) {
					if (match.winning_team_name === $scope.winner.name && $scope.winner.rank) {
						match.winning_team_name += ' (' + $parse('winner.rank | suffix')($scope) + ')';
					}
				});
			}

			function getFinishes () {
                return $scope.matches.reduce((all, match) => {
                    if(match.finishes) {
                        try {
                            let future = JSON.parse(match.finishes);

                            all.push(future);
                        } catch (err) {
                            console.error(err);
                        }
                    }

                    return all;
                }, []);
            }
		}],
		template: '<div ng-include="contentUrl"></div>'
	}
}

bracketsTeam.$inject = ['$parse', '$http', '$templateCache', '$compile'];

function bracketsTeam ($parse, $http, $templateCache, $compile) {
	return {
		restrict: "C",
		require: '^brackets',
		scope: {},
        compile: function compile(tElement, tAttrs, transclude) {
            var codes = tElement.text().match(/matches\[(\d+)\]\.(.*)\}\}$/i);
            var match = codes[1];
            var team = codes[2];
            var template = $templateCache.get('components/brackets/brackets-team.html');
            var templateLoader;

            if (angular.isUndefined(template)) {
                templateLoader = $http.get('components/brackets/brackets-team.html', {cache: $templateCache});
            }
            return {
                pre: function preLink(scope, iElement, iAttrs, ctrl) {
                    scope.team = {
                        id: $parse('matches[' + match + '].' + team + '_roster_id')(ctrl),
                        name: $parse('matches[' + match + '].' + team + '_name')(ctrl),
                        temp_name: $parse('matches[' + match + '].' + team + '_temp_name')(ctrl),
                        temp_id: $parse('matches[' + match + '].' + team + '_temp_roster_id')(ctrl),
                        pool_id: $parse('matches[' + match + '].' + team + '_pool_id')(ctrl),
                        pool_name: $parse('matches[' + match + '].' + team + '_pool_name')(ctrl),
                        source_name: $parse('matches[' + match + '].source.' + team + '.name')(ctrl),
                        source_type: $parse('matches[' + match + '].source.' + team + '.type')(ctrl),
                        rank: $parse('matches[' + match + '].' + team + '_rank')(ctrl),
                        show_previously_accepted_bid:
                            $parse('matches[' + match + '].show_previously_accepted_bid_' + team )(ctrl)
                    };

                    if(scope.team.rank) {
                        scope.team.rank = $parse('team.rank | suffix')(scope);
                        scope.team.name += ' (' + scope.team.rank + ')';
                    }

                    if (angular.isDefined(template)) {
                        renderItem(template);
                    } else {
                        templateLoader.then(function (response) {
                            renderItem(response.data);
                        });
                    }

                    function renderItem (template) {
                        iElement.html(template);
                        $compile(iElement.contents())(scope);
                    }
                }
            }
        }
	};
}
