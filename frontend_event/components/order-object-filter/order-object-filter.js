angular.module('SportWrench').filter('orderObjectBy', function() {
  return function(items, field, reverse) {
    var filtered = [];
    angular.forEach(items, function(item) {
      filtered.unshift(item);
    });
    if (field) {
      filtered.sort(function (a, b) {
        return (b[field] > a[field]) ? 1 : (b[field] < a[field]) ? -1 : 0;
      });
      if(reverse) filtered.reverse();
    }
    return filtered;
  };
});
