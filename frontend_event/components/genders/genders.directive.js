angular.module('SportWrench').directive('genders', function () {
    return {
        restrict: 'E',
        scope: {
            f: '<',
            m: '<',
            title_f: '<',
            title_m: '<',
            fw: '<',
        },
        templateUrl: 'components/genders/genders.html',
        link: function (scope, attrs, elem) {
            _.each(['f', 'm', 'fw'], (e)=> {
                if(!elem[e] && typeof elem[e] == 'string') scope[e]= true;
            })
        }
    }
});
