angular.module('SportWrench').directive('poolWide', ['$filter', function ($filter) {
	return {
		restrict: 'E',
		scope: true,
		replace: true,
		templateUrl: 'components/pool-wide/pool-wide.html',
		link: function (scope, iElement, iAttrs) {
			
			scope.isWide = false;

			scope.wider = function() {
				if (scope.isWide) {
					$('.pool-details-wide').removeClass('tabs');
					$('.pool-details-tabs').removeClass('wide');
				} else {
					$('.pool-details-wide').addClass('tabs');
					$('.pool-details-tabs').addClass('wide');
				}

				scope.isWide = !scope.isWide;
			}

		}
	};
}]);