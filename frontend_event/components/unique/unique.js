angular.module('SportWrench')
.filter('unique', function() {
   return function(collection, keyname) {
      var output = [], 
          keys = [];

      angular.forEach(collection, function(item) {
          var key = item[keyname];
          if(keys.indexOf(key) === -1) {
              keys.push(key);
              if (moment(parseInt(item.date_start)).isValid()) {
                output.push(item);
              }
          }
      });

      return output;
   };
});