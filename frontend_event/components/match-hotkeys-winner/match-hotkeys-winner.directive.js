angular.module('SportWrench').directive('matchHotkeysWinner', matchHotkeysWinner);

matchHotkeysWinner.$inject = ['$document', '$timeout'];

function matchHotkeysWinner ($document, $timeout) {
    return {
        restrict: 'A',
        link: function (scope, elem, attrs) {
            setTimeout(function () {
                $document.on('keyup', function (e) {
                    $timeout(function () {
                        switch (e.keyCode) {
                            case 65: 
                                // A
                                scope.winner = 1;
                                break;
                            case 66: 
                                // B
                                scope.winner = 2;
                                break;
                            case 50: 
                                // 2
                                scope.set_count = 2;
                                if (scope.$parent) {
                                    scope.$parent.set_count = 2;
                                }
                                break;
                            case 51:
                                // 3 
                                scope.set_count = 3;
                                if (scope.$parent) {
                                    scope.$parent.set_count = 3;
                                }
                                break;
                        }
                    });
                });
            }, 1000);
        }
    };
}
