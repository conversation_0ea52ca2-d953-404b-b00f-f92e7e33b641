angular.module('SportWrench').directive('scrollPropagation', scrollPropagation)

function scrollPropagation () {
	return {
        link: function (scope, elem, attrs) {
            var el = $(elem);
            el.on("wheel", function (e) {

                var context = el.context;
                var originalEvent= e.originalEvent;

                if(originalEvent.deltaY < 0 && context.scrollTop == 0) {
                    e.preventDefault();
                }

                if(originalEvent.deltaY > 0 && context.scrollHeight - context.clientHeight - context.scrollTop <= 1) {
                    e.preventDefault();
                }
            });
        }
	}
}