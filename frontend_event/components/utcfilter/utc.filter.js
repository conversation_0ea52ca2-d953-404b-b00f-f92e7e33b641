angular.module('SportWrench')

.filter("UTCdate",function(){
    return function (date, format, parse) {
        if(!date) {
            return;
        }
        parse = parse || "";
        format = format || "";
        var result = moment.utc(date, parse).format(format);
        if (result === "Invalid date" && isNaN(+date) === false) {
        	var result = moment.utc(+date, parse).format(format);
        }
        return result;
    };
});
