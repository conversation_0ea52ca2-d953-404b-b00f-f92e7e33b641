angular.module('SportWrench').directive('futureMatches', function () {
	return {
		restrict: 'E',
		scope: {
			matches 	: '=',
			stats 		: '='
		},
		templateUrl: 'components/future-matches/future-matches.html',
		link: function (scope) {
			var _matches = scope.matches;
			if (_matches) {
		        _matches = angular.fromJson(_matches);
		        delete _matches.team_ids;		

		        console.log(_matches)		
		        var emptyLength = 0;

		        for (var future in _matches) {
		        	if (_.isEmpty(_matches[future].next_match)) {
		        		emptyLength++;
		        	}

		        	if(!_.isEmpty(_matches[future].next_match)) {
		        		_matches[future].next_match.start 
		        					= new Date(parseInt(_matches[future].next_match.secs_start * 1000));
		        	}
		        	if(!_.isEmpty(_matches[future].next_ref)) {
		        		_matches[future].next_ref.start = new Date(parseInt(_matches[future].next_ref.secs_start*1000));
		        	}
		        }

		        scope.futureAssigned = emptyLength == Object.keys(_matches).length ? false : true; 
		    }

		    scope.matchesList = function () {
		    	return _matches;
		    }

			scope.isMatchAvailable = function (future) {
		        return (future.next_match && Object.keys(future.next_match).length !== 0 && future.next_match.match_id);
		    };

            scope.isPlaceholderAvailable = function (future) {
                return (future.next_match && Object.keys(future.next_match).length !== 0 && future.next_match.display_name);
            };

		    scope.isRefAvailable = function (future) {
		        return (future.next_ref && Object.keys(future.next_ref).length !== 0 && future.next_ref.match_id);
		    };

		    scope.isFuturePlaceholderShown = function (future) {
		        return !future.next_match.match_id && !future.next_ref.match_id;
		    };

		    scope.isNothingAssigned = function (future) {
		        return scope.isFuturePlaceholderShown(future) && !future.next_match.display_name;
		    };
		}
	}
})
