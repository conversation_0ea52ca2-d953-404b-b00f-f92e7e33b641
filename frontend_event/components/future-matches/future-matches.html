<h4 class="text-primary" ng-if="futureAssigned"><i class='fa fa-binoculars'></i> Future</h4>
<div class="future-item" ng-repeat="future in matchesList() | toArray | orderBy: 'number'" ng-if="futureAssigned">
    <a 
        class="clearfix list-group-item" 
        ng-show="isMatchAvailable(future)"
        style="padding: 10px;"
        ui-sref="events.event.matches.match({match: future.next_match.match_id})">
            <span class="counter">{{$index+1 | suffix }}</span>
            <span class="list-arrow glyphicon glyphicon-chevron-right"></span>
            <span class="future-item">
                <i class="fa fa-play-circle-o"></i>
                <b>{{future.team_name}}</b> Plays {{future.next_match.display_name}} {{future.next_match.week_day}} {{future.next_match.start_time_string}} {{future.next_match.court}}
            </span>
    </a>
    <a 
        class="clearfix list-group-item" 
        ng-show="isRefAvailable(future)" 
        style="padding: 10px;"
        ui-sref="events.event.matches.match({match: future.next_ref.match_id})">
            <span class="counter">{{$index+1 | suffix }}</span>
            <span class="list-arrow glyphicon glyphicon-chevron-right"></span>
            <span class="future-item">
                <i class="fa fa-flag pull-lef"></i>
                <b>{{future.team_name}}</b> Officiates {{future.next_ref.display_name}} {{future.next_ref.week_day}} {{future.next_ref.start_time_string}} {{future.next_ref.court}}
            </span>
    </a>
    <li class="list-group-item" style="padding: 10px;" ng-show="!isMatchAvailable(future) && isPlaceholderAvailable(future)">
        <span class="counter">{{$index+1 | suffix }}</span>
        <span class="list-arrow glyphicon glyphicon-chevron-right"></span>
        <span class="future-item">
            <i class="fa fa-play-circle-o"></i>
            {{future.next_match.display_name}}
        </span>
    </li>
    <li class="list-group-item" style="padding: 10px;" ng-show="isNothingAssigned(future)">
        <span class="counter">{{$index+1 | suffix }}</span> Not assigned yet
    </li>
</div>
