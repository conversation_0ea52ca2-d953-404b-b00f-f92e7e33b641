<h3 class="esw_title">Find By Staff</h3>
<div class="row">
	<div class="form-group col-md-3">
		<input type="text" ng-model="search.first" class="form-control" placeholder="Search by first name">
	</div>
	<div class="form-group col-md-3">
		<input type="text" ng-model="search.last" class="form-control" placeholder="Search by last name">
	</div>
	<div class="form-group col-md-3">
		<input type="text" ng-model="search.team_name" class="form-control" placeholder="Search by team name">
	</div>
	<div class="form-group col-md-3">
		<button type="button" class="btn btn-default" ng-click="search()">Search</button>
	</div>
</div>
<div class="list-group">
	<div class="row">
		<div class="form-group col-md-12">
			<div ng-show="staff && !staff.length">0 staff</div>
			<div ng-show="staff == undefined">
				<img class="loading-image" src="/images/loading-bubbles.svg" width="64" height="64" alt="">
			</div>
			<a ui-sref="events.event.divisions.division.divisionteams.divisionteam({team: st.roster_team_id, division: st.division_id})" class="list-group-item" ng-repeat="st in staff">
				<span class="staff-item"><b>{{st.last}}, {{st.first}}</b> - {{st.team_name}}</span>
				<span class="pull-right staff-team-code">
					<span class="list-arrow glyphicon glyphicon-chevron-right"></span>
					<span class="list-text-right">{{st.state}}</span>
				</span>
			</a>
		</div>
	</div>
</div>