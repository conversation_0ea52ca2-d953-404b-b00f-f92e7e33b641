angular.module('SportWrench').controller('Public.Events.StaffController', eventStaffController);

eventStaffController.$inject = ['$scope', '$rootScope', '$stateParams', 'eswService'];

function eventStaffController($scope, $rootScope, $stateParams, eswService)
{
    $scope.staff = $rootScope.$storage.lastStaffs || false;

    $rootScope.pageTitle = "Staff";

    $scope.search = function() {
    	$scope.staff = undefined;

	    eswService.getEventStaff($stateParams.event, $scope.search.first, $scope.search.last, $scope.search.team_name, function(response) {
	        $scope.staff = response.data;
	        $rootScope.$storage.lastStaffs = $scope.staff;
	    });
	};
}
