<br/>
<h4 ng-show="!team.results.length">No information</h4>

<div ng-repeat="pool in team.results" class="list-group">
  <a class="list-group-item list-group-item-info" ng-if="pool.is_pool" href ui-sref="events.event.divisions.division.pools.pooldetails.schedule({pool: pool.uuid})">
    <span class="fa fa-list-ol"></span>
    <span class="underlined">{{ pool.round_name }} {{ pool.pb_name }}</span>
    <span class="pool-icons">
      <span class="span-right-strict">
        <span ng-if="pool.matches_won || pool.matches_lost">{{pool.matches_won}}-{{pool.matches_lost}}</span>
        <span ng-if="pool.sets_won || pool.sets_lost">({{pool.sets_won}}-{{pool.sets_lost}})</span>
      </span>
      <!-- <matches-sets ng-if="!pool.matches_won && !pool.matches_lost" class="spacer-lg-r" pool="{{pool.pb_name}}"></matches-sets> -->
      <span class="glyphicon glyphicon-chevron-right"></span>
    </span>
  </a>
  <a class="list-group-item list-group-item-info" ng-if="!pool.is_pool" ui-sref="events.event.divisions.division.divisionbrackets.bracket({bracket: pool.uuid})">
    <span class="fa fa-sitemap fa-rotate-90"></span>
    <span class="underlined">{{ pool.round_name }} {{ pool.pb_name }} Bracket</span>
    <span class="pool-icons">
      <span class="span-right-strict">
        <span ng-if="pool.matches_won || pool.matches_lost">{{pool.matches_won}}-{{pool.matches_lost}}</span>
        <span ng-if="pool.sets_won || pool.sets_lost">({{pool.sets_won}}-{{pool.sets_lost}})</span>
      </span>
      <!-- <matches-sets ng-if="!pool.matches_won && !pool.matches_lost" class="spacer-lg-r" pool="{{pool.pb_name}}"></matches-sets> -->
      <span class="glyphicon glyphicon-chevron-right"></span>
    </span>
  </a>
  <a ng-repeat="match in pool.matches" class="list-group-item clearfix" href ui-sref="events.event.matches.match({match: match.match_id})">
    <div class="clearfix">
      <span ng-if="match.results.team1.roster_team_id && match.results.team2.roster_team_id">
        <i ng-class="{'fa fa-smile-o': (team.roster_team_id == match.results.team1.roster_team_id && match.results.winner == '1') || (team.roster_team_id == match.results.team2.roster_team_id && match.results.winner == '2'), 'fa fa-frown-o': (team.roster_team_id != match.results.team2.roster_team_id && match.results.winner == '2') || (team.roster_team_id != match.results.team1.roster_team_id && match.results.winner == '1')}"></i>
        <span ng-if="(team.roster_team_id == match.results.team1.roster_team_id && match.results.winner == '1') || (team.roster_team_id == match.results.team2.roster_team_id && match.results.winner == '2')"> Wins</span>
        <span ng-if="(team.roster_team_id != match.results.team2.roster_team_id && match.results.winner == '2') || (team.roster_team_id != match.results.team1.roster_team_id && match.results.winner == '1')"> Loses</span>
      </span>
      <span ng-if="$stateParams.team == match.results.team1.roster_team_id">{{ match.results.team1.sets_won }}-{{ match.results.team1.sets_lost }}</span> <span ng-if="$stateParams.team == match.results.team2.roster_team_id">{{ match.results.team2.sets_won }}-{{ match.results.team2.sets_lost }} </span> vs <b>{{match.opponent_team_name}}</b>
      <span class="list-text-right-strict pull-right hidden-xs">
        <span class="span-right-strict ng-binding">{{match.opponent_organization_code}}</span>
        <span class="list-arrow glyphicon glyphicon-chevron-right"></span>
      </span>
    </div>
  </a>
</div>