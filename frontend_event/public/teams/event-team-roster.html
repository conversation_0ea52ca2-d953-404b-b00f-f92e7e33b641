<br/>

<h4 ng-show="!team.athletes.length && !team.staff.length">No information</h4>

<table class="table club-athletes-columns" ng-show="team.athletes.length">
  <thead>
    <tr>
      <td class="col-05"><b>Uni</b></td>
      <td class="col-05"><b>Pos</b></td>
      <td class="col-1"><b>Name</b></td>
    </tr>    
  </thead>
  <tbody>
    <tr ng-repeat="athlete in team.athletes">
      <td class="col-05" ng-if="!eventHasAAUSanctioning">
        <span ng-if="athlete.uniform">{{ athlete.uniform }}</span>
        <span ng-if="!athlete.uniform"> - </span>
      </td>
      <td class="col-05" ng-if="eventHasAAUSanctioning">
        <span ng-if="athlete.aau_uniform">{{ athlete.aau_uniform }}</span>
        <span ng-if="!athlete.aau_uniform"> - </span>
      </td>
      <td class="col-05">
        <span ng-if="athlete.short_position">{{ athlete.short_position }}</span>
        <span ng-if="!athlete.short_position"> - </span>
      </td>
      <td class="col-1">{{ athlete.last }}, {{ athlete.first }}</td>
    </tr>
  </tbody>
</table>

<table class="table club-athletes-columns" ng-show="team.staff.length">
  <thead>
    <tr>
      <td class="col-08"><b>Role</b></td>
      <td class="col-1"><b>Name</b></td>
    </tr>    
  </thead>
  <tbody>
    <tr ng-repeat="staff in team.staff">
      <td class="col-08">
        <span ng-if="staff.role_name">{{ staff.role_name }}</span>
        <span ng-if="!staff.role_name"> - </span>
      </td>
      <td class="col-1">{{ staff.last }}, {{ staff.first }}</td>
    </tr>
  </tbody>
</table>

