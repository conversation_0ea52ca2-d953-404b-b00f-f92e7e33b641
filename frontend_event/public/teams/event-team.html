<div class="row">
    <h3 class="col-md-12">{{title}}</h3>
    <p class="col-md-12 esw_title">{{USAV}}</p>
</div>

<div class="list-group" ng-if="!isDoublesTeams && hasClubs">
  <a class="list-group-item" ui-sref="events.event.clubs.clubteams({club: team.roster_club_id})">
    <span class="list-arrow glyphicon glyphicon-chevron-right"></span>
    <span class="list-text-right">{{team.state}}</span>
    <span class="crop">Club: {{team.club_name}}</span>
  </a>
</div>

<div class="list-group" ng-if="showManualClub">
    <a class="list-group-item" ui-sref="events.event.manualclubs.clubteams({club: team.manual_club_name})">
        <span class="list-arrow glyphicon glyphicon-chevron-right"></span>
        <span class="crop">Club: {{team.manual_club_name}}</span>
    </a>
</div>

<tabset>
  <tab ng-show="hasShedule" active="tabs[0].active" ng-click="setTab('schedule')">
    <tab-heading>
      <i class='fa fa-clock-o'></i> Schedule
    </tab-heading>
  </tab>
  <!-- <tab heading="Future" active="tabs[1].active" ng-click="setTab('future')"></tab> -->
  <tab ng-show="hasResults" active="tabs[2].active" ng-click="setTab('results')">
    <tab-heading>
      <i class='fa fa-list-ol'></i> Results
    </tab-heading>
  </tab>
  <tab ng-show="hasRoster()" active="tabs[3].active" ng-click="setTab('roster')">
    <tab-heading>
      <i class='fa fa-users'></i> Roster
    </tab-heading>
  </tab>
</tabset>

<div class="list-group" ui-view></div>
