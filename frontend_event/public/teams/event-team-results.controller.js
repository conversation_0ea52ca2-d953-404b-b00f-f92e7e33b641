angular.module('SportWrench').controller('Public.Events.TeamResultsController', TeamResultsController);

TeamResultsController.$inject = ['$scope', '$rootScope', '$stateParams', 'eswService', 'currentTeam', '$state'];

function TeamResultsController($scope, $rootScope, $stateParams, eswService, currentTeam, $state)
{
    $scope.team = currentTeam;

    $rootScope.pageTitle = "Team - " + currentTeam.team_name + " Results";
    $scope.title = currentTeam.team_name;

    if ($scope.team && $scope.team.results && $scope.team.results.length) {
        $scope.team.results.forEach(function(res) {
            try {
                res.matches = angular.fromJson(res.matches);

                if (res.pb_stats) {
                    res.pb_stats = angular.fromJson(res.pb_stats);
                
                    res.matches_lost = null;
                    res.matches_won = null;
                    res.sets_lost = null;
                    res.sets_won = null;

                    for(var key in res.pb_stats) {
                        if (res.pb_stats[key].team_id == $scope.team.roster_team_id) {
                            res.matches_lost = res.pb_stats[key].matches_lost;
                            res.matches_won = res.pb_stats[key].matches_won;
                            res.sets_lost = res.pb_stats[key].sets_lost;
                            res.sets_won = res.pb_stats[key].sets_won;
                        }
                    }
                }

                console.info(res);

                res.matches.forEach(function(mt) {
                    mt.results = angular.fromJson(mt.results);
                });
            } catch (e) {
                console.log('JSON Error: res.results = ', res.results);
            }
        });
    }
}
