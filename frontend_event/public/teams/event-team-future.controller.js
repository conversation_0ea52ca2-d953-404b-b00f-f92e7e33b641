angular.module('SportWrench').controller('Public.Events.TeamFutureController', TeamFutureController);

TeamFutureController.$inject = ['$scope', '$rootScope', '$stateParams', 'eswService', 'currentTeam', '$state'];

function TeamFutureController($scope, $rootScope, $stateParams, eswService, currentTeam, $state)
{
    $scope.team = currentTeam;

    $rootScope.pageTitle = "Team - " + currentTeam.team_name + " Future";
    $scope.title = currentTeam.team_name;
}
