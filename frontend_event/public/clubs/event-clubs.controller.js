angular.module('SportWrench').controller('Public.Events.ClubsController', eventClubsController);

eventClubsController.$inject = ['$scope', '$rootScope', '$stateParams', 'eswService', 'eventsList'];

function eventClubsController($scope, $rootScope, $stateParams, eswService, eventsList)
{
    $scope.clubs = undefined;

    $rootScope.pageTitle = ($rootScope.currentEvent && $rootScope.currentEvent.name) + ' Clubs'

    eswService.getEventClubs($stateParams.event, function(response) {
        $scope.clubs = response.data;
    });

    $scope.saveClubName = function(name, state) {
        $rootScope.currentClubName = name;
        $rootScope.$storage.lastValue = name;
        $rootScope.$storage.clubState = state;
    };
}
