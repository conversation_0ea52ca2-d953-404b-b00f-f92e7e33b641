angular.module('SportWrench').controller('Public.Events.ClubTeamsController', clubTeamsController);

clubTeamsController.$inject = ['$scope', '$rootScope', '$stateParams', 'eswService', 'currentClub'];

function clubTeamsController($scope, $rootScope, $stateParams, eswService, currentClub)
{
    $scope.teams 		= undefined;

    $scope.club_name 	= currentClub.club_name;
    $scope.club_state 	= currentClub.state;
    $scope.has_clubs 	= $rootScope.currentEvent && $rootScope.currentEvent.has_clubs;

    $rootScope.pageTitle = $scope.club_name + " Teams";

    eswService.getEventClubTeams($stateParams.event, $stateParams.club, function(response) {
        $scope.teams = response.data;
    });
}
