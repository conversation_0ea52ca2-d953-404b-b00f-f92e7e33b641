<h3 class="esw_title">{{$root.pageTitle}}</h3>
<div class="list-group">
    <div class="form-group" ng-show="clubs.length">
        <input type="text" ng-model="search" class="form-control" placeholder="Search by club">
    </div>
    <div ng-show="!clubs.length && clubs != undefined">0 clubs</div>
    <spinner active="clubs == undefined"></spinner>
    <a 
      ui-sref="events.event.clubs.clubteams({club: club.roster_club_id})" 
      ng-click="saveClubName(club.club_name, club.state)" 
      class="list-group-item" 
      ng-repeat="club in clubs | orderBy: 'club_name' | filter: search">
        <span class="pull-right">
          <span class="list-arrow glyphicon glyphicon-chevron-right"></span>
          <span class="list-text-right">{{club.state}}</span>
        </span>
        <span class="text-ellipsis">{{club.club_name}}</span>
    </a>
</div>
