angular.module('SportWrench').controller('Public.Events.StandingsFutureController', StandingsFutureController);

StandingsFutureController.$inject = ['$scope', '$rootScope', '$stateParams', 'currentStandings', '$filter'];

function StandingsFutureController($scope, $rootScope, $stateParams, currentStandings, $filter)
{
  // $scope.division_name = currentStandingsFuture.pool.name;

  // $scope.pool_name = currentStandingsFuture.pool.display_name;

  // $scope.standings_future = currentStandingsFuture.standings;

  // $scope.standings_future.forEach(function(fut) {
  //   fut.start = new Date(parseInt(fut.date_start));
  // });

	$rootScope.pageTitle = "Standings";

	try {
		if (currentStandings.pb_stats) currentStandings.pb_stats = angular.fromJson(currentStandings.pb_stats);
	} catch (e) {
		console.log('JSON Error: currentStandings.pb_stats = ', currentStandings.pb_stats);
	}

	$scope.num = function (str) {
		if(!str) return '';
		return $filter('number')(+str, 3);
	}

	$scope.standings = currentStandings;

	console.log($scope.standings)
}
