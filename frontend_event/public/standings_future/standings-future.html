<h3 class="esw_title">Standings</h3>

<div class="list-group">
  <a class="clearfix list-group-item" ui-sref="events.event.divisions.division.pools.pooldetails.schedule({pool: $stateParams.pool})">
    <span class="list-arrow glyphicon glyphicon-chevron-right"></span>
    <b>{{ standings.division_name }} > {{ standings.display_name }}</b>
  </a>

  <h4 ng-hide="standings.pb_stats">No known teams</h4>

  <span ng-show="standings.pb_stats" class="clearfix list-group-item">
    <table class="table table-hover future-table-desktop">
      <thead>
        <tr>
          <td class="col-xs-1">Rank</td>
          <td class="col-xs-3">Team Name</td>
          <td class="col-xs-2 text-center">Match W-L</td>
          <td class="col-xs-2 text-center">Set W-L</td>
          <td class="col-xs-2 text-center">Point Ratio</td>
          <!-- <td class="col-xs-2 text-center">Code</td> -->
          <td class="col-xs-1 text-center"></td>
        </tr>
      </thead>
      <tbody>
        <tr ng-repeat="(key, standing) in standings.pb_stats | orderObjectBy:'rank':true" ui-sref="events.event.divisions.division.divisionteams.divisionteam.schedule({team: standing.team_id})">
          <td class="col-xs-1"><b>{{standing.rank}}</b></td>
          <td class="col-xs-3 standings-team-name">{{standing.name}}</td>
          <td class="col-xs-2 text-center">{{standing.matches_won}}-{{standing.matches_lost}}</td>
          <td class="col-xs-2 text-center">{{standing.sets_won}}-{{standing.sets_lost}} ({{standing.sets_pct | number:2}}%)</td>
          <td class="col-xs-2 text-center">{{standing.points_lost ? num(standing.points_ratio) : "" }}</td>
          <!-- td class="col-xs-2 text-center" ng-if="standing.points_lost == '0' || st.sets_pct > 100">100%</td -->
          <!-- <td class="col-xs-2 text-center">{{ standing.organization_code }}</td> -->
          <td class="col-xs-1 text-center"><span class="list-arrow glyphicon glyphicon-chevron-right"></span></td>
        </tr>
      </tbody>
    </table>

    <table class="table future-table-mobile">
      <thead>
        <tr>
          <td class="col-xs-3 text-center">Rank</td>
          <td class="col-xs-3 text-center">Match W-L</td>
          <td class="col-xs-3 text-center">Set W-L</td>
          <td class="col-xs-3 text-center">Point Ratio</td>
        </tr>
      </thead>
    </table>
  </span>

  <a ng-show="standings.pb_stats" class="list-group-item future-mobile-item" ng-repeat="(key, standing) in standings.pb_stats | orderObjectBy:'rank':true" ui-sref="events.event.divisions.division.divisionteams.divisionteam.schedule({team: standing.team_id})">
    <span class="list-arrow glyphicon glyphicon-chevron-right"></span>
    <div class="clearfix">
      {{ standing.name }}
      <!-- <span class="list-text-right pull-right hidden-xs">{{ standing.organization_code }}</span> -->
    </div>
    <div class="clearfix standings-scores">
      <div ng-hide="standing.matches_lost == '0' && standing.matches_won == '0'">
        <table class="table table-condensed future-table-mobile">
          <tbody>
            <tr>
              <td class="col-xs-3 text-center rank-cell"><b>{{standing.rank}}</b></td>
              <td class="col-xs-3 text-center">{{standing.matches_won}}-{{standing.matches_lost}}</td>
              <td class="col-xs-3 text-center">{{standing.sets_won}}-{{standing.sets_lost}} ({{standing.sets_pct | number:2}}%)</td>
              <td class="col-xs-3 text-center">{{standing.points_lost ? num(standing.points_ratio) : ""}}</td>
            </tr>
          </tbody>
        </table>
      </div>
      <span ng-show="standing.matches_lost == '0' && standing.matches_won == '0'" class="center-block text-center">No Scores Entered Yet</span>
    </div>
  </a>
  
  <!--span class="clearfix list-group-item" ng-show="standings_future.future.length"><b>Future</b></span>

  <div class="list-group">
    <a class="clearfix list-group-item" ng-repeat="future in standings_future.future | orderBy: 'number'" ui-sref="events.event.matches.match({match: future.match_id})">
      <i ng-class="{'fa fa-eye': future.officiates, 'fa fa-play-circle-o': !future.officiates}" class="pull-left"></i>
      <span class="pull-left">{{ future.number }} - <span ng-show="future.officiates">Officiates</span> <span ng-hide="future.officiates">Plays</span> {{ future.start | UTCdate: 'ddd h:mma' }} {{ future.court_name }} {{ future.pool_name }}</span>
      <span class="list-arrow glyphicon glyphicon-chevron-right"></span>
    </a>
  </div-->
</div>

