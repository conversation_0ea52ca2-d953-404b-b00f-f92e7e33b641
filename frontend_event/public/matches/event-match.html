<!-- <h3 class="esw_title" style="display: inline-block;">{{head}} > {{ match.start | UTCdate: 'ddd h:mma' }} {{ match.court_name }}</h3> -->
<h3 class="esw_title" style="display: inline-block;">{{ match.round_name }} {{ match.pb_name }} Match {{ match.match_number }} > {{ match.start | UTCdate: 'ddd h:mma' }} {{ match.court_name }}</h3>
<div class="list-group">
  <a ng-if="match.is_pool" class="clearfix list-group-item list-group-item-info" ui-sref="events.event.divisions.division.pools.pooldetails.schedule({division: match.division_id ,pool: match.pool_bracket_id})">
    <span class="clip">
      <span class="fa fa-list-ol"></span>
      <span class="underlined">{{title}}</span>
    </span>
    <span class="list-arrow glyphicon glyphicon-chevron-right"></span>
  </a>
  <a ng-if="!match.is_pool" class="clearfix list-group-item list-group-item-info" ui-sref="events.event.divisions.division.divisionbrackets.bracket({division: match.division_id, bracket: match.pool_bracket_id})">
    <span class="clip">
      <span class="fa fa-sitemap fa-rotate-90"></span>
      <span class="underlined">{{title}}</span>
    </span>
    <span class="list-arrow glyphicon glyphicon-chevron-right"></span>
  </a>

  <a ng-if="match.team1_roster_id" class="clearfix list-group-item" ui-sref="events.event.divisions.division.divisionteams.divisionteam({division: match.division_id, team: match.team1_roster_id})">
    <span class="pull-left">
      <!-- <i class="fa fa-play-circle-o"></i> -->
      <i ng-class="{'fa fa-smile-o': match.results.winner == 1, 'fa fa-frown-o': match.results.winner == 2}"></i>
      <b ng-if="match.team1_name">&nbsp;{{ match.team1_name }}</b>
      <b ng-if="!match.team1_name">{{ match.team1_pool_name }}</b>
    </span>
    <span class="list-arrow glyphicon glyphicon-chevron-right"></span>
    <span class="list-text-right pull-right match-team-code">
      <span ng-show="!match.results">{{ match.team1_code }}</span>
      <span ng-show="match.results.winner == 1">Wins</span>
      <span ng-show="match.results.winner == 2">Loses</span>
    </span>
  </a>
  <span class="clearfix list-group-item" ng-if="!match.team1_roster_id && match.source.team1.type != 5">
    <i ng-if="!results" class="fa fa-play-circle-o"></i> <i ng-class="{'fa fa-frown-o': match.results.winner == 2, 'fa fa-smile-o': match.results.winner == 1}"></i>
    <b>{{ match.source.team1.name }}</b>
  </span>
  <a class="clearfix list-group-item" ng-if="!match.team1_roster_id && match.source.team1.type == 5" ui-sref="events.event.divisions.division.pools.pooldetails.schedule({division: match.division_id, pool: match.team1_pool_id})">
    <span class="list-arrow glyphicon glyphicon-chevron-right"></span>
    <span class="pull-left"><i class="fa fa-play-circle-o"></i> {{ match.team1_pool_name }}</span>
  </a>

  <div class="clearfix list-group-item" ng-show="match.results">
    <span class="spacer-lg-l">{{ match.results.team1.scores }}</span>
  </div>

  <a ng-if="match.team2_roster_id" class="clearfix list-group-item" ui-sref="events.event.divisions.division.divisionteams.divisionteam({division: match.division_id, team: match.team2_roster_id})">
    <span class="pull-left">
      <i ng-class="{'fa fa-smile-o': match.results.winner == 2, 'fa fa-frown-o': match.results.winner == 1}"></i>
      <b ng-if="match.team2_name">&nbsp;{{ match.team2_name }}</b>
      <b ng-if="!match.team2_name">{{ match.team2_pool_name }}</b>
    </span>
    <span class="list-arrow glyphicon glyphicon-chevron-right"></span>
    <span class="list-text-right pull-right match-team-code">
      <span ng-show="!match.results">{{ match.team2_code }}</span>
      <span ng-show="match.results.winner == 2">Wins</span>
      <span ng-show="match.results.winner == 1">Loses</span>
    </span>
  </a>
  <span class="clearfix list-group-item" ng-if="!match.team2_roster_id && match.source.team2.type != 5"><i ng-if="!results" class="fa fa-play-circle-o"></i> <i ng-class="{'fa fa-frown-o': match.results.winner == 1, 'fa fa-smile-o': match.results.winner == 2}"></i> 
    <b>{{ match.source.team2.name }}</b>
  </span>
  <a class="clearfix list-group-item" ng-if="!match.team2_roster_id && match.source.team2.type == 5" ui-sref="events.event.divisions.division.pools.pooldetails.schedule({division: match.division_id, pool: match.team2_pool_id})">
    <span class="list-arrow glyphicon glyphicon-chevron-right"></span>
    <span class="pull-left"><i class="fa fa-play-circle-o"></i> {{ match.team2_pool_name }}</span>
  </a>

  <div class="clearfix list-group-item" ng-show="match.results">
    <span class="spacer-lg-l">{{ match.results.team2.scores }}</span>
  </div>

  <a ng-if="match.ref_roster_id" class="clearfix list-group-item" ng-show="!match.results" ui-sref="events.event.divisions.division.divisionteams.divisionteam({division: match.division_id, team: match.ref_roster_id})">
    <span class="pull-left">
      <i class="fa fa-flag"></i>
      <span ng-if="match.ref_team_name">{{ match.ref_team_name }}</span>
      <span ng-if="!match.ref_team_name">{{ match.ref_pool_name }}</span>
    </span>
    <span class="list-arrow glyphicon glyphicon-chevron-right"></span>
    <span class="list-text-right pull-right hidden-xs">Officiate</span>
  </a>
  <span class="clearfix list-group-item" ng-if="!match.ref_roster_id"><i class="fa fa-flag"></i> 
    <span ng-if="match.source.ref.id">{{ match.source.ref.name }}</span>
    <span ng-if="!match.source.ref.id && match.source.ref.type != 5">{{ match.source.ref.name }}</span>
    <span ng-if="!match.source.ref.id && match.source.ref.type == 5">{{ match.ref_pool_name }}</span>
  </span>
</div>

<future-matches
    matches="match.pb_finishes"
    stats="match.pb_stats"
></future-matches>