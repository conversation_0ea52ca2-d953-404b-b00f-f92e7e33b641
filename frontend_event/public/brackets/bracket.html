<h3 class="esw_title bracket-title">
    {{bracket.name}} <!-- {{bracket.pool.full_name}} -->

    <div class="esw-nav">
        <a class="prev-link" href ng-if="bracket.prev && bracket.prev.is_pool" ui-sref="events.event.divisions.division.pools.pooldetails.schedule({pool: bracket.prev.uuid})">
            <i class="fa fa-arrow-left"></i> 
            {{bracket.prev.name}}
        </a>
        <a class="prev-link" href ng-if="bracket.prev && !bracket.prev.is_pool" ui-sref="events.event.divisions.division.divisionbrackets.bracket({bracket: bracket.prev.uuid})">
            <i class="fa fa-arrow-left"></i> 
            {{bracket.prev.name}}
        </a>
        <a class="next-link" href ng-if="bracket.next && bracket.next.is_pool" ui-sref="events.event.divisions.division.pools.pooldetails.schedule({pool: bracket.next.uuid})">
            {{bracket.next.name}}
            <i class="fa fa-arrow-right"></i>
        </a>
        <a class="next-link" href ng-if="bracket.next && !bracket.next.is_pool" ui-sref="events.event.divisions.division.divisionbrackets.bracket({bracket: bracket.next.uuid})">
            {{bracket.next.name}}
            <i class="fa fa-arrow-right"></i>
        </a>
    </div>
</h3>

<brackets type="{{bracket.pool.flow_chart}}" matches="bracket.matches" pool="bracket.pool"></brackets>

<div class="bracket-bottom-text">
    <div>* &nbsp;- <i class="fa fa-flag"></i> Loser officiates next match on same court</div>
    <div>** - <i class="fa fa-flag"></i> Winner officiates next match on same court</div>
</div>