angular.module('SportWrench').controller('Public.Events.DivisionBracketsController', BracketsController);

BracketsController.$inject = ['$scope', '$rootScope', '$stateParams', 'eswService'];

function BracketsController($scope, $rootScope, $stateParams, eswService)
{
    $scope.brackets = undefined;

    $scope.byBracket = function (pool) {
        return pool.is_pool == 0 && pool.team_count >= 4;
    }

    eswService.getPools($stateParams.event, $stateParams.division, function(response) {
        $scope.brackets = [];
        if (response.data && response.data.length) {
            $scope.brackets = response.data.filter(function (bracket) {
                return bracket.is_pool == 0;
            });
        }

        $rootScope.pageTitle = 'Brackets ' + $rootScope.currentDivisionName;
    });
}
