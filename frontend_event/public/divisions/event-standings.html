<h3 class="esw_title">
  <div class="form-group div-short">
    <select class="form-control divisions-desktop spacer-mobile-sm-t" ng-model="currentDiv" ng-change="changeDivision()" ng-options="div.id as div.name for div in divisions">
    </select>
    <select class="form-control divisions-mobile spacer-mobile-sm-t" ng-model="currentDiv" ng-change="changeDivision()" ng-options="div.id as div.short_name for div in divisions">
    </select>
  </div>

  <span class="page-title">{{pageTitle}}</span>
</h3>

<div class="alert alert-warning" ng-if="isDivisionStandingsHidden()">
    Division is still playing
</div>
<div ng-if="!isDivisionStandingsHidden()">
    <h4 ng-if="isAllStandingsUnavailable()">No standings</h4>

    <div ng-repeat="(division_name, division_obj) in standings" ng-if="division_obj.length">
        <h4 class="text-center" ng-hide="isTitleHidden(division_name)">{{division_name === 'None' ? 'Pending' : division_name}}</h4>

        <div class="clearfix list-group-item">
            <table class="table table-hover future-table-desktop">
                <thead>
                <tr>
                    <td class="col-xs-1 text-center" ng-if="division_obj[0].rank"><a href ng-click="orderData('rank')">Finish</a></td>
                    <td class="col-xs-2 text-center" ng-if="!division_obj[0].rank && !hideSeeds"><a href ng-click="orderData('seed_original')">Current Seed</a></td>
                    <td class="col-xs-1 text-center" ng-if="division_obj[0].rank && division_obj[0].info && division_obj[0].info.points">Points</td>
                    <td class="col-xs-3"><a href ng-click="orderData('team_name')">Team Name</a></td>
                    <td class="col-xs-2 text-center">Match W-L</td>
                    <td class="col-xs-2 text-center">Set W-L</td>
                    <td class="col-xs-2 text-center"><a href ng-click="orderData('points_ratio')">Point Ratio</a></td>
                    <td class="col-xs-2 text-center" ng-show="!isDoubles"><a href ng-click="orderData('organization_code')">Code</a></td>
                    <td class="col-xs-1 text-center"></td>
                </tr>
                </thead>
                <tbody>
                <tr ng-repeat="standing in division_obj | orderBy:orderColumn:reverseSort" ui-sref="events.event.divisions.division.divisionteams.divisionteam({team: standing.roster_team_id})">
                    <td class="col-xs-1 text-center" ng-if="standing.rank"><b>{{standing.rank | suffix}}</b></td>
                    <td class="col-xs-1 text-center" ng-if="!standing.rank && standing.seed_current && !hideSeeds">{{standing.seed_current}}</td>
                    <td class="col-xs-1 text-center" ng-if="!standing.rank && !standing.seed_current && !hideSeeds">-</td>
                    <td class="col-xs-1 text-center" ng-if="showPointsColumn(standing)"><b>{{standing.info.points || 0}}</b></td>
                    <!-- <td class="col-xs-1 text-center" ng-if="!standing.rank && !(!!standing.matches_won || !!standing.matches_lost)">-</td> -->
                    <td class="col-xs-4 standings-team-name">
                        {{standing.team_name}}
                        <span ng-if="standing.show_accepted_bid" class="label label-success" style="margin: 3px">
                            Bid Earned <i class="fa fa-trophy" aria-hidden="true"></i>
                        </span>
                        <prev-qual-badge accepted-bid="standing.show_previously_accepted_bid"></prev-qual-badge>
                    </td>
                    <td class="col-xs-2 text-center">{{standing.matches_won || 0}}-{{standing.matches_lost || 0}}</td>
                    <td class="col-xs-2 text-center">
                        {{standing.sets_won}}-{{standing.sets_lost}}
                        <span ng-if="standing.sets_pct && standing.sets_pct < 10">({{standing.sets_pct | number:2}}%)</span>
                        <span ng-if="standing.sets_pct && standing.sets_pct >= 10">({{standing.sets_pct | number:0}}%)</span>
                    </td>
                    <td class="col-xs-2 text-center">{{standing.points_ratio | number:2}}</td>
                    <td class="col-xs-2 text-center" ng-show="!isDoubles">{{standing.organization_code}}</td>
                    <td class="col-xs-1 text-center"><span class="list-arrow glyphicon glyphicon-chevron-right"></span></td>
                </tr>
                </tbody>
            </table>

            <table class="table future-table-mobile">
                <thead>
                <tr>
                    <td><span class="standings-scores-data">Matches, Sets (%), Point Ratio </span></td>
                </tr>
                </thead>
            </table>

            <a class="list-group-item future-mobile-item" ng-repeat="standing in division_obj | orderBy:orderColumn:reverse" ui-sref="events.event.divisions.division.divisionteams.divisionteam({team: standing.roster_team_id})">
                <span class="list-arrow glyphicon glyphicon-chevron-right"></span>
                <div class="clearfix">
                    <span ng-if="standing.rank">{{standing.rank | suffix}}</span>
                    <span ng-if="!standing.rank && standing.seed_current && !hideSeeds">{{standing.seed_current | suffix}}</span>
                    <b>{{ standing.team_name }}</b>
                    <span ng-if="standing.show_accepted_bid" class="label label-success" style="margin: 3px">
                        Bid Earned <i class="fa fa-trophy" aria-hidden="true"></i>
                    </span>
                    <prev-qual-badge accepted-bid="standing.show_previously_accepted_bid"></prev-qual-badge>
                </div>
                <div class="clearfix standings-scores">
                    <div ng-hide="standing.matches_lost == '0' && standing.matches_won == '0'">
                        {{standing.matches_won}}-{{standing.matches_lost}},
                        {{standing.sets_won}}-{{standing.sets_lost}}
                        <span ng-if="standing.sets_pct < 10">({{standing.sets_pct | number:2}}%),</span>
                        <span ng-if="standing.sets_pct >= 10">({{standing.sets_pct | number:0}}%),</span>
                        {{standing.points_ratio | number:2}}
                    </div>
                    <span ng-show="standing.matches_lost == '0' && standing.matches_won == '0'" class="center-block text-center">No Scores Entered Yet</span>
                </div>
            </a>
        </div>
    </div>
</div>
