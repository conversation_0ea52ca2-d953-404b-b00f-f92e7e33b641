angular.module('SportWrench').controller('Public.Events.QualifiedController', QualifiedController);

QualifiedController.$inject = ['$http', '$scope', '$state', '$rootScope', '$stateParams', 'eswService', 'eventsList', 'currentEvent', 'currentDivision', 'currentStandings', 'jsonData'];

function QualifiedController($http, $scope, $state, $rootScope, $stateParams, eswService, eventsList, currentEvent, currentDivision, currentStandings, jsonData)
{
	$rootScope.pageTitle = "Qualified Teams";

	$scope.currentDiv = currentDivision.division_id;

	$scope.items = jsonData;

	$scope.divisions = eswService.getCurrentDivisions();

	$scope.changeDivision = function() {
		$state.go('events.event.divisions.division.qualified', {division: $scope.currentDiv});
	};

	$scope.byDivision = function() {
		return function(item) {
			console.log(item['BS Division'] == currentDivision.name)
			return item['BS Division'] == currentDivision.name;
		}
	};
}
