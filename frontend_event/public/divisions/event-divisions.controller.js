angular.module('SportWrench').controller('Public.Events.DivisionsController', eventDivisionsController);

eventDivisionsController.$inject = ['$scope', '$rootScope', '$stateParams', 'eswService', 'eventsList', 'currentEvent', 'divisionsList', '$state'];

function eventDivisionsController($scope, $rootScope, $stateParams, eswService, eventsList, currentEvent, divisionsList, $state)
{
    $rootScope.pageTitle = 'Divisions';

    $scope.title = currentEvent.name + ' Divisions';

    $scope.isSchedulePublished = currentEvent.schedule_published;

    $scope.divisions = divisionsList;

    $scope.hideStandings = (division) => {
        return currentEvent.teams_settings && currentEvent.teams_settings.hide_standings && !division.is_game_over;
    };

    $scope.goToState = function(state, division_id, e) {
    	e.preventDefault();
    	e.stopPropagation();
    	$state.go('events.event.divisions.division.' + state, {
    	    division: division_id
    	});
    };
}
