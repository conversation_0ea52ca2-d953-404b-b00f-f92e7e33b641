<h3 class="esw_title">
	<div class="form-group div-short">
		<select class="form-control divisions-desktop spacer-mobile-sm-t" ng-model="currentDiv" ng-change="changeDivision()" ng-options="div.id as div.name for div in divisions">
		</select>
		<select class="form-control divisions-mobile spacer-mobile-sm-t" ng-model="currentDiv" ng-change="changeDivision()" ng-options="div.id as div.short_name for div in divisions">
		</select>
	</div>

	<span class="page-title">{{pageTitle}}</span>
</h3>

<table class="table prevqual-table division-filtered">
	<thead>
		<tr>
			<td><b>BS Division</b></td>
			<td><b>Team Name</b></td>
			<td><b>Team Code</b></td>
			<td><b>Bid Earned</b></td>
			<td><b>Earned At</b></td>
		</tr>
	</thead>
	<tbody>
		<tr ng-repeat="item in items | filter:byDivision()" ng-class="{'line-red': item['Earned At'] == 'Big South'}">
			<td>{{ item['BS Division'] || '-' }}</td>
			<td>{{ item['Team Name']   || '-' }}</td>
			<td>{{ item['Team Code']   || '-' }}</td>
			<td>{{ item['Bid Earned']  || '-' }}</td>
			<td>{{ item['Earned At']   || '-' }}</td>
		</tr>		
	</tbody>
</table>
