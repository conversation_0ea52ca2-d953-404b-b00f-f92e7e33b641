<h3 class="esw_title">{{title}}</h3>

<div ng-show="divisions == undefined">
	<img class="loading-image" src="/images/loading-bubbles.svg" width="64" height="64" alt="">
</div>

<div class="list-group">
  <a ui-sref="events.event.divisions.division({division: div.division_id})" class="list-group-item" ng-repeat="div in divisions">
    <genders 
    m="div.gender === 'male'"
    f="div.gender === 'female' || div.gender === 'coed'"
    ></genders>
    {{div.name}} 
    <span class="division-links list-arrow">
    	<span class="list-arrow list-text-right-sm glyphicon glyphicon-chevron-right"></span>
  		<span class="fa fa-list-ol list-text-right-sm" ng-if="!hideStandings(div)" ng-click="goToState('divisionstandings', div.division_id, $event)" title="Standings"></span>
  		<!-- <span class="fa fa-sitemap fa-rotate-90 list-text-right-sm" ng-click="goToState('divisionbrackets', div.division_id, $event)" title="Brackets"></span> -->
      <span class="fa fa-sitemap fa-rotate-90 list-text-right-lt" ng-click="goToState('pools', div.division_id, $event)" title="Brackets"></span>
  		<span class="fa fa-list list-text-right-fst" ng-click="goToState('pools', div.division_id, $event)" title="Pools"></span>
  		<span class="fa fa-users list-text-right-sm" ng-if="div.teams_count" ng-click="goToState('divisionteams', div.division_id, $event)" title="Teams"></span>
    </span>
  </a>
</div>
