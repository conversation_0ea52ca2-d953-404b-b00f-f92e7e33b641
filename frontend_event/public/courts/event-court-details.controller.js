angular.module('SportWrench').controller('Public.Events.CourtDetailsController', CourtDetailsController);

CourtDetailsController.$inject = ['$scope', '$rootScope', '$stateParams', 'eswService', '$filter'];

function CourtDetailsController($scope, $rootScope, $stateParams, eswService, $filter)
{
    $scope.court_details = [];

    eswService.getEventCourtDetails($stateParams.event, $stateParams.court, function(response) {
        $scope.court_details = response.data;

        $scope.currentRound = $scope.court_details[0].r_name;

        $rootScope.pageTitle = "Court " + $scope.court_details[0].court_name + " Details";

        if ($scope.court_details) {
            $scope.court_details.forEach(function(match) {
                match.start = new Date(parseInt(match.date_start));
                try {
                    match.source = angular.fromJson(match.source);
                } catch (e) {
                    console.log('JSON Error: match.source = ', match.source);
                }
            });

            var rounds = $filter('unique')($scope.court_details, 'r_name');

            $scope.tabs = [];

            var current_date = moment(new Date()).utc().format('YYYY-MM-DD');

            rounds.forEach(function(round, i, arr) {
                if (i == arr.length - 1) {
                    var lastRnd = moment(+round.date_start).utc().format("YYYY-MM-DD");
                    if (moment(lastRnd).isBefore(current_date)) {
                        $scope.currentRound = round.r_name;
                        $scope.tabs.push({active: true});
                    }
                } else if (moment(+round.date_start).utc().format("YYYY-MM-DD") == current_date) {
                        $scope.currentRound = round.r_name;
                        $scope.tabs.push({active: true});
                } else {
                    $scope.tabs.push({active: false});
                }
            });
        }
    });

    $scope.setRound = function(round) {
        $scope.currentRound = round.r_name;
    }
}
