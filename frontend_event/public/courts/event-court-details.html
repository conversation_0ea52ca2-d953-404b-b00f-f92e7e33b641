<h3 class="esw_title">{{court_details[0].court_name}}</h3>

<tabset>
  <tab heading="{{round.r_short_name}} {{round.date_start | UTCdate: 'ddd'}}" ng-click="setRound(round)" active="tabs[$index].active" ng-repeat="round in court_details | unique: 'r_name' | orderBy: 'date_start'"></tab>
</tabset>

<br/>

<div ng-repeat="cd in court_details | filter: currentRound | unique: 'division' | orderBy: ['division']">

  <h3 ng-if="cd.division">{{ cd.division_short_name }} {{cd.r_short_name}} D{{cd.division}} - Starts {{cd.date_start | UTCdate: 'h:mm a ddd'}}</h3>

  <div ng-if="cd.division == match.division" class="list-group" ng-repeat="match in court_details | orderBy:'division' | filter: currentRound">
      <a ui-sref="events.event.matches.match({match: match.match_id})" class="list-group-item list-group-item-info clearfix">
        <span class="list-arrow glyphicon glyphicon-chevron-right"></span>
        <span class="list-text-right pull-right">{{ match.display_name }}</span>
        <div class="text-ellipsis">
          <i ng-show="match.match_type != 'ref'" class="fa fa-calendar"></i>
          <span class="underlined">{{ match.start | UTCdate: 'ddd h:mma' }} {{ match.court_name }}</span>
        </div>
      </a>
      <a ng-if="match.team1_roster_id" class="list-group-item clearfix" ui-sref="events.event.divisions.division.divisionteams.divisionteam.schedule({division: match.division_id, team: match.team1_roster_id})">
        <span class="list-arrow glyphicon glyphicon-chevron-right"></span>
        <span class="list-text-right pull-right hidden-xs">{{ match.team1_code }}</span>
        <span class="pull-left"><i class="fa fa-play-circle-o"></i> <b>{{ match.team1_name || match.source.team1.name }}</b> </span>
      </a>
      <span ng-if="!match.team1_roster_id" class="list-group-item clearfix">
        <i class="fa fa-play-circle-o"></i>
        <b ng-if="match.source.team1.type != 5">({{ match.source.team1.seed }}) {{ match.team1_name || match.source.team1.name }}</b>
        <b ng-if="match.source.team1.type == 5">({{ match.source.team1.seed }}) {{ match.team1_pool_name }}</b>
      </span>
      <a ng-if="match.team2_roster_id" class="list-group-item clearfix" ui-sref="events.event.divisions.division.divisionteams.divisionteam.schedule({division: match.division_id, team: match.team2_roster_id})">
        <span class="list-arrow glyphicon glyphicon-chevron-right"></span>
        <span class="list-text-right pull-right hidden-xs">{{ match.team2_code }}</span>
        <span class="pull-left"><i class="fa fa-play-circle-o"></i> <b>{{ match.team2_name || match.source.team2.name }}</b></span>
      </a>
      <span ng-if="!match.team2_roster_id" class="list-group-item clearfix">
        <i class="fa fa-play-circle-o"></i>
        <b ng-if="match.source.team2.type != 5">({{ match.source.team2.seed }}) {{ match.team2_name || match.source.team2.name }}</b>
        <b ng-if="match.source.team2.type == 5">({{ match.source.team2.seed }}) {{ match.team2_pool_name }}</b>
      </span>
      <a ng-if="match.ref_roster_id" class="list-group-item clearfix" ui-sref="events.event.divisions.division.divisionteams.divisionteam.schedule({division: match.division_id, team: match.ref_roster_id})">
        <span class="list-arrow glyphicon glyphicon-chevron-right"></span>
        <span class="list-text-right pull-right hidden-xs">Officiate</span>
        <span class="pull-left"><i class="fa fa-flag"></i> {{ match.ref_team_name || match.source.ref.name }}</span>
      </a>
      <span ng-if="!match.ref_roster_id" class="list-group-item clearfix" ng-repeat-end>
        <i class="fa fa-flag"></i>
        <span ng-if="match.source.ref.type != 5">{{ match.ref_name || match.source.ref.name }}</span>
        <span ng-if="match.source.ref.type == 5">{{ match.ref_pool_name }}</span>
      </span>      
  </div>

</div>