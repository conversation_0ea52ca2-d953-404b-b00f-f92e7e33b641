angular.module('SportWrench').controller('Public.Events.EventController', eventController);

eventController.$inject = [
    '$scope', '$stateParams', 'eswService', '$rootScope', '$localStorage',
    'eventsList', 'currentEvent', '$state', 'lastVisitedListService', 'UtilsService', 'ESW_NEW_URL'
];

function eventController (
    $scope, $stateParams, eswService, $rootScope, $localStorage, 
    eventsList, currentEvent, $state, lastVisitedListService, UtilsService, ESW_NEW_URL
) {   

    $scope.eventId = $stateParams.event; 
		$scope.newESWLink = ESW_NEW_URL + '/events/' + $scope.eventId;

	$scope.currentEvent = currentEvent || {};
    if(_.isEmpty($scope.currentEvent)) {
        $state.go('invalid-event');
    }

    lastVisitedListService.addLastVisited($scope.currentEvent);

    var today = (new Date()).valueOf();

    if ($scope.currentEvent && 
    	$scope.currentEvent.date_start < today && 
    	$scope.currentEvent.date_end > today) {
    	$localStorage.loadLastEvent = true;
    	$localStorage.lastEventEnds = $scope.currentEvent.date_end;
    	$localStorage.allowLastEventLoading = false;
    } else {
    	$localStorage.loadLastEvent = false;
    }

    if(currentEvent && currentEvent.tickets_published) {
        $scope.showTicketsLink = true;

        if(currentEvent.allow_point_of_sales) {
            $scope.ticketsLink = UtilsService.getTicketsSalesHubLink(currentEvent.point_of_sales_id);
        } else {
            $scope.ticketsLink = UtilsService.getTicketsDirectLink(currentEvent.tickets_code);
        }
    } else {
        $scope.showTicketsLink = false;
    }

	$rootScope.pageTitle = currentEvent.long_name;
	$localStorage.lastEventId = currentEvent.event_id;
	$localStorage.lastEvent = currentEvent;
	$localStorage.lastEventName = currentEvent.name;
	$scope.title = currentEvent.name;

	$rootScope.courtFilters = {
	    event_id: null,
	    event_day: null,
	    start_hour: 0,
	    hours_count: 3,
	    division: null
	};

	$scope.isSchedulePublished = currentEvent.schedule_published;

	$scope.hasTieBreakProcLink = (
		$stateParams.event == 21 ||				// 2015 Big South
		$stateParams.event == 'ca22c3ec6' ||
		$stateParams.event == 42 ||				// 2016 Big South
		$stateParams.event == '1674a8440' ||
        $stateParams.event == 17019 ||		    // 2017 Big South
        $stateParams.event == '2515a4cad' ||
        $stateParams.event == 18027 ||          // 2018 Mizuno Big South National Qualifier
        $stateParams.event == 'a66657943' ||
        $stateParams.event == 20053 ||          // 2020 Music City Volleyball Championships
        $stateParams.event == 'f0d90567a' ||
        $stateParams.event == 20026 ||          // OT MLK Showcase
        $stateParams.event == '73336abbd' ||
        $stateParams.event == 20052 ||          // 2020 Lil' Big South
        $stateParams.event == '346d6b153'
 		) ? true : false;

    $scope.tieBreakProcLink = 'data/events/' + currentEvent.id + '/tiebreakproc.pdf';

	$scope.isBSQ2015 = ($stateParams.event == 21 ||
                        $stateParams.event == 'ca22c3ec6') ? true : false;

    $scope.isBSQ2016 = ($stateParams.event == 42 ||
                        $stateParams.event == '1674a8440') ? true : false;

    $scope.isBSQ2017 = ($stateParams.event == 17019 ||
                        $stateParams.event == '2515a4cad') ? true : false;

    $scope.is41 = ($stateParams.event == 41 ||
                       $stateParams.event == '6e8524ede') ? true : false;

	$scope.hasPrevQual =  (
		$stateParams.event == 21 ||				// 2015 Big South
		$stateParams.event == 'ca22c3ec6' ||
		$stateParams.event == 42 ||				// 2016 Big South
		$stateParams.event == '1674a8440' ||
		$stateParams.event == 63 ||				// 2016 Boys' East Coast Championships
		$stateParams.event == '30494bfd6' ||
		$stateParams.event == 17012 ||			// 2017 Music City Volleyball Championships
		$stateParams.event == 'b68128ed8' ||
		$stateParams.event == 17019 ||			// 2017 Music City Volleyball Championships
		$stateParams.event == '2515a4cad' ||
        $scope.currentEvent.is_with_prev_qual
	);

    $rootScope.$storage.lastAthletes = false;
    $rootScope.$storage.lastStaffs = false;

    $scope.showFindByClub = function () {
    	return ($scope.currentEvent.registration_method !== 'doubles' && $rootScope.currentEvent && $scope.currentEvent.has_clubs);
    }

    $scope.showFindByManualClub = function () {
        return $scope.currentEvent.teams_settings.manual_club_names;
    }
}
