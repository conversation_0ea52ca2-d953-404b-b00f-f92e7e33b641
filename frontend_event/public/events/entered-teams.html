<div class="panel panel-default">
    <div class="panel-heading">
        <h4 class="panel-title">Entered Teams</h4>
    </div>
    <div class="panel-body">
        <spinner active="event_divisions === undefined"></spinner>
        <table class="table table-condensed" ng-if="event_divisions !== undefined">
            <thead>
                <tr>
                    <th></th>
                    <th>Gender</th>
                    <th>Division name</th>
                    <th>Registered</th>
                    <th>Accepted</th>
                </tr>
            </thead>
            <tbody>
                <tr class="division-line" ng-repeat-start="division in event_divisions" ng-click="checkDivision(division)">
                    <td>
                        <i ng-class="{ 'fa': true, 'fa-angle-double-down': division.checked, 'fa-angle-double-right': !division.checked }"></i>
                    </td>
                    <td>
                        <genders 
                        m="division.gender !== 'female'"
                        f="division.gender !== 'male'"
                        ></genders>
                    </td>
                    <td>
                        <a href="" ng-if="division.teams.length" ng-bind="division.division_name"></a>
                        <span ng-if="!division.teams.length" ng-bind="division.division_name"></span>
                    </td>
                    <td>{{division.entered_teams_count}}</td>
                    <td>{{division.accepted_teams_count}}</td>
                </tr>
                <tr ng-repeat-end ng-show="division.checked">
                    <td colspan="6" class="teams-in-division">
                        <div>
                            <table class="table table-condensed" ng-show="division.teams.length > 0">
                                <thead>
                                    <tr>
                                        <th>Team name</th>
                                        <th>Club name</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr ng-repeat="team in division.teams">
                                        <td>
                                            <b ng-if="team.status_entry == 12">{{team.team_name}}</b>
                                            <span ng-if="team.status_entry != 12">{{team.team_name}}</span>
                                        </td>
                                        <td>{{team.club_name}}</td>
                                    </tr>
                                </tbody>
                            </table>
                            <alert type="danger text-center" ng-if="emptyTeamsList(division)">There is no teams in this division yet</alert>
                        </div>
                    </td>
                </tr>
            </tbody>
        </table>
    </div>
</div>