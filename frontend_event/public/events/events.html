<h3 class="esw_title">Events</h3>
<div class="list-group">
    <div class="form-group">
        <div class="row">
            <div class="col-md-6 col-sm-8 col-xs-12">
                <input type="text" ng-model="search" class="form-control" placeholder="Find event...">
            </div>
        </div>
    </div>
    <spinner active="events == undefined"></spinner>
    <div ng-cloak ng-show="!events.length">No upcoming events...</div>
    <a 
        ui-sref="events.event({event:ev.event_id})" 
        class="list-group-item esw-event-list"
        ng-style="ev.current && {'background-color': '#dff0d8'}"
        ng-repeat="ev in events | filter: search | orderBy: '-date_start'">
        <div ng-class=" ev.current ? 'esw-event-lname' : 'esw-event-old' ">
            <span class="list-arrow glyphicon glyphicon-chevron-right"></span>
            <span class="list-text-right">
            <span class="events-city">{{ev.city}},</span> {{ev.state}}
            </span>
            <span class="esw-event-date">{{ev.date_start | date : 'MM/dd/yyyy'}}</span>
            <span class="esw-event-date-small">{{ev.date_start | date : 'MM/dd'}}</span>
            <span ng-class="{ 'fa fa-calendar' : ev.schedule_published }"></span>
            <span> {{ev.long_name}} </span>
        </div>
    </a>
</div>
