angular.module('SportWrench').controller('Public.Events.PoolDetailsFutureController', PoolDetailsController);

PoolDetailsController.$inject = ['$scope', '$rootScope', '$stateParams', 'eswService', 'currentDivision', 'currentPool', '$filter', '$state', '$window'];

function PoolDetailsController($scope, $rootScope, $stateParams, eswService, currentDivision, currentPool, $filter, $state, $window) {
    $scope.pool = currentPool;

    $scope.title = currentDivision.name + ' > ' + $scope.pool.display_name;
    $rootScope.pageTitle = $scope.title + " Future";

    $scope.hasFuture = function () {
        try {
            $scope.pool.pb_finishes = angular.fromJson($scope.pool.pb_finishes);
        } catch (e) {
            console.log('JSON Error: pb_finishes');
        }  

        return !_.isEmpty($scope.pool.pb_finishes)
    }; 
}
