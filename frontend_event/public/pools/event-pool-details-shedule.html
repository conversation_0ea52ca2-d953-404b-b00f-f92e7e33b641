<br/>
<div ng-show="pool.upcoming_matches == undefined && pool.upcoming_matches">
  <img class="loading-image" src="/images/loading-bubbles.svg" width="64" height="64" alt="">
</div>

<h4 ng-show="!pool.upcoming_matches.length">No upcoming matches</h4>
<a ui-sref="events.event.matches.match({match: match.match_id})" class="list-group-item list-group-item-info clearfix" ng-repeat-start="match in pool.upcoming_matches">
  <span class="list-arrow glyphicon glyphicon-chevron-right"></span>
  <span class="list-text-right pull-right team-name">{{ match.display_name }}</span>
  <div class="text-ellipsis">
    <i ng-show="match.match_type != 'ref'" class="fa fa-calendar"></i>
    {{ match.start | UTCdate: 'ddd h:mma' }} {{ match.court_name }}
  </div>
</a>

<a ng-if="match.team1_roster_id" class="list-group-item clearfix" ui-sref="events.event.divisions.division.divisionteams.divisionteam.schedule({club: $stateParams.division, team: match.team1_roster_id})">
  <span class="pull-left"><i class="fa fa-play-circle-o"></i> <b>{{ match.team1_name || match.source.team1.name }}</b> </span>
  <span class="list-arrow glyphicon glyphicon-chevron-right"></span>
  <span class="list-text-right pull-right hidden-xs">{{ match.team1_code }}</span>
</a>
<span ng-if="!match.team1_roster_id" class="list-group-item clearfix">
  <i class="fa fa-play-circle-o"></i>
  <b ng-if="match.source.team1.type != 5">({{ match.source.team1.seed }}) {{ match.team1_name || match.source.team1.name }}</b>
  <b ng-if="match.source.team1.type == 5">({{ match.source.team1.seed }}) {{ match.team1_pool_name }}</b>
</span>

<a ng-if="match.team2_roster_id" class="list-group-item clearfix" ui-sref="events.event.divisions.division.divisionteams.divisionteam.schedule({club: $stateParams.division, team: match.team2_roster_id})">
  <span class="pull-left"><i class="fa fa-play-circle-o"></i> <b>{{ match.team2_name || match.source.team2.name }}</b></span>
  <span class="list-arrow glyphicon glyphicon-chevron-right"></span>
  <span class="list-text-right pull-right hidden-xs">{{ match.team2_code }}</span>
</a>
<span ng-if="!match.team2_roster_id" class="list-group-item clearfix">
  <i class="fa fa-play-circle-o"></i>
  <b ng-if="match.source.team2.type != 5">({{ match.source.team2.seed }}) {{ match.team2_name || match.source.team2.name }}</b>
  <b ng-if="match.source.team2.type == 5">({{ match.source.team2.seed }}) {{ match.team2_pool_name }}</b>
</span>

<a ng-if="match.ref_roster_id" class="list-group-item clearfix" ui-sref="events.event.divisions.division.divisionteams.divisionteam.schedule({club: $stateParams.division, team: match.ref_roster_id})">
  <span class="pull-left"><i class="fa fa-flag"></i> {{ match.ref_team_name || match.source.ref.name }}</span>
  <span class="list-arrow glyphicon glyphicon-chevron-right"></span>
  <span class="list-text-right pull-right hidden-xs">Officiate</span>
</a>
<span ng-if="!match.ref_roster_id" class="list-group-item clearfix" ng-repeat-end>
  <i class="fa fa-flag"></i>
  <span ng-if="match.source.ref.type != 5">{{ match.ref_name || match.source.ref.name }}</span>
  <span ng-if="match.source.ref.type == 5">{{ match.ref_pool_name }}</span>
</span>