angular.module('SportWrench').controller('Public.Events.PoolDetailsStandingsController', PoolDetailsController);

PoolDetailsController.$inject = ['$scope', '$rootScope', '$stateParams', 'eswService', 'currentDivision', 'currentPool', '$filter', '$state', '$window'];

function PoolDetailsController($scope, $rootScope, $stateParams, eswService, currentDivision, currentPool, $filter, $state, $window) {
    $scope.pool = currentPool;

    $scope.title = currentDivision.name + ' > ' + $scope.pool.display_name;
    $rootScope.pageTitle = $scope.title + " Standings";

    $scope.orderData = function(colName) {
        $scope.orderColumn = colName;
        $scope.reverseSort = !$scope.reverseSort;
    };

    if ($scope.pool.standings) {
        try {
            if ($scope.pool.standings[0] != undefined && 
                $scope.pool.standings[0].pb_stats && 
                $scope.pool.standings[0].pb_stats['1st'] == undefined) {
                $scope.pool.standings[0].pb_stats = angular.fromJson($scope.pool.standings[0].pb_stats);
            }
        } catch (e) {
            console.log('JSON Error: currentStandings.pb_stats = ', $scope.pool.standings.pb_stats);
        }
    }

    $scope.hasStandings = function () {
        if (!$scope.pool.standings || $scope.pool.standings[0] == undefined) {
            return false;
        }
        return ($scope.pool.standings[0].pb_stats && Object.keys($scope.pool.standings[0].pb_stats).length !== 0);
    };

    $scope.checkPb_finishes = function () {
        return angular.isObject($scope.pool.pb_finishes) && Object.keys($scope.pool.pb_finishes).length ? true : false;
    };

    if ($scope.pool.standings) {
        try {
            if ($scope.pool.standings[0] != undefined && 
                $scope.pool.standings[0].pb_stats && 
                $scope.pool.standings[0].pb_stats['1st'] == undefined) {
                    $scope.pool.standings[0].pb_stats = angular.fromJson($scope.pool.standings[0].pb_stats);
            }

            $scope.pool.standing_keys = Object.keys($scope.pool.standings[0].pb_stats);
            console.log('standing keys:', $scope.pool.standing_keys)
            $scope.isRank = $scope.pool.standings[0].pb_stats['1st'].rank ? true : false;
            
            for (var i in $scope.pool.standings.pb_stats) {
                $scope.pool.standings.pb_stats[i].starting = i;
            }

            var isEveryRankZero = _.some($scope.pool.standings[0].pb_stats, function (stat) {
                return start.rank == 0;
            });

            if ($scope.isRank && isEveryRankZero === false) {
                $scope.orderColumn = 'rank';
            }

            $scope.num = function (str) {
                if(!str) return '';
                return $filter('number')(+str, 3);
            }
        } catch (e) {
            console.log('JSON Error: currentStandings.pb_stats = ', $scope.pool.standings.pb_stats);
        }
    }
    console.log('pool standings:', $scope.pool.standings)

    $scope.hasFutureMatches = false;

    if ($scope.pool.pb_finishes) {
        try {
            $scope.pool.pb_finishes = angular.fromJson($scope.pool.pb_finishes);
        } catch (e) {
            console.log('JSON Error: $scope.pool.pb_finishes = ', $scope.pool.pb_finishes);
        }
        delete $scope.pool.pb_finishes.team_ids;

        for (var future in $scope.pool.pb_finishes) {
            if ($scope.pool.pb_finishes[future].next_match && Object.keys($scope.pool.pb_finishes[future].next_match).length) {
                $scope.hasFutureMatches = true;
                console.log($scope.hasFutureMatches);
                $scope.pool.pb_finishes[future].next_match.start = new Date(parseInt($scope.pool.pb_finishes[future].next_match.secs_start*1000));
            }
            if ($scope.pool.pb_finishes[future].next_ref && Object.keys($scope.pool.pb_finishes[future].next_ref).length) {
                $scope.pool.pb_finishes[future].next_ref.start = new Date(parseInt($scope.pool.pb_finishes[future].next_ref.secs_start*1000));
            }
        }
    }

}
