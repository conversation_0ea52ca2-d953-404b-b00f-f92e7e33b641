<h3 class="esw_title">
  <div class="form-group div-short">
    <select class="form-control divisions-desktop spacer-mobile-sm-t" ng-model="currentDiv" ng-change="changeDivision()" ng-options="div.id as div.name for div in divisions">
    </select>
    <select class="form-control divisions-mobile spacer-mobile-sm-t" ng-model="currentDiv" ng-change="changeDivision()" ng-options="div.id as div.short_name for div in divisions">
    </select>
  </div>

  <span class="page-title">{{pageTitle}}</span>
</h3>

<tabset>
  <tab heading="{{round.r_short_name}} {{round.date_start | UTCdate: 'ddd'}}"
       ng-click="setRound(round)"
       active="tabs[$index].active"
       ng-repeat="round in pools | unique: 'unique_id' | orderBy: ['date_start']"
      >
  </tab>
</tabset>

<div class="list-group">
  <div ng-show="pools == undefined">
    <img class="loading-image" src="/images/loading-bubbles.svg" width="64" height="64" alt="">
  </div>
  <span ng-show="!pools.length && pools != undefined">No teams yet</span>

  <div ng-repeat="p in getFilteredPools(pools) | orderBy: ['rr_sort_priority', 'r_sort_priority', 'sort_priority'] | unique: 'unique_id_in_pool'">
      <div class="pool-brackets-headings">
          <div class="row">
              <div class="col-xs-5 col-sm-4">
                  <h3>{{p.r_name}} <span ng-if="p.rr_name">{{p.rr_name}}</span>
                    <span class="esw-nav pool-title-link" ng-if="$first">
                    <i class="fa fa-indent"></i>
                    <a href ng-click="$parent.$parent.showTeams = !$parent.$parent.showTeams">
                      <span ng-if="!$parent.$parent.showTeams">Show</span><span ng-if="$parent.$parent.showTeams">Hide</span> Teams
                    </a>
                  </span>
                  </h3>
              </div>
              <div class="col-xs-7 col-sm-5" ng-if="!pool.is_empty_pb_stats">
                  <div class="row text-right pb-stats-headings">
                      <div class="col-xs-4"><b>Match W-L</b></div>
                      <div class="col-xs-4"><b>Set W-L</b></div>
                      <div class="col-xs-4"><b>Point Ratio</b></div>
                  </div>
              </div>
          </div>
      </div>

    <span ng-show="!pools.length">Teams are not assigned yet</span>

    <div class="list-group" ng-repeat="pool in pools | orderBy: ['r_sort_priority', 'rr_sort_priority', 'sort_priority']" ng-if="pool.unique_id_in_pool == p.unique_id_in_pool">
      <a ui-sref="events.event.divisions.division.pools.pooldetails.schedule({pool: pool.uuid})" class="list-group-item list-group-item-info" ng-if="pool.is_pool">
        <span class="clip">
          <span class="fa fa-list-ol"></span> 
          <span class="underlined">
            <span class="pool-stars underlined">{{ pool.division_short_name }}</span> 
            <b>{{ pool.display_name }}</b> - {{ pool.pb_name }} - <span class="pool-stars underlined">Starts</span> {{pool.date_start | UTCdate: 'h:mma ddd'}} {{pool.court_start}} - {{pool.matches_played_exp}}
          </span>
        </span>
        <span class="pool-icons absolute-right-icon">
          <span class="glyphicon glyphicon-chevron-right"></span>
        </span>
      </a> 
      <a ui-sref="events.event.divisions.division.divisionbrackets.bracket({bracket: pool.uuid})" class="list-group-item list-group-item-info" ng-if="!pool.is_pool">
        <span class="clip">
          <span class="fa fa-sitemap fa-rotate-90"></span>
          <span class="underlined">
            <span class="pool-stars underlined">{{ pool.division_short_name }}</span>
            <b>{{ pool.display_name }}</b> - {{ pool.pb_name }} - <span class="pool-stars underlined">Starts</span> {{pool.date_start | UTCdate: 'h:mma ddd'}} {{pool.court_start}}
          </span>
        </span>
        <span class="pool-icons absolute-right-icon">
          <span class="glyphicon glyphicon-chevron-right"></span>
        </span>
      </a>
      <div class="list-group" ng-show="showTeams">
        <span class="list-group-item" ng-show="!pool.teams.length">Teams are not assigned yet</span>
        <!-- <span ng-show="!pool.teams.length">Teams are not assigned yet</span> -->
        <a class="list-group-item" ng-repeat="team in pool.teams | orderBy: sortTeamsBy()" ui-sref="events.event.divisions.division.divisionteams.divisionteam.schedule({team: team.opponent_team_id})">
            <div class="row">
                <div class="{{teamNameClass(team)}} col-sm-4">
                    <span ng-if="team.rank && p.r_short_name == 'R1'">({{team.rank | suffix}})</span>
                    <b>{{team.opponent_team_name}}</b>
                    <prev-qual-badge accepted-bid="team.show_previously_accepted_bid"></prev-qual-badge>
                </div>
                <div class="col-xs-12 col-sm-3 list-text-right-strict pull-right hidden-xs text-right">
                    <span class="span-right-strict">{{team.opponent_organization_code}}</span>
                    <span class="list-arrow glyphicon glyphicon-chevron-right"></span>
                </div>
                <div class="{{pbStatsClass(team)}} col-xs-7 col-sm-5 pull-right" ng-if="!team.is_empty_team_pb_stats">
                  <div class="row text-right">
                      <div ng-if="team.matches_won && team.matches_lost" class="col-xs-4">
                    {{team.matches_won}}-{{team.matches_lost}}
                  </div>
                      <div ng-if="team.sets_won && team.sets_lost" class="col-xs-4">
                    {{team.sets_won}}-{{team.sets_lost}}
                  </div>
                      <div ng-if="team.points_ratio" class="col-xs-4">{{team.points_ratio}}</div>
                  </div>
                </div>
            </div>
        </a>            
      </div>
    </div>

  </div>
</div>
