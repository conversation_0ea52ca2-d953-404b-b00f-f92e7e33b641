angular.module('SportWrench').controller('Public.Events.PoolsController', PoolsController);

PoolsController.$inject = ['$scope', '$rootScope', '$state', '$stateParams', 'eswService', '$filter', 'divisionsList', 'currentDivision', 'currentPools', '$localStorage', 'currentEvent'];

function PoolsController($scope, $rootScope, $state, $stateParams, eswService, $filter, divisionsList, currentDivision, currentPools, $localStorage, currentEvent)
{
    $rootScope.pageTitle = 'Pools & Brackets';

    $scope.currentDiv = currentDivision.division_id;

    $scope.divisions = eswService.getCurrentDivisions();

    $scope.changeDivision = function() {
        $state.go('events.event.divisions.division.pools', {division: $scope.currentDiv});
    };

    $scope.showTeams = $localStorage.showPoolTeams || true;
    $scope.currentEvent = currentEvent;
    $scope.sortTeamsBy = function () {
        let defaultSort = 'info.seed_current';

        if(currentEvent && currentEvent.teams_settings && currentEvent.teams_settings.sort_by) {
            switch(currentEvent.teams_settings.sort_by) {
                case 'team_name':
                    return 'opponent_team_name';
                case 'seed_current':
                default:
                    return defaultSort;
            }
        } else {
            return defaultSort;
        }
    };

    $scope.$watch('showTeams', function() {
        $localStorage.showPoolTeams = $scope.showTeams;
    });

    divisionsList = $filter('orderBy')(divisionsList, 'name');

    divisionsList.forEach(function(division, i, arr) {
        if ($stateParams.division == division.division_id) {
            $scope.short_name = division.short_name;
            if (i > 0) {
                $scope.prev = {
                    id: arr[i-1].division_id,
                    name: arr[i-1].short_name
                };
            }
            if (i < divisionsList.length - 1) {
                $scope.next = {
                    id: arr[i+1].division_id,
                    name: arr[i+1].short_name
                };
            }
        }
    });

    $scope.pools = currentPools;

    $scope.pools.forEach(function(team) {
        if (isNaN(parseInt(team.date_start))) {
            team.date_start = new Date(parseInt(team.date_start));
        } else {
            let poolDate  = moment.utc(+team.date_start).date();
            let roundName = team.r_name;

            team.unique_id = poolDate + roundName;

            team.unique_id_in_pool = team.unique_id + team.rr_short_name;
        }

        if (team.settings) {
            team.settings = angular.fromJson(team.settings);

            if (!team.settings['PlayAllSets']) {
                team.matches_played_exp = 'Best of ' + team.settings['SetCount'];
            } else {
                team.matches_played_exp = team.settings['SetCount'] + ' to ' + team.settings['WinningPoints'];
            }
        }
    });

    if ($scope.pools[0] && $scope.pools[0].unique_id) {
        const orderedByDatePulls =  $filter('orderBy')($scope.pools, 'date_start');
        const index = orderedByDatePulls.findIndex(p => !!p.unique_id);
        $scope.currentRound = orderedByDatePulls[index].unique_id;
    }

    var rounds = $filter('unique')($scope.pools, 'unique_id');

    $scope.tabs = [];

    var current_date = moment(new Date()).utc().format('YYYY-MM-DD');

    $filter('orderBy')(rounds, 'date_start').forEach(function(round, i, arr) {
        if ($rootScope.savedRound) {
            if (round.unique_id == $rootScope.savedRound) {
                $scope.currentRound = round.unique_id;
                $scope.tabs.push({active: true});
            } else {
                $scope.tabs.push({active: false});
            }
        } else if (i == arr.length - 1 && moment.utc(+round.date_start).format("YYYY-MM-DD") != current_date) {
            var lastRnd = moment(+round.date_start).utc().format("YYYY-MM-DD");
            if (moment(lastRnd).isBefore(current_date)) {
                $scope.currentRound = round.unique_id;
                $scope.tabs.push({active: true});
            }
        } else if (moment.utc(+round.date_start).format("YYYY-MM-DD") == current_date) {
            $scope.currentRound = round.unique_id;
            $scope.tabs.push({active: true});
        } else {
            $scope.tabs.push({active: false});
        }
    });

    var isAnyActiveTab = $scope.tabs.some(function (tab) {
        return tab.active === true;
    });

    if (isAnyActiveTab === false && $scope.tabs[0]) {
        $scope.tabs[0].active = true;
    }

    $scope.byPool = function (pool) {
        return pool.is_pool === 1;
    }

    $scope.byBracket = function (pool) {
        return pool.is_pool === 0;
    }

    $scope.setRound = function(round) {
        $scope.currentRound = round.unique_id;
        $rootScope.savedRound = $scope.currentRound;
    }

    $scope.getFilteredPools = function (pools) {
        return pools.filter(pool => pool.unique_id === $scope.currentRound);
    }

    $scope.teamNameClass = function (team) {
        return !_.isString(team.matches_won) || team.opponent_team_name.length > 11 ? 'col-xs-12' : 'col-xs-5';
    }

    $scope.pbStatsClass = function (team) {
        return !_.isString(team.matches_won) || team.opponent_team_name.length > 11 ? 'col-xs-offset-5 col-sm-offset-0' : '';
    }
}
