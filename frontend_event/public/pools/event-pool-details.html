<h3 class="esw_title">
	<span class="desktop-layout">{{divisionName}}</span><span class="mobile-layout">{{divisionShortName}}</span> > {{pool.display_name}}
	<div class="esw-nav">
		<a class="prev-link" href ng-if="pool.prev && pool.prev.is_pool" ui-sref="events.event.divisions.division.pools.pooldetails.schedule({pool: pool.prev.uuid})">
			<i class="fa fa-arrow-left"></i> 
			{{pool.prev.name}}
		</a>
		<a class="prev-link" href ng-if="pool.prev && !pool.prev.is_pool" ui-sref="events.event.divisions.division.divisionbrackets.bracket({bracket: pool.prev.uuid})">
			<i class="fa fa-arrow-left"></i> 
			{{pool.prev.name}}
		</a>
		<a class="next-link" href ng-if="pool.next && pool.next.is_pool" ui-sref="events.event.divisions.division.pools.pooldetails.schedule({pool: pool.next.uuid})">
			{{pool.next.name}}
			<i class="fa fa-arrow-right"></i>
		</a>
		<a class="next-link" href ng-if="pool.next && !pool.next.is_pool" ui-sref="events.event.divisions.division.divisionbrackets.bracket({bracket: pool.next.uuid})">
			{{pool.next.name}}
			<i class="fa fa-arrow-right"></i>
		</a>
	</div>
</h3>

<pool-wide></pool-wide>

<div class="pool-details-tabs">
  
  <tabset>
    <tab ng-show="hasShedule" active="tabs[0].active" ng-click="setTab('schedule')">
      <tab-heading>
        <i class='fa fa-clock-o'></i> Schedule
      </tab-heading>
    </tab>
    <tab ng-show="hasResults" active="tabs[1].active" ng-click="setTab('results')">
      <tab-heading>
        <i class='fa fa-list-ol'></i> Results
      </tab-heading>
    </tab>
    <tab ng-show="hasStandings()" active="tabs[2].active" ng-click="setTab('standings')">
      <tab-heading>
        <i class='fa fa-sort-numeric-asc'></i> Standings
      </tab-heading>
    </tab>
    <tab ng-show="hasFuture()" active="tabs[3].active" ng-click="setTab('future')">
      <tab-heading>
        <i class='fa fa-binoculars'></i> Future
      </tab-heading>
    </tab>
  </tabset>

  <div class="list-group" ui-view></div>

</div>


<div class="pool-details-wide">

  <h4 class="text-primary" ng-show="hasShedule"><i class='fa fa-clock-o'></i> Schedule</h4>

  <div ng-show="pool.upcoming_matches == undefined && pool.upcoming_matches">
    <img class="loading-image" src="/images/loading-bubbles.svg" width="64" height="64" alt="">
  </div>

  <table class="table table-hover pool-detail-results" ng-show="pool.upcoming_matches.length">
    <thead>
      <tr>
        <td class="text-center">
          <span>Team 1</span>
        </td>
        <td class="text-center">
          <span>Team 2</span>
        </td>
        <td class="text-center">Officiating Team</td>
        <td class="text-center">Court & Time</td>
      </tr>      
    </thead>
    <tbody>
    <tr ui-sref="events.event.matches.match({match: match.match_id})" ng-repeat="match in pool.upcoming_matches">
      <td class="col-1">
        <span class="clearfix" style="padding: 5px 0;">
          <span class="list-arrow link-arrow glyphicon glyphicon-chevron-right"></span>
          <b>{{ match.team1_name || match.source.team1.name }}</b>
        </span>
      </td>

      <td class="col-2">
        <span class="clearfix" style="padding: 5px 0;">
          <span class="list-arrow link-arrow glyphicon glyphicon-chevron-right"></span>
          <b>{{ match.team2_name || match.source.team2.name }}</b>
        </span>
      </td>

      <td class="col-3">
        <span class="clearfix" style="padding: 5px 0;">{{ match.ref_team_name || match.source.ref.name }}</span>
      </td>

      <td class="col-4">
        <span class="clearfix" style="padding: 5px 0;">{{ match.date_start | UTCdate: 'ddd h:mma' }} {{match.court_name}}</span>
      </td>
    </tr>
    </tbody>
  </table>



  <h4 class="text-primary" ng-show="hasResults"><i class='fa fa-list-ol'></i> Results</h4>

  <!-- <strong class="list-group-item" ng-show="pool.results">Results</strong> -->
  <!-- <h4 ng-show="!pool.results.length">No results</h4> -->

  <table class="table table-hover pool-detail-results" ng-show="pool.results">
    <thead>
      <tr>
        <td class="text-center">
          <!--i class="fa fa-smile-o"></i--> 
          <span>Team 1</span>
        </td>
        <td class="text-center">
          <!--i class="fa fa-frown-o"></i--> 
          <span>Team 2</span>
        </td>
        <td class="text-center">Score</td>
      </tr>      
    </thead>
    <tbody>
    <tr ui-sref="events.event.matches.match({match: results.match_id})" ng-repeat="results in pool.results">
        <td class="col-1">
            <span class="clearfix" style="padding: 5px 0;">
              <span class="list-arrow link-arrow glyphicon glyphicon-chevron-right"></span>
              <b>{{ (results.results.winner == 2)?results.team2_name:results.team1_name }}</b>
            </span>
        </td>
        <td class="col-2">
            <span class="clearfix" style="padding: 5px 0;">
              <span class="list-arrow link-arrow glyphicon glyphicon-chevron-right"></span>
              <b>{{ (results.results.winner == 2)?results.team1_name:results.team2_name }}</b>
            </span>
        </td>
        <td class="col-5">
            <span class="clearfix" style="padding: 5px 0;">
              <span class="list-arrow glyphicon glyphicon-chevron-right"></span>
              {{ (results.results.winner == 2)?results.results.team2.scores:results.results.team1.scores }}
            </span>
        </td>
    </tr>
    </tbody>
  </table>


  <h4 class="text-primary" ng-show="hasStandings()"><i class='fa fa-sort-numeric-asc'></i> Standings</h4>

  <div class="spacer-sm-t spacer-sm-l text-primary" ng-if="!isRank"><b>** This pool is still in process **</b></div>
  <div class="spacer-sm-t spacer-sm-l text-primary" ng-if="isRank"><b>This pool is finished</b></div>

  <span ng-show="pool.standings[0].pb_stats" class="clearfix list-group-item future-items">
    <table class="table table-hover future-table-desktop">
      <thead>
        <tr>
          <td ng-if="isRank" class="col-xs-1">
            <span>&nbsp;</span>
          </td>
          <td class="col-xs-3">Team Name</td>
          <td class="col-xs-2 text-center" ng-hide="pool.hide_winner">Match W-L</td>
          <td class="col-xs-2 text-center">Set W-L</td>
          <td class="col-xs-2 text-center">Point Ratio</td>
          <td class="col-xs-1 text-center"></td>
        </tr>
      </thead>
      <tbody>
        <tr ng-repeat="(key, standing) in pool.standings[0].pb_stats | toArray | orderBy:orderColumn" ng-click="ifAvailableGoTo(standing, 'events.event.divisions.division.divisionteams.divisionteam.schedule')">
          <td class="col-xs-1" ng-if="standing.rank">
            <b>
              <span>{{(standing.rank | suffix)}}</span>
            </b>
          </td>
          <td class="col-xs-3 standings-team-name">{{standing.name}}</td>
          <td class="col-xs-2 text-center" ng-hide="pool.hide_winner">{{standing.matches_won}}-{{standing.matches_lost}}</td>
          <td class="col-xs-2 text-center">
            {{standing.sets_won}}-{{standing.sets_lost}} 
            <span ng-if="standing.sets_pct < 10">({{standing.sets_pct | number:2}}%)</span>
            <span ng-if="standing.sets_pct >= 10">({{standing.sets_pct | number:0}}%)</span>
          </td>
          <td class="col-xs-2 text-center">{{standing.points_lost ? num(standing.points_ratio) : "" }}</td>
          <td class="col-xs-1 text-center"><span class="list-arrow glyphicon glyphicon-chevron-right"></span></td>
        </tr>
      </tbody>
    </table>

    <table class="table future-table-mobile">
      <thead>
        <!-- <tr>
          <td class=""><span ng-if="isRank">Rank</span> Team Name</td>
        </tr> -->
        <tr>
          <td class="col-xs-12"><span class="standings-scores-data">Matches, Sets (%), Point Ratio</span></td>
        </tr>
      </thead>
    </table>
  </span>

  <a ng-show="pool.standings[0].pb_stats" class="list-group-item future-mobile-item" ng-repeat="(key, standing) in pool.standings[0].pb_stats | orderObjectBy:'rank':true | orderBy:orderColumn:reverseSort" ui-sref="events.event.divisions.division.divisionteams.divisionteam.schedule({team: standing.team_id})">
    <span class="list-arrow glyphicon glyphicon-chevron-right"></span>
    <div class="clearfix">
        <b>
            <span ng-if="standing.rank">{{standing.rank | suffix}}</span>
        </b> {{ standing.name }}
    </div>
    <div class="clearfix standings-scores">
      <div ng-hide="standing.matches_lost == '0' && standing.matches_won == '0'">
        <table class="table table-condensed future-table-mobile">
          <tbody>
            <tr>
              <td class="col-xs-12">
                <span class="standings-scores-data">
                  {{standing.matches_won}}-{{standing.matches_lost}},
                  {{standing.sets_won}}-{{standing.sets_lost}}
                  <span ng-if="standing.sets_pct < 10">({{standing.sets_pct | number:2}}%),</span>
                  <span ng-if="standing.sets_pct >= 10">({{standing.sets_pct | number:0}}%),</span>
                  {{num(standing.points_ratio) | number:2}}
                </span>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
      <span ng-show="standing.matches_lost == '0' && standing.matches_won == '0'" class="center-block text-center">No Scores Entered Yet</span>
    </div>
  </a>
  
  <future-matches
    matches="pool.pb_finishes"
    stats="pool.pb_stats"
  ></future-matches>
</div>
