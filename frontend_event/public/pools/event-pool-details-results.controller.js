angular.module('SportWrench').controller('Public.Events.PoolDetailsResultsController', PoolDetailsController);

PoolDetailsController.$inject = ['$scope', '$rootScope', '$stateParams', 'eswService', 'currentDivision', 'currentPool', '$filter', '$state', '$window'];

function PoolDetailsController($scope, $rootScope, $stateParams, eswService, currentDivision, currentPool, $filter, $state, $window) {
    $scope.pool = currentPool;

    $scope.title = currentDivision.name + ' > ' + $scope.pool.display_name;
    $rootScope.pageTitle = $scope.title + " Results";

    if ($scope.pool.results) {
        $scope.pool.results.forEach(function(res) {
            try {
                res.results = angular.fromJson(res.results);

                if (res.results.team1 && 
                    res.results.team1.scores && 
                    res.results.team1.scores.indexOf(',undefined-undefined') != -1) {
                    res.results.team1.scores = res.results.team1.scores.replace(',undefined-undefined', '');
                }

                if (res.results.team2 && 
                    res.results.team2.scores && 
                    res.results.team2.scores.indexOf(',undefined-undefined') != -1) {
                    res.results.team2.scores = res.results.team2.scores.replace(',undefined-undefined', '');
                }
            } catch (e) {
                console.log('JSON Error: res.results = ', res.results);
            }
        });
    }

    $scope.hasFutureMatches = false;

    if ($scope.pool.pb_finishes) {
        try {
            $scope.pool.pb_finishes = angular.fromJson($scope.pool.pb_finishes);
        } catch (e) {
            console.log('JSON Error: $scope.pool.pb_finishes = ', $scope.pool.pb_finishes);
        }
        delete $scope.pool.pb_finishes.team_ids;

        for (var future in $scope.pool.pb_finishes) {
            if ($scope.pool.pb_finishes[future].next_match && Object.keys($scope.pool.pb_finishes[future].next_match).length) {
                $scope.hasFutureMatches = true;
                console.log($scope.hasFutureMatches);
                $scope.pool.pb_finishes[future].next_match.start = new Date(parseInt($scope.pool.pb_finishes[future].next_match.secs_start*1000));
            }
            if ($scope.pool.pb_finishes[future].next_ref && Object.keys($scope.pool.pb_finishes[future].next_ref).length) {
                $scope.pool.pb_finishes[future].next_ref.start = new Date(parseInt($scope.pool.pb_finishes[future].next_ref.secs_start*1000));
            }
        }
    }

}
