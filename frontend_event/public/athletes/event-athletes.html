<h3 class="esw_title">Find By Athlete</h3>
<div class="row">
	<div class="form-group col-md-3">
		<input type="text" ng-model="search.first" class="form-control" placeholder="Search by first name">
	</div>
	<div class="form-group col-md-3">
		<input type="text" ng-model="search.last" class="form-control" placeholder="Search by last name">
	</div>
	<div class="form-group col-md-3">
		<input type="text" ng-model="search.team" class="form-control" placeholder="Search by team name, code">
	</div>
    <div class="form-group col-md-3">
        <input type="text" ng-model="search.state" class="form-control" placeholder="Search by club state">
    </div>
	<div class="form-group col-md-3">
		<button type="button" class="btn btn-default" ng-click="search()">Search</button>
	</div>
</div>
<div class="row">
	<div class="form-group col-md-12">
		<div ng-show="athletes && !athletes.length">0 athletes</div>
		<div ng-show="athletes == undefined">
			<img class="loading-image" src="/images/loading-bubbles.svg" width="64" height="64" alt="">
		</div>
		<div class="list-group">
		  <a class="list-group-item" ng-repeat="athlete in athletes" ng-click="goToAthlete(athlete)">
		    <span class="staff-item">
		    	<span ng-if="athlete.jersey">#{{athlete.jersey}}</span> <b>{{athlete.last}}, {{athlete.first}}</b> - {{athlete.team_name}}
                <span style="cursor: text"
                      ng-if="clipboardSupported"
                      ng-click="$event.stopPropagation()"
                      text="athlete.team_organization_code"
                      clipboard
                      on-copied="onSuccessCopy()"
                      on-error="onErrorCopy(err)"
                >({{athlete.team_organization_code}})</span>
		    </span>
            <span ng-if="!clipboardSupported">
                ({{athlete.team_organization_code}})
            </span>
		    <span class="pull-right staff-team-code">
		      <span class="list-arrow glyphicon glyphicon-chevron-right"></span>
		      <span class="list-text-right">{{athlete.state}}</span>
		    </span>
		  </a>
		</div>
	</div>
</div>
