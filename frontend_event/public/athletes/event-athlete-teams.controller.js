angular.module('SportWrench').controller('Public.Events.AthleteTeamsController', athleteTeamsController);

athleteTeamsController.$inject = ['$scope', '$rootScope', '$stateParams', 'eswService'];

function athleteTeamsController($scope, $rootScope, $stateParams, eswService)
{
    $scope.teams = [];

    $rootScope.pageTitle = 'Athlete teams';

    eswService.getEventClubTeams($stateParams.event, $stateParams.athlete, function(response) {
        $scope.teams = response.data;
    });
}
