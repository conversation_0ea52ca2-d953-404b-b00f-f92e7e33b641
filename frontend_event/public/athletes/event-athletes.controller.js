angular.module('SportWrench').controller('Public.Events.AthletesController', eventAthletesController);

eventAthletesController.$inject = ['$scope', '$rootScope', '$stateParams', 'eswService', '$state', 'toastr', 'clipboard'];

function eventAthletesController($scope, $rootScope, $stateParams, eswService, $state, toastr, clipboard)
{
    $scope.athletes = $rootScope.$storage.lastAthletes || false;

    $rootScope.pageTitle = 'Athletes';

    $scope.search = function() {
    	$scope.athletes = undefined;

    	eswService.getEventAthletes($stateParams.event, $scope.search)
            .then(response => {
                $scope.athletes = response.data;
                $rootScope.$storage.lastAthletes = $scope.athletes;
            });
    };

    $scope.clipboardSupported = clipboard.supported;

    $scope.onSuccessCopy = function () {
        toastr.success('Copied team code to clipboard');
    };

    $scope.onErrorCopy = function (err) {
        toastr.error('Error: ' + err);
    };

    $scope.goToAthlete = function (athlete) {
        $state.go('events.event.divisions.division.divisionteams.divisionteam', {team: athlete.roster_team_id, division: athlete.division_id})
    }
}
