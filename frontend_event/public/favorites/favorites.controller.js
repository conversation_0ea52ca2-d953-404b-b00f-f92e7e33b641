angular.module('SportWrench').controller('Public.Events.Favorites', Favorites);

Favorites.$inject = ['$scope', '$stateParams', '$localStorage', '$rootScope', '$modalInstance', '$location'];

function Favorites($scope, $stateParams, $localStorage, $rootScope, $modalInstance, $location) {
    
    $scope.hasTitle = function(fav) {
        return function(fav) {
            return fav.title ? true : false;
        };
    }

    $scope.uniqFavorites =_.uniq($rootScope.$storage.favorites, function(item) {
        return item.event;
    });

    $modalInstance.result.then(function (selectedItem) {
    }, function () {
    });

    $scope.addCurrent = function() {
    	$rootScope.$storage.favorites.push({
    	    url: $location.$$url,
    	    event: $stateParams.event,
            eventName: $localStorage.lastEventName,
    	    title: $rootScope.pageTitle
    	});
    	$rootScope.isFavorite = true;

        $modalInstance.dismiss('cancel');
    };

    $scope.removeCurrent = function() {
        for (var i=0; i < $rootScope.$storage.favorites.length; i++) {
            if ($rootScope.$storage.favorites[i] && $rootScope.$storage.favorites[i].url) {
                if ($rootScope.$storage.favorites[i].url == $location.$$url) {
                    $rootScope.$storage.favorites.splice(i, 1);
                    $rootScope.isFavorite = false;
                    break;
                }
            }
        }

        $modalInstance.dismiss('cancel');
    };

    $scope.remove = function(fav) {
        event.preventDefault();
        for (var i=0; i < $rootScope.$storage.favorites.length; i++) {
            if ($rootScope.$storage.favorites[i] && $rootScope.$storage.favorites[i].url) {
                if ($rootScope.$storage.favorites[i] == fav) {
                    $rootScope.$storage.favorites.splice(i, 1);
                    $rootScope.isFavorite = false;
                    break;
                }
            }
        }
    };

    $scope.cancel = function() {
    	$modalInstance.dismiss('cancel');
    };

}
