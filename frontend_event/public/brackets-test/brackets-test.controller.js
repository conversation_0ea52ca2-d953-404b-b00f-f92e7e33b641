angular.module('SportWrench').controller('BracketsTestController', bracketsTestController);

bracketsTestController.$inject = ['$scope', '$rootScope', '$filter'];

function bracketsTestController($scope, $rootScope, $filter)
{
    var matches_count = 100;
    var tmp_matches = [];
    for (var i = 0; i < matches_count; i++) {
        var d = new Date();
        var date = $filter('UTCdate')(new Date(d.getFullYear(), d.get<PERSON>onth(), d.getUTCDate(), 12, i), 'ddd h:mma');

        tmp_matches.push({
            court_name: "court_name_" + i,
            date_start: date,
            display_name: "R4F4BM1_" + i,
            match_id: "e30d1c07-a673-408e-a9f1-16e31d1dbb98_" + i,
            match_number: "match_number_" + i,
            ref_code: "FJ6TIVBA3GE_" + i,
            ref_name: "ref_name_" + i,
            ref_roster_id: i,
            results: {
                "winner":"1",
                "set1":"25-19",
                "set2":"25-14",
                "team1": {
                    "matches_won":1,
                    "matches_lost":0,
                    "sets_won":2,
                    "sets_lost":0,
                    "points_won":50,
                    "points_lost":33,
                    "matches_pct":100.0,
                    "sets_pct":100.0,
                    "points_ratio":1.515151500701904,
                    "roster_team_id":1398,
                    "scores":"2-0  (25-19,25-14)"},
                    "team2":{"matches_won":0,
                    "matches_lost":1,
                    "sets_won":0,
                    "sets_lost":2,
                    "points_won":33,
                    "points_lost":50,
                    "matches_pct":0.0,
                    "sets_pct":0.0,
                    "points_ratio":0.660000026226044,
                    "roster_team_id":1740,
                    "scores":"0-2  (19-25,14-25)"}
            },
            scores: "scores_" + i,
            source: {
                "team1": {
                    "type":5,
                    "seed":5,
                    "id":"",
                    "name":"P\/B Seed 5"
                },
                "team2": {
                    "type":5,
                    "seed":4,
                    "id":"",
                    "name":"P\/B Seed 4"
                },
                "ref": {
                    "type":3,
                    "seed":0,
                    "id":"",
                    "name":"Next Higher"
                }
            },
            team1_code: "team1_code_" + i,
            team1_name: "team1_name_" + i,
            team1_pool_id: "team1_pool_id_" + i,
            team1_pool_name: "team1_pool_name_" + i,
            team1_roster_id: "team1_roster_id_" + i,
            team1_temp_name: "team1_temp_name_" + i,
            team1_temp_roster_id: "team1_temp_roster_id_" + i,
            team2_code: "team2_code_" + i,
            team2_name: "team2_name_" + i,
            team2_pool_id: "team2_pool_id_" + i,
            team2_pool_name: "team2_pool_name_" + i,
            team2_roster_id: "team2_roster_id_" + i,
            team2_temp_name: "team2_temp_name_" + i,
            team2_temp_roster_id: "team2_temp_roster_id_" + i,
            temp_score: "temp_score_" + i,
            winner: "1",
            winning_roster_id: "winning_roster_id_" + i,
            winning_team_id: "winning_team_id_" + i,
            winning_team_name: "winning_team_name_" + i,
            winning_temp_name: "winning_temp_name_" + i,
        });
    }

    $scope.pool = {
        '3rd': null,
        consolation: 0,
        display_name: "R3G",
        division_name: "15 Open",
        event_id: 14,
        flow_chart: "2tnc.html",
        pb_finishes: '{"1":{"team_id":3491,"team_name":"SMASH 15s","next_match":{},"next_ref":{}},"2":{"team_id":232,"team_name":"CMASS 15 Apocalypse","next_match":{},"next_ref":{}},"team_ids":"3491 232"}',
        pb_seeds: '{"1st":"{\"type\":6,\"seed\":1,\"id\":\"79456979-41c2-4df2-907f-2ce074fae4b6\",\"name\":\"1st-R2D1P1\",\"overallSeed\":1}","2nd":"{\"type\":6,\"seed\":1,\"id\":\"6cc89da6-03c6-4793-b9a8-d7bbec090a06\",\"name\":\"1st-R2D1P2\",\"overallSeed\":2}"}',
        round_id: "08586e46-2f0a-4c05-b37a-fd33f9fda8e7",
        sort_priority: 1,
        team_count: 2,
        uuid: null,
        win: null,
    }

    $scope.matches = tmp_matches;
}
