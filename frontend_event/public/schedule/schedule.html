<div class="container">
	<h3 class="esw_title form-inline">
		<span class="spacer-sm-r">{{::event.long_name}}</span>		
	</h3>

	<div class="row">
        <div class="col-xs-12">
            <form class="form schedule-form">
                <div class="form-group">
                    <label for="date">Event Day</label>
                    <select class="form-control spacer-mobile-sm-t"
                            id="date"
                            ng-model="event_day"
                            ng-change="changeHours()"
                            ng-options="day | UTCdate:'ddd MM/DD' for (day_key , day) in days.days_array"
                    ></select>
                </div>

                <div class="form-group">
                    <label for="start_hour">Start At</label>
                    <select class="form-control spacer-mobile-sm-t"
                            id="start_hour"
                            ng-model="start_hour"
                            ng-change="changeHours()"
                            ng-options="hour as hour.time12 for hour in startHours"
                    ></select>
                </div>

                <div class="form-group">
                    <label for="hours_count">Duration</label>
                    <select class="form-control spacer-mobile-sm-t"
                            id="hours_count"
                            ng-model="hours_count"
                            ng-change="changeHours()"
                            ng-options="div | formatHour for div in hoursCountArray"
                    ></select>
                </div>

                <div class="form-group">
                    <label for="division">Division</label>
                    <select class="form-control spacer-mobile-sm-t"
                            id="division"
                            ng-model="event_division"
                            ng-change="changeHours()"
                            ng-options="((div.gender == 'female') ? '&#9792;' : '&#9794;') + ' ' + div.division_name for div in divisions_array"
                    >
                        <option value="">All Divisions</option>
                    </select>
                </div>
            </form>
        </div>
    </div>

	<div ng-show="!isDataLoaded" class="spacer-sm-r">Loading courts grid data. Please wait...</div>
</div>

<br/>

<div class="courts-schedule-table" scroll-propagation auto-height additional-height="5">
	<table class="table table-bordered table-condensed table-striped text-center" ng-show="isDataLoaded">
		<thead>
			<tr>
				<th class="col-1">
					<span class="court-tablet">
                        <duration-change-arrows
                            hide-left-arrow="scheduleHoursAreEmpty()"
                            hide-right-arrow="scheduleHoursAreEmpty()"
                            disable-left-arrow="isFirstEventHoursItem()"
                            disable-right-arrow="isLastEventHoursItem()"
                            change-duration="changeStartHours(type)"
                        >
                            {{event_day | UTCdate:'ddd MM/DD'}}
                        </duration-change-arrows>
                    </span>
					<span class="court-mobile">
                        <duration-change-arrows
                            hide-left-arrow="scheduleHoursAreEmpty()"
                            hide-right-arrow="scheduleHoursAreEmpty()"
                            disable-left-arrow="isFirstEventHoursItem()"
                            disable-right-arrow="isLastEventHoursItem()"
                            change-duration="changeStartHours(type)"
                        >
                            {{event_day | UTCdate:'MM/DD'}}
                        </duration-change-arrows>
                    </span>
				</th>
				<th ng-repeat="hour in hours">
                    <duration-change-arrows
                        hide-left-arrow="isFirstHour(hour)"
                        hide-right-arrow="isLastHour(hour)"
                        disable-left-arrow="isFirstEventHoursItem()"
                        disable-right-arrow="isLastEventHoursItem()"
                        change-duration="changeStartHours(type)"
                    >
                        <b>{{::hour | UTCdate:'hh:mm A'}}</b>
                    </duration-change-arrows>
				</th>
			</tr>
		</thead>
		<tbody>
            <tr ng-if="scheduleHoursAreEmpty()">
                <td colspan="1">
                    <span class="empty-filters-text">
                        No matches for selected filters,&nbsp;
                        <br class="visible-xs" />
                        please change it or <a href ng-click="resetFilters()">reset</a> it
                    </span>
                </td>
            </tr>
			<tr ng-repeat="court in courts | filter: hasMatches()">
				<td class="col-1" ng-if="court.matches">
					<span class="court-tablet">
                        <a ui-sref="events.event.courts.courtdetails({court: court.court_id})">
                            {{::court.court_name}}
                        </a>
                    </span>
					<span class="court-mobile">
                        <a ui-sref="events.event.courts.courtdetails({court: court.court_id})">
                            {{::court.short_name}}
                        </a>
                    </span>
				</td>

				<td ng-repeat="match in court.matches | limitTo:hours.length" ng-style="getDivisionColor(match)">
					<div ng-if="match.match_id" ui-sref="events.event.matches.match({match: match.match_id})">
						<div>{{::match.division_short_name}} {{::match.match_name}}</div>
						<div>
							<span ng-if="match.res_team1_roster_id == match.team1_roster_id && match.res_winner == 1">
                                W:
                            </span>
							<span ng-if="match.res_team2_roster_id == match.team2_roster_id && match.res_winner == 2">
                                L:
                            </span>
							<span>{{::match.team_1_name}}</span>
						</div>
						<div>
							<span ng-if="match.res_team1_roster_id == match.team1_roster_id && match.res_winner == 2">
                                W:
                            </span>
							<span ng-if="match.res_team2_roster_id == match.team2_roster_id && match.res_winner == 1">
                                L:
                            </span>
							<span>{{::match.team_2_name}}</span>
						</div>
						<div>
							<i ng-if="!match.scores" class="ref-team">{{::match.team_ref_name}}</i>
							<span ng-if="match.scores">{{ ::match.scores }}</span>
							<span ng-if="!event.has_match_barcodes">{{::match.secs_finished | UTCdate:'h:mma'}}</span>
						</div>
					</div>
				</td>
			</tr>
		</tbody>
	</table>
</div>
