angular.module('SportWrench').controller('ScheduleController', scheduleController);

scheduleController.$inject = [
    '$scope', '$rootScope', '$stateParams', '$filter', '$window', 'currentEvent',
    'eswService', 'currentSchedule', '$localStorage'
];

function scheduleController(
    $scope, $rootScope, $stateParams, $filter, $window, currentEvent, eswService, currentSchedule, $localStorage
) {

    const DEFAULT_HOURS_COUNT = 3;

    function init () {
        $scope.courts_init      = currentSchedule.data.courts;
        $scope.hours_init       = currentSchedule.data.hours;
        $scope.divisions_init   = currentSchedule.data.divisions;

        $scope.event            = currentEvent;
        $scope.courts           = [];
        $scope.hours            = [];
        $scope.divisions_array  = [];
        $scope.isDataLoaded     = false;

        $scope.current_date = moment.utc().format('YYYY-MM-DD');

        $scope.hoursCountArray = [1, 2, 3, 4, 5, 6, 8];

        $rootScope.pageTitle = "Court Grid";

        $scope.courts = angular.copy($scope.courts_init);

        loadLocalStorageFilters();

        initStartHours();
        initDivisionFilter();

        $scope.days = {
            date_start  : moment.utc(currentEvent.date_start, "MM/DD/YYYY"),
            date_end    : moment.utc(currentEvent.date_end, "MM/DD/YYYY")
        };

        $scope.days.days_array = (function() {
            let res = [];

            if(!currentEvent || !currentEvent.days) {
                return res;
            }

            for (let i = 0; i < currentEvent.days.length; i++) {
                let date = moment.utc(currentEvent.days[i], 'YYYY-MM-DD');

                if ($scope.event_day && date.isSame($scope.event_day)) {
                    $scope.event_day= date;
                }
                res.push(date);
            }
            return res;
        })();

        initEventDays();

        $rootScope.courtFilters.event_id = $stateParams.event;

        $scope.changeHours();
    }

    function initStartHours () {
        if ($rootScope.courtFilters.event_id === $stateParams.event) {
            if ($rootScope.courtFilters.event_day) {
                $scope.last_event_day = $rootScope.courtFilters.event_day;
            }

            if ($scope.lc_start_hour || $rootScope.courtFilters.start_hour) {
                let def_hour = $rootScope.courtFilters.start_hour
                    ? $rootScope.courtFilters.start_hour.time
                    : $scope.lc_start_hour.time;

                $scope.getStartHours(def_hour);
            }

            if ($rootScope.courtFilters.hours_count) {
                $scope.hours_count = $rootScope.courtFilters.hours_count;
            }
        } else {
            if ($scope.hours_init) {
                $scope.getStartHours($scope.hours_init[$scope.hours_init.length - 1].default);
            }
        }
    }

    function initDivisionFilter () {
        $scope.divisions_array = angular.copy($scope.divisions_init);
        $scope.event_division  = {};

        if ($scope.lc_event_division || $rootScope.courtFilters.division) {
            $scope.event_division = $scope.lc_event_division || $rootScope.courtFilters.division;

            $scope.divisions_array.forEach(function(div) {
                if (Number(div.division_id) === Number($scope.event_division.division_id)) {
                    $scope.event_division = div;
                }
            });
        }
    }

    function initEventDays () {
        let compare_against_today = {};

        if (!$scope.event_day && $scope.days.days_array && $scope.days.days_array.length) {
            angular.forEach($scope.days.days_array, function(date) {

                if (date.isSame($scope.current_date)) {
                    $scope.event_day= date;
                } else if (date.isBefore($scope.current_date)) {
                    if (!compare_against_today.before) {
                        compare_against_today.before = date;
                    } else if (date.isAfter(compare_against_today.before)) {
                        compare_against_today.before = date;
                    }
                } else if (date.isAfter($scope.current_date)) {
                    if (!compare_against_today.after) {
                        compare_against_today.after = date;
                    } else if (date.isBefore(compare_against_today.after)) {
                        compare_against_today.after = date;
                    }
                }
            });

            if(!$scope.event_day) {
                $scope.event_day = (function(){
                    if (!compare_against_today.after || !compare_against_today.before) {
                        return (compare_against_today.after || compare_against_today.before);
                    }
                    return compare_against_today.after;
                })();
            }

            setFilterToLocalStorage('event_day', $scope.event_day);
        }
    }

    $scope.getDivisionColor = function(match) {
        if(!_.isEmpty($scope.event.teams_settings) && $scope.event.teams_settings.colored_esw_schedule_fields) {
            let color = match.color;

            if(!color) {
                return {};
            }

            //color values from DB have &h00 instead of #
            if(/&h00/.test(color)) {
                color = color.replace('&h00', '#');
            }

            return { 'background-color': color };
        } else {
            return {};
        }
    }

    $scope.getStartHours = function(defHour) {
        $scope.default_start_hour = defHour;
        $scope.hours_init.splice($scope.hours_init.length - 1, 1);
        $scope.startHours = $scope.hours_init;
        $scope.start_hour = null;

       if(!_.isEmpty($scope.startHours)) {
            if ($scope.default_start_hour > $scope.startHours[$scope.startHours.length - 1].time ||
                $scope.default_start_hour < $scope.startHours[0].time) {
                $scope.start_hour = $scope.startHours[0];
            }
        }

        if (!$scope.start_hour) {
            $scope.startHours.forEach(function(hour) {
                if (hour.time == $scope.default_start_hour) {
                    $scope.start_hour = hour;
                }
            }); 
        }

        if (!$scope.start_hour) {
            let defaultStartHour  = $scope.default_start_hour;
            let allowedDifference = [1, 2, 3];

            $scope.startHours.forEach(function(hour) {
                let difference = defaultStartHour - hour.time;

                if (allowedDifference.includes(difference)) {
                    $scope.start_hour = hour;
                }
            });
        }
    };

    $scope.isSameTime = function(match, index) {
        if ($scope.hours[index]) {
            return match.date_start === $scope.hours[index].date_start;
        }
        return false;
    };

    function addEmptyBoxes(matches, hours) {
        for (let j = 0; j < matches.length; j++) {
            for (let i = 0; i < hours.length; i++) {
                if (matches[j] && hours[i] != matches[j].date_start) {
                    matches.splice(j, 0, {
                        date_start: hours[i]
                    });
                }
                j++;
            }
        }

        if (matches.length < hours.length) {
            for (let i=matches.length; i<hours.length; i++) {
                matches.push({
                    date_start: $scope.hours[i]
                });
            }
        }

        return matches;
    }

    $scope.changeHours = function() {
        $scope.isDataLoaded = false;

        var event_day   = moment($scope.event_day).format('YYYY-MM-DD');
        var time        = $scope.start_hour && $scope.start_hour.time || 0;
        var hoursCount  = $scope.hours_count;
        let division    = $scope.event_division && $scope.event_division.division_id || null;
        
        $rootScope.courtFilters.start_hour = $scope.start_hour;
        $rootScope.courtFilters.hours_count = $scope.hours_count;
        $rootScope.courtFilters.division = $scope.event_division;
        $rootScope.courtFilters.event_day = $scope.event_day;

        eswService.getCourtSchedule($stateParams.event, event_day, time, hoursCount, division)
            .then(response => {
                $scope.divisions_init   = response.data.divisions;
                $scope.hours_init       = response.data.hours;
                $scope.courts_init      = response.data.courts;

                $scope.getStartHours($scope.hours_init[$scope.hours_init.length - 1].default);

                let courts = angular.copy(response.data.courts);

                $scope.isDataLoaded = true;

                setTableData(courts);

                setFilterToLocalStorage('start_hour'    , $scope.start_hour);
                setFilterToLocalStorage('hours_count'   , $scope.hours_count);
                setFilterToLocalStorage('event_day'     , $scope.event_day);
                setFilterToLocalStorage('event_division', $scope.event_division);
            })
    };

    function getHours(courts) {
        return $window._.sortBy(
            $window._.unique(
                $window._.union(
                    $window._.reduce(courts, function(memo, court) {
                        return memo.concat($window._.pluck(court.matches, 'date_start'));
                    }, [])
                ),
                function(time) {
                    return $filter('UTCdate')(time, 'hh:mm A');
                }
            )
        );
    }

    function loadLocalStorageFilters () {
        let eventFilters = $localStorage.courtFilters && $localStorage.courtFilters[$scope.event.event_id];

        if(eventFilters) {
            if(eventFilters.event_day) {
                $scope.event_day = eventFilters.event_day;
            }

            if(eventFilters.start_hour) {
                $scope.lc_start_hour = eventFilters.start_hour;
            }

            if(eventFilters.hours_count) {
                $scope.hours_count = eventFilters.hours_count;
            }

            if(eventFilters.event_division) {
                $scope.lc_event_division = eventFilters.event_division;
            }
        }

        if(!$scope.hours_count) {
            $scope.hours_count = DEFAULT_HOURS_COUNT;
        }
    }

    function setTableData(courts) {
        try {
            let hours = getHours(courts);

            $window._.each(courts, function(court) {
                if(court.matches && court.matches.length) {
                    court.matches = addEmptyBoxes(court.matches, hours);
                }
            });

            $scope.hours = hours;
            $scope.courts = courts;

            $rootScope.$emit('courts-grid:updated');
            $rootScope.$emit('auto-height:change');

        } catch (e) {
            console.log('Schedule error:', e);
        }
    }

    $scope.hasMatches = function(court) {
        return function(court) {
            if ($scope.event_division && typeof $scope.event_division.division_id == 'undefined' ||
                !$scope.event_division) {
                return true;
            }

            if (court.matches) {
                return !!$filter('filter')(court.matches, $scope.event_division.division_id).length;
            } else {
                return true;
            }
        }
    };

    function setFilterToLocalStorage (prop, val) {
        if(!$localStorage.courtFilters) {
            $localStorage.courtFilters = {
                [$scope.event.event_id]: {}
            };
        }

        if(!$localStorage.courtFilters[$scope.event.event_id]) {
            $localStorage.courtFilters[$scope.event.event_id] = {};
        }

        $localStorage.courtFilters[$scope.event.event_id][prop] = val;
    }

    $scope.isFirstEventHoursItem = function () {
        if(!_.isEmpty($scope.start_hour) && $scope.startHours.length) {
            return $scope.start_hour.time === _.head($scope.startHours).time;
        }
    }

    $scope.isLastEventHoursItem = function () {
        if(!_.isEmpty($scope.start_hour) && $scope.startHours.length) {
            return $scope.start_hour.time === _.last($scope.startHours).time;
        }
    }

    $scope.changeStartHours = function (type) {
        let index = $scope.startHours.indexOf($scope.start_hour);

        if(type === 'next') {
            if($scope.isLastEventHoursItem()) {
                return;
            }

            $scope.start_hour = $scope.startHours[index+1];
        } else if(type === 'prev') {
            if($scope.isFirstEventHoursItem()) {
                return;
            }

            $scope.start_hour = $scope.startHours[index-1];
        } else {
            return;
        }

        $scope.changeHours();
    }

    $scope.isFirstHour = function (hour) {
        return $scope.hours.indexOf(hour) === 0;
    }

    $scope.isLastHour = function (hour) {
        return $scope.hours.indexOf(hour) === $scope.hours.length - 1;
    }

    $scope.scheduleHoursAreEmpty = function () {
        return !$scope.hours || !$scope.hours.length;
    }

    $scope.resetFilters = function () {
        $scope.getStartHours($scope.hours_init[$scope.hours_init.length - 1].default);

        $scope.hours_count      = DEFAULT_HOURS_COUNT;
        $scope.event_division   = {};

        $scope.changeHours();
    }

    init();
}
