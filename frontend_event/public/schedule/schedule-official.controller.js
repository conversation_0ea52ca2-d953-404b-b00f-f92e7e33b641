angular.module('SportWrench').controller('ScheduleOfficialsController', ScheduleOfficialsController);

ScheduleOfficialsController.$inject = ['$scope', '$rootScope', '$stateParams', '$filter', '$window', 'currentEvent', 'eswService', 'currentSchedule', '$timeout'];

function ScheduleOfficialsController($scope, $rootScope, $stateParams, $filter, $window, currentEvent, eswService, currentSchedule, $timeout) {

    var draggableData = {
        official: undefined,
        court: undefined,
        match: undefined
    };

    var maxAssignments = 2;

    // officials list
    $scope.officials = [{
        id: 0,
        name: '<PERSON>',
        shown: true,
        matches: [],
        division_max_age: 13
    }, {
        id: 1,
        name: '<PERSON>',
        shown: true,
        matches: [],
        division_max_age: 15
    }, {
        id: 2,
        name: '<PERSON>',
        shown: true,
        matches: [],
        division_max_age: 18
    }, {
        id: 3,
        name: '<PERSON>',
        shown: true,
        matches: [],
        division_max_age: 13
    }, {
        id: 4,
        name: '<PERSON><PERSON><PERSON>',
        shown: true,
        matches: [],
        division_max_age: 15
    }, {
        id: 5,
        name: '<PERSON>',
        shown: true,
        matches: [],
        division_max_age: 18
    }, {
        id: 6,
        name: '<PERSON> <PERSON>cke',
        shown: true,
        matches: [],
        division_max_age: 13
    }, {
        id: 7,
        name: 'Norman Marinelli',
        shown: true,
        matches: [],
        division_max_age: 15
    }, {
        id: 8,
        name: 'Hal Alatorre',
        shown: true,
        matches: [],
        division_max_age: 18
    }, {
        id: 9,
        name: 'Tristan Murphree',
        shown: true,
        matches: [],
        division_max_age: 13
    }, {
        id: 10,
        name: 'Steven Arvie',
        shown: true,
        matches: [],
        division_max_age: 15
    }, {
        id: 11,
        name: 'Howard Castelli',
        shown: true,
        matches: [],
        division_max_age: 18
    }, {
        id: 12,
        name: 'Nigel Galbreath',
        shown: true,
        matches: [],
        division_max_age: 13
    }, {
        id: 13,
        name: 'Evan Paras',
        shown: true,
        matches: [],
        division_max_age: 15
    }, {
        id: 14,
        name: 'Raymundo Brinn',
        shown: true,
        matches: [],
        division_max_age: 18
    }, {
        id: 15,
        name: 'Mckinley Lacroix',
        shown: true,
        matches: [],
        division_max_age: 17
    }];

    $scope.selectedMatches = [];

    $scope.isOfficialsAssigned = _isOfficialsAssigned;
    $scope.disableOfficial = _disableOfficial;

    // Draggable plugin option
    $scope.draggableOptions = function(official, $index) {
        return {
            index: $index,
            animate: true,
            // placeholder: true,
            // insertInline: true,
            deepCopy: true,
            onStart: 'onDragStart(official)',
            onEnd: 'onDragEnd(official)'
        };
    };

    // Draggable jQuery UI options
    $scope.jquiDraggableOptions = {
        appendTo: "body",
        revert: 'invalid',
        helper: "clone",
        // cursor: "move",
        cancel: ".disabled",
        snap: ".schedule-officials-table > tbody > tr > td"
    };

    // Draggable plugin table option
    $scope.draggableTableOptions = function(match, court, $index) {
        return {
            index: $index,
            animate: true,
            // placeholder: true,
            // insertInline: true,
            deepCopy: true,
            onStart: 'onTableDragStart(match, court, $index)',
            onEnd: 'onDragEnd(official, court, $index)'
        };
    };

    // Droppable plugin table options
    $scope.droppableOptions = function(match, court, $index) {
        return {
            onDrop: 'onDrop(match, court, $index)',
            multiple: true,
            index: $index
        };
    };

    // Droppable jQuery UI table options
    $scope.jquiDroppableOptions = {
        accept: function ($item) {
            var $this = $(this);
            return $item.hasClass("official") &&
                   $item.closest('.officials-list').length &&
                   $this.hasClass('official-assigned') === false &&
                   // Disallow cells if official already belong to this time
                   !$window._.find(draggableData.official.matches, function (match) {
                       return match.cellIndex === $this.index();
                   })
            ;
        },
        activeClass: "ui-state-highlight",
        hoverClass: 'ui-hover-highlight',
        greedy: true
    };

    // Droppable plugin officials options
    $scope.droppableSourceOptions = {
        index: 0,
        onDrop: 'onSourceDrop'
    };

    // Droppable jQuery UI officials options
    $scope.jquiDroppableSourceOptions = {
        accept: ".schedule-officials-table .official",
        activeClass: "ui-state-highlight",
        greedy: true
    };

    $scope.onDragStart = function(event, ui, official) {
        draggableData.official = official;
        console.info('Drag Start', event, ui, official);
    };

    $scope.onTableDragStart = function(event, ui, match, court, $index) {
        draggableData.official = _findOfficial(match.officials[$index].name);
        draggableData.court = court;
        draggableData.match = match;
        console.info('Drag Start', event, ui, match, court);
    };

    $scope.onDrop = function(event, ui, match, court, $index) {
        // _toggleOfficial(draggableData.official, false);
        _setOfficialToMatches(draggableData.official, match, court, $index);
        console.info('Drop!', event, ui, match, court, $index);
    };

    $scope.onSourceDrop = function(event, ui) {
        // _toggleOfficial(draggableData.official, true);
        _removeOfficialFromMatch(draggableData.match, draggableData.official);
        console.info('Drop Source!', event, ui);
    };

    // Set official access status. _toggleOfficial(official:object, status:boolean)
    function _toggleOfficial(official, status) {
        // find original official instance
        official = _findOfficial(official.name);
        // set official shown status
        official.shown = status;
    }

    function _findOfficial(name, source) {
        source = source || $scope.officials;
        // Find original instance of official object in officials list
        return $window._.find(source, function(official) {
            return official.name === name;
        });
    }

    function _isOfficialsAssigned (officials) {
        return officials && officials.length === 2;
    }

    // example: {busy:3, free:2}
    function _getMatchesStatistic(matches) {
        return $window._.countBy(matches, function(match) {
            return match.officials && match.officials.length >= 2 ? 'busy' : 'free';
        });
    }

    function _setOfficialToMatches(official, match, court, $index) {
        var numberOfMatches = maxAssignments;
        var i = $index;
        var officialCopy;
        var matchCopy;
        var currentMatch;
        var statistic;

        // Add official to matches while left free matches without officials
        while (numberOfMatches > 0 && _getMatchesStatistic(court.matches).free > 0) {
            // Break if official already have max assignments
            // if (official.matches.length >= maxAssignments) {
            //     break;
            // }

            statistic = _getMatchesStatistic(court.matches);
            // Check if last match -> reset counter to first match
            if (i > court.matches.length) {
                i = 0;
            }

            currentMatch = court.matches[i];
            matchCopy = angular.copy(currentMatch);

            if (currentMatch) {
                // Check if officials array in match exist
                if (angular.isArray(currentMatch.officials) === false) {
                    currentMatch.officials = [];
                }

                // If official already assigned to match
                if (_findOfficial(official.name, currentMatch.officials)) {
                    // break if 1 free match left
                    if (statistic.free === 1 || official.matches.length >= maxAssignments) {
                        break;
                    } else {
                        // continue searching in other matches
                        i++;
                        continue;
                    }
                }

                // Check if current match exist, length of officials, is current official didn't assined to this match
                if (currentMatch.officials.length < 2) {
                    // Set official copy to matches
                    officialCopy = angular.copy(official);
                    delete officialCopy.matches;

                    // Add official to match
                    currentMatch.officials.push(officialCopy);

                    // Set cell index to match that will be assigned to official
                    matchCopy.cellIndex = i + 1;

                    // Assign match to original official object
                    official.matches.push(matchCopy);

                    // Decrease left matches counter
                    numberOfMatches--;
                }
            }

            i++;
        }
    }

    function _disableOfficial (official) {
        // return !official.shown || official.matches.length >= maxAssignments;
        return !official.shown;
    }

    function _removeOfficialFromMatch(match, official) {
        match.officials = $window._.without(match.officials, $window._.findWhere(match.officials, {name: official.name}));
        official.matches = $window._.without(official.matches, $window._.findWhere(official.matches, {match_id: match.match_id}));
    }

    function _removeOfficialFromAllMatches(official) {
        match.official = null;
    }

    currentSchedule = {
        "data": {
            "courts": [{
                "event_id": 21,
                "court_id": "48f2aa75-329e-4e17-8a78-07b8ede6dbeb",
                "court_name": "Ct 1 Hall B",
                "matches": [{
                    "match_id": "7e932267-b1ce-450f-b06d-d5e896f88872",
                    "match_name": "R3D1GP1M1",
                    "division_id": 169,
                    "division_name": "18 Open",
                    "division_short_name": "18O",
                    "date_start": 1428220800000,
                    "date_end": 1428224400000,
                    "secs_finished": 1428223438000,
                    "team1_roster_id": 501,
                    "team2_roster_id": 593,
                    "team_1_name": "TAV 18 Black",
                    "team_2_name": "Carolina Islanders 18 Elite",
                    "team_ref_name": "A5 18-2 Marc",
                    "res_team1_roster_id": 501,
                    "res_team2_roster_id": 593,
                    "res_winner": "1",
                    "scores": "2-0  (25-13,25-11)"
                }, {
                    "match_id": "a294197d-7f53-40bd-87cf-eb15c514eec7",
                    "match_name": "R3D1GP1M2",
                    "division_id": 169,
                    "division_name": "18 Open",
                    "division_short_name": "18O",
                    "date_start": 1428224400000,
                    "date_end": 1428228000000,
                    "secs_finished": 1428227870000,
                    "team1_roster_id": 528,
                    "team2_roster_id": 593,
                    "team_1_name": "A5 18-2 Marc",
                    "team_2_name": "Carolina Islanders 18 Elite",
                    "team_ref_name": "TAV 18 Black",
                    "res_team1_roster_id": 528,
                    "res_team2_roster_id": 593,
                    "res_winner": "1",
                    "scores": "2-1  (16-25,25-20,15-7)"
                }, {
                    "match_id": "cc865b4e-944a-49cf-9d84-53e00f07ba18",
                    "match_name": "R3D1GP1M3",
                    "division_id": 169,
                    "division_name": "18 Open",
                    "division_short_name": "18O",
                    "date_start": 1428228000000,
                    "date_end": 1428231600000,
                    "secs_finished": 1428231034000,
                    "team1_roster_id": 501,
                    "team2_roster_id": 528,
                    "team_1_name": "TAV 18 Black",
                    "team_2_name": "A5 18-2 Marc",
                    "team_ref_name": "Carolina Islanders 18 Elite",
                    "res_team1_roster_id": 501,
                    "res_team2_roster_id": 528,
                    "res_winner": "1",
                    "scores": "2-0  (25-13,25-14)"
                }]
            }, {
                "event_id": 21,
                "court_id": "ac8bd6bc-47bd-4355-95cb-2084f32ec827",
                "court_name": "Ct 2 Hall B",
                "matches": [{
                    "match_id": "deb98d71-9728-4583-a151-d899f44a3163",
                    "match_name": "R3D1GP2M1",
                    "division_id": 169,
                    "division_name": "18 Open",
                    "division_short_name": "18O",
                    "date_start": 1428220800000,
                    "date_end": 1428224400000,
                    "secs_finished": 1428223648000,
                    "team1_roster_id": 527,
                    "team2_roster_id": 494,
                    "team_1_name": "A5 18-1 Bob",
                    "team_2_name": "TUVA 18 Tampa",
                    "team_ref_name": "CJV 18 Infinity",
                    "res_team1_roster_id": 527,
                    "res_team2_roster_id": 494,
                    "res_winner": "1",
                    "scores": "2-0  (25-12,25-21)"
                }, {
                    "match_id": "c8077498-f655-483e-9885-86c89067540a",
                    "match_name": "R3D1GP2M2",
                    "division_id": 169,
                    "division_name": "18 Open",
                    "division_short_name": "18O",
                    "date_start": 1428224400000,
                    "date_end": 1428228000000,
                    "secs_finished": 1428227677000,
                    "team1_roster_id": 468,
                    "team2_roster_id": 494,
                    "team_1_name": "CJV 18 Infinity",
                    "team_2_name": "TUVA 18 Tampa",
                    "team_ref_name": "A5 18-1 Bob",
                    "res_team1_roster_id": 468,
                    "res_team2_roster_id": 494,
                    "res_winner": "1",
                    "scores": "2-1  (25-23,19-25,15-8)"
                }, {
                    "match_id": "9f655aae-9883-48af-9d7a-6124302834bf",
                    "match_name": "R3D1GP2M3",
                    "division_id": 169,
                    "division_name": "18 Open",
                    "division_short_name": "18O",
                    "date_start": 1428228000000,
                    "date_end": 1428231600000,
                    "secs_finished": 1428230889000,
                    "team1_roster_id": 527,
                    "team2_roster_id": 468,
                    "team_1_name": "A5 18-1 Bob",
                    "team_2_name": "CJV 18 Infinity",
                    "team_ref_name": "TUVA 18 Tampa",
                    "res_team1_roster_id": 527,
                    "res_team2_roster_id": 468,
                    "res_winner": "1",
                    "scores": "2-0  (25-19,25-14)"
                }]
            }, {
                "event_id": 21,
                "court_id": "4a2d294e-e0ad-4546-b9af-185d3034c3bc",
                "court_name": "Ct 3 Hall B",
                "matches": [{
                    "match_id": "325d852c-1052-4b27-beb1-a63a618d69c9",
                    "match_name": "R3D1SM1",
                    "division_id": 169,
                    "division_name": "18 Open",
                    "division_short_name": "18O",
                    "date_start": 1428220800000,
                    "date_end": 1428224400000,
                    "secs_finished": 1428224770000,
                    "team1_roster_id": 411,
                    "team2_roster_id": 445,
                    "team_1_name": "EliteVBTC 18 Black",
                    "team_2_name": "Revolution South 18 White",
                    "team_ref_name": "Rockwood Thunder 18 Mizuno",
                    "res_team1_roster_id": 411,
                    "res_team2_roster_id": 445,
                    "res_winner": "1",
                    "scores": "2-1  (25-20,23-25,15-10)"
                }, {
                    "match_id": "4ed5aab3-6863-4201-84d7-3a01d96658f5",
                    "match_name": "R3D1SM2",
                    "division_id": 169,
                    "division_name": "18 Open",
                    "division_short_name": "18O",
                    "date_start": 1428224400000,
                    "date_end": 1428228000000,
                    "secs_finished": 1428228397000,
                    "team1_roster_id": 824,
                    "team2_roster_id": 1718,
                    "team_1_name": "Triangle 18 Black",
                    "team_2_name": "Rockwood Thunder 18 Mizuno",
                    "team_ref_name": "Revolution South 18 White",
                    "res_team1_roster_id": 824,
                    "res_team2_roster_id": 1718,
                    "res_winner": "2",
                    "scores": "2-0  (25-23,25-23)"
                }, {
                    "match_id": "f39f3fda-3594-4c80-b849-7374b97f3394",
                    "match_name": "R3D1SM3",
                    "division_id": 169,
                    "division_name": "18 Open",
                    "division_short_name": "18O",
                    "date_start": 1428228000000,
                    "date_end": 1428231600000,
                    "secs_finished": 1428232588000,
                    "team1_roster_id": 1026,
                    "team2_roster_id": 411,
                    "team_1_name": "NC Elite 18 Black",
                    "team_2_name": "EliteVBTC 18 Black",
                    "team_ref_name": "Triangle 18 Black",
                    "res_team1_roster_id": 1026,
                    "res_team2_roster_id": 411,
                    "res_winner": "2",
                    "scores": "2-1  (19-25,25-20,15-10)"
                }]
            }, {
                "event_id": 21,
                "court_id": "be4175aa-8b01-410f-900c-14d24d0cbb10",
                "court_name": "Ct 4 Hall B",
                "matches": [{
                    "match_id": "070345a2-8d85-4355-a8e7-7fefc8305004",
                    "match_name": "R3D2F1M1",
                    "division_id": 169,
                    "division_name": "18 Open",
                    "division_short_name": "18O",
                    "date_start": 1428220800000,
                    "date_end": 1428224400000,
                    "secs_finished": 1428223717000,
                    "team1_roster_id": 2947,
                    "team2_roster_id": 441,
                    "team_1_name": "MDJRS 18 Elite",
                    "team_2_name": "NVVA 18 ICE Elite",
                    "team_ref_name": "Thunder Bay 18-1",
                    "res_team1_roster_id": 2947,
                    "res_team2_roster_id": 441,
                    "res_winner": "1",
                    "scores": "2-0  (26-24,25-17)"
                }, {
                    "match_id": "23ceaf3a-9e71-4c66-adb8-ca26c10d5fd7",
                    "match_name": "R3D2F1M2",
                    "division_id": 169,
                    "division_name": "18 Open",
                    "division_short_name": "18O",
                    "date_start": 1428224400000,
                    "date_end": 1428228000000,
                    "secs_finished": 1428226909000,
                    "team1_roster_id": 2710,
                    "team2_roster_id": 3659,
                    "team_1_name": "Boomers 18 Black",
                    "team_2_name": "Thunder Bay 18-1",
                    "team_ref_name": "NVVA 18 ICE Elite",
                    "res_team1_roster_id": 2710,
                    "res_team2_roster_id": 3659,
                    "res_winner": "1",
                    "scores": "2-0  (25-15,25-10)"
                }, {
                    "match_id": "122add53-3b88-476a-869e-53a1073bea60",
                    "match_name": "R3D2F1M3",
                    "division_id": 169,
                    "division_name": "18 Open",
                    "division_short_name": "18O",
                    "date_start": 1428228000000,
                    "date_end": 1428231600000,
                    "secs_finished": 1428231630000,
                    "team1_roster_id": 435,
                    "team2_roster_id": 2947,
                    "team_1_name": "NO NAME 18",
                    "team_2_name": "MDJRS 18 Elite",
                    "team_ref_name": "Thunder Bay 18-1",
                    "res_team1_roster_id": 435,
                    "res_team2_roster_id": 2947,
                    "res_winner": "1",
                    "scores": "2-1  (17-25,25-13,15-13)"
                }]
            }, {
                "event_id": 21,
                "court_id": "bcc8ffbb-f169-4764-8239-218bde797fe2",
                "court_name": "Ct 5 Hall B",
                "matches": [{
                    "match_id": "9cb56fa2-74a8-45da-9bd3-a2233d8ae0e2",
                    "match_name": "R3D2F2M1",
                    "division_id": 169,
                    "division_name": "18 Open",
                    "division_short_name": "18O",
                    "date_start": 1428220800000,
                    "date_end": 1428224400000,
                    "secs_finished": 1428223806000,
                    "team1_roster_id": 3037,
                    "team2_roster_id": 3511,
                    "team_1_name": "GBV 18-1",
                    "team_2_name": "RVJ 18-1",
                    "team_ref_name": "PVC 18 mizuno",
                    "res_team1_roster_id": 3037,
                    "res_team2_roster_id": 3511,
                    "res_winner": "2",
                    "scores": "2-0  (25-19,25-19)"
                }, {
                    "match_id": "dd6b6c2d-cc26-4930-a4db-8b9e21e02148",
                    "match_name": "R3D2F2M2",
                    "division_id": 169,
                    "division_name": "18 Open",
                    "division_short_name": "18O",
                    "date_start": 1428224400000,
                    "date_end": 1428228000000,
                    "secs_finished": 1428227603000,
                    "team1_roster_id": 2003,
                    "team2_roster_id": 1365,
                    "team_1_name": "TBVA 18T",
                    "team_2_name": "PVC 18 mizuno",
                    "team_ref_name": "GBV 18-1",
                    "res_team1_roster_id": 2003,
                    "res_team2_roster_id": 1365,
                    "res_winner": "2",
                    "scores": "2-0  (25-20,25-22)"
                }, {
                    "match_id": "195fec86-051b-4483-9758-0d64397bb0f3",
                    "match_name": "R3D2F2M3",
                    "division_id": 169,
                    "division_name": "18 Open",
                    "division_short_name": "18O",
                    "date_start": 1428228000000,
                    "date_end": 1428231600000,
                    "secs_finished": 1428232899000,
                    "team1_roster_id": 1750,
                    "team2_roster_id": 3511,
                    "team_1_name": "AZ DESERT SKY 18 CROSSFIRE",
                    "team_2_name": "RVJ 18-1",
                    "team_ref_name": "TBVA 18T",
                    "res_team1_roster_id": 1750,
                    "res_team2_roster_id": 3511,
                    "res_winner": "2",
                    "scores": "2-1  (25-22,26-28,15-12)"
                }]
            }, {
                "event_id": 21,
                "court_id": "24e18efc-2a28-4ffd-865a-3796a7871834",
                "court_name": "Ct 6 Hall B",
                "matches": [{
                    "match_id": "65440c5d-9467-480e-9a5f-7f63c8b5383f",
                    "match_name": "R3D1GM1",
                    "division_id": 171,
                    "division_name": "18 USA",
                    "division_short_name": "18U",
                    "date_start": 1428220800000,
                    "date_end": 1428224400000,
                    "secs_finished": 1428223752000,
                    "team1_roster_id": 580,
                    "team2_roster_id": 755,
                    "team_1_name": "ACE 18 Smack",
                    "team_2_name": "Five Star 18 Black",
                    "team_ref_name": "CUVC 18 John",
                    "res_team1_roster_id": 580,
                    "res_team2_roster_id": 755,
                    "res_winner": "1",
                    "scores": "2-0  (27-25,25-21)"
                }, {
                    "match_id": "a56e3ece-0213-454f-a263-14b19a6f982a",
                    "match_name": "R3D1GM5",
                    "division_id": 171,
                    "division_name": "18 USA",
                    "division_short_name": "18U",
                    "date_start": 1428224400000,
                    "date_end": 1428228000000,
                    "secs_finished": 1428228414000,
                    "team1_roster_id": 618,
                    "team2_roster_id": 580,
                    "team_1_name": "CUVC 18 John",
                    "team_2_name": "ACE 18 Smack",
                    "team_ref_name": "Five Star 18 Black",
                    "res_team1_roster_id": 618,
                    "res_team2_roster_id": 580,
                    "res_winner": "2",
                    "scores": "2-1  (25-23,13-25,15-10)"
                }, {
                    "match_id": "9793cb80-b1a3-4792-88e0-581969471eb0",
                    "match_name": "R3D1GM9",
                    "division_id": 171,
                    "division_name": "18 USA",
                    "division_short_name": "18U",
                    "date_start": 1428228000000,
                    "date_end": 1428231600000,
                    "secs_finished": 1428232101000,
                    "team1_roster_id": 580,
                    "team2_roster_id": 582,
                    "team_1_name": "ACE 18 Smack",
                    "team_2_name": "Tx Fusion 18-1",
                    "team_ref_name": "CUVC 18 John",
                    "res_team1_roster_id": 580,
                    "res_team2_roster_id": 582,
                    "res_winner": "1",
                    "scores": "2-0  (25-21,25-22)"
                }]
            }, {
                "event_id": 21,
                "court_id": "00536ac8-6a1f-47c1-81c6-ff7d4185e060",
                "court_name": "Ct 7 Hall B",
                "matches": [{
                    "match_id": "ff7c20b8-644b-4f22-a9a4-6d284d3dfb90",
                    "match_name": "R3D1GM2",
                    "division_id": 171,
                    "division_name": "18 USA",
                    "division_short_name": "18U",
                    "date_start": 1428220800000,
                    "date_end": 1428224400000,
                    "secs_finished": 1428223122000,
                    "team1_roster_id": 557,
                    "team2_roster_id": 582,
                    "team_1_name": "Ocala Power United 18 Rox",
                    "team_2_name": "Tx Fusion 18-1",
                    "team_ref_name": "SC Midlands 18 Nat Elite",
                    "res_team1_roster_id": 557,
                    "res_team2_roster_id": 582,
                    "res_winner": "2",
                    "scores": "2-0  (25-20,25-16)"
                }, {
                    "match_id": "7ce5339e-ddde-457d-a4a1-cca45374599e",
                    "match_name": "R3D1GM6",
                    "division_id": 171,
                    "division_name": "18 USA",
                    "division_short_name": "18U",
                    "date_start": 1428224400000,
                    "date_end": 1428228000000,
                    "secs_finished": 1428227124000,
                    "team1_roster_id": 582,
                    "team2_roster_id": 1133,
                    "team_1_name": "Tx Fusion 18-1",
                    "team_2_name": "SC Midlands 18 Nat Elite",
                    "team_ref_name": "Ocala Power United 18 Rox",
                    "res_team1_roster_id": 582,
                    "res_team2_roster_id": 1133,
                    "res_winner": "1",
                    "scores": "2-0  (25-17,25-18)"
                }, {
                    "match_id": "4ca17bb9-953d-49a7-9f01-13e33c7b24cb",
                    "match_name": "R3D1GM10",
                    "division_id": 171,
                    "division_name": "18 USA",
                    "division_short_name": "18U",
                    "date_start": 1428228000000,
                    "date_end": 1428231600000,
                    "secs_finished": 1428232168000,
                    "team1_roster_id": 413,
                    "team2_roster_id": 825,
                    "team_1_name": "WAVE 18-1",
                    "team_2_name": "Triangle 18 Blue",
                    "team_ref_name": "SC Midlands 18 Nat Elite",
                    "res_team1_roster_id": 413,
                    "res_team2_roster_id": 825,
                    "res_winner": "1",
                    "scores": "2-0  (25-20,25-23)"
                }]
            }, {
                "event_id": 21,
                "court_id": "f961e8cb-9a2f-4d0c-8821-1ff6e14729d3",
                "court_name": "Ct 8 Hall B",
                "matches": [{
                    "match_id": "bc9270de-c1bc-4390-8058-aa0a32c37012",
                    "match_name": "R3D1GM3",
                    "division_id": 171,
                    "division_name": "18 USA",
                    "division_short_name": "18U",
                    "date_start": 1428220800000,
                    "date_end": 1428224400000,
                    "secs_finished": 1428224270000,
                    "team1_roster_id": 4239,
                    "team2_roster_id": 413,
                    "team_1_name": "SMASH 18s",
                    "team_2_name": "WAVE 18-1",
                    "team_ref_name": "TOP SELECT 18 ELITE",
                    "res_team1_roster_id": 4239,
                    "res_team2_roster_id": 413,
                    "res_winner": "2",
                    "scores": "2-1  (25-16,19-25,15-8)"
                }, {
                    "match_id": "1ebc7f94-0b81-4f32-9da5-4ac796fa1fc8",
                    "match_name": "R3D1GM7",
                    "division_id": 171,
                    "division_name": "18 USA",
                    "division_short_name": "18U",
                    "date_start": 1428224400000,
                    "date_end": 1428228000000,
                    "secs_finished": 1428228091000,
                    "team1_roster_id": 1525,
                    "team2_roster_id": 413,
                    "team_1_name": "TOP SELECT 18 ELITE",
                    "team_2_name": "WAVE 18-1",
                    "team_ref_name": "SMASH 18s",
                    "res_team1_roster_id": 1525,
                    "res_team2_roster_id": 413,
                    "res_winner": "2",
                    "scores": "2-0  (25-22,25-18)"
                }, {
                    "match_id": "3620aac2-8c25-485c-a6d3-5263bec883ac",
                    "match_name": "R3D1BRAM1",
                    "division_id": 170,
                    "division_name": "18 American",
                    "division_short_name": "18A",
                    "date_start": 1428228000000,
                    "date_end": 1428231600000,
                    "secs_finished": 1428232707000,
                    "team1_roster_id": 4016,
                    "team2_roster_id": 1900,
                    "team_1_name": "BCYC  18-1",
                    "team_2_name": "Jackson Juniors 181",
                    "team_ref_name": "POWER 18 NAT'L",
                    "res_team1_roster_id": 4016,
                    "res_team2_roster_id": 1900,
                    "res_winner": "1",
                    "scores": "2-1  (20-25,25-23,15-9)"
                }]
            }, {
                "event_id": 21,
                "court_id": "f74bc307-a161-454c-9702-1529b2052cd2",
                "court_name": "Ct 9 Hall B",
                "matches": [{
                    "match_id": "b558fe13-9d58-40af-80be-2237ce210ace",
                    "match_name": "R3D1GM4",
                    "division_id": 171,
                    "division_name": "18 USA",
                    "division_short_name": "18U",
                    "date_start": 1428220800000,
                    "date_end": 1428224400000,
                    "secs_finished": 1428224555000,
                    "team1_roster_id": 1577,
                    "team2_roster_id": 825,
                    "team_1_name": "Tampa Elite U18 Columbia",
                    "team_2_name": "Triangle 18 Blue",
                    "team_ref_name": "Momentum 18 Black",
                    "res_team1_roster_id": 1577,
                    "res_team2_roster_id": 825,
                    "res_winner": "2",
                    "scores": "2-1  (17-25,25-23,15-12)"
                }, {
                    "match_id": "9f64544e-2425-492c-b033-f8eeb6e431aa",
                    "match_name": "R3D1GM8",
                    "division_id": 171,
                    "division_name": "18 USA",
                    "division_short_name": "18U",
                    "date_start": 1428224400000,
                    "date_end": 1428228000000,
                    "secs_finished": 1428228026000,
                    "team1_roster_id": 825,
                    "team2_roster_id": 542,
                    "team_1_name": "Triangle 18 Blue",
                    "team_2_name": "Momentum 18 Black",
                    "team_ref_name": "Tampa Elite U18 Columbia",
                    "res_team1_roster_id": 825,
                    "res_team2_roster_id": 542,
                    "res_winner": "1",
                    "scores": "2-0  (25-23,25-19)"
                }, {
                    "match_id": "134da1d9-91c6-4b46-9202-5e8fd8e90028",
                    "match_name": "R3D1BRBM1",
                    "division_id": 170,
                    "division_name": "18 American",
                    "division_short_name": "18A",
                    "date_start": 1428228000000,
                    "date_end": 1428231600000,
                    "secs_finished": 1428232838000,
                    "team1_roster_id": 1829,
                    "team2_roster_id": 1720,
                    "team_1_name": "352 Elite 18'U",
                    "team_2_name": "FMTVA 18 Orange",
                    "team_ref_name": "Texas Synergy 18 Green",
                    "res_team1_roster_id": 1829,
                    "res_team2_roster_id": 1720,
                    "res_winner": "1",
                    "scores": "2-1  (25-20,21-25,15-12)"
                }]
            }, {
                "event_id": 21,
                "court_id": "7be6aaa6-2cc9-434f-b16f-510ad2617d1c",
                "court_name": "Ct 10 Hall B",
                "matches": [{
                    "match_id": "94066dfb-0574-4540-ae8a-cdcbe55632f5",
                    "match_name": "R3D1SM1",
                    "division_id": 171,
                    "division_name": "18 USA",
                    "division_short_name": "18U",
                    "date_start": 1428220800000,
                    "date_end": 1428224400000,
                    "secs_finished": 1428224269000,
                    "team1_roster_id": 1814,
                    "team2_roster_id": 1628,
                    "team_1_name": "FSVBC Suns 18Black",
                    "team_2_name": "KVA 18RED",
                    "team_ref_name": "Set Point 18 Black",
                    "res_team1_roster_id": 1814,
                    "res_team2_roster_id": 1628,
                    "res_winner": "1",
                    "scores": "2-0  (25-20,25-22)"
                }, {
                    "match_id": "63f72c9e-8ab7-4df7-89b7-b36b15eb721a",
                    "match_name": "R3D1SM2",
                    "division_id": 171,
                    "division_name": "18 USA",
                    "division_short_name": "18U",
                    "date_start": 1428224400000,
                    "date_end": 1428228000000,
                    "secs_finished": 1428227802000,
                    "team1_roster_id": 421,
                    "team2_roster_id": 1049,
                    "team_1_name": "CCXJRS 18-1",
                    "team_2_name": "Set Point 18 Black",
                    "team_ref_name": "KVA 18RED",
                    "res_team1_roster_id": 421,
                    "res_team2_roster_id": 1049,
                    "res_winner": "2",
                    "scores": "2-0  (27-25,25-23)"
                }, {
                    "match_id": "234efff6-b167-4df1-8e28-cff803379b05",
                    "match_name": "R3D1SM3",
                    "division_id": 171,
                    "division_name": "18 USA",
                    "division_short_name": "18U",
                    "date_start": 1428228000000,
                    "date_end": 1428231600000,
                    "secs_finished": 1428232020000,
                    "team1_roster_id": 661,
                    "team2_roster_id": 1814,
                    "team_1_name": "Academy 18 Diamond",
                    "team_2_name": "FSVBC Suns 18Black",
                    "team_ref_name": "CCXJRS 18-1",
                    "res_team1_roster_id": 661,
                    "res_team2_roster_id": 1814,
                    "res_winner": "2",
                    "scores": "2-0  (25-21,26-24)"
                }]
            }, {
                "event_id": 21,
                "court_id": "80fea29e-a38e-4549-acd6-21373db16dcd",
                "court_name": "Ct 11 Hall B",
                "matches": [{
                    "match_id": "27feab30-077d-46e9-b939-5d2f55806e27",
                    "match_name": "R3D1BRM1",
                    "division_id": 171,
                    "division_name": "18 USA",
                    "division_short_name": "18U",
                    "date_start": 1428220800000,
                    "date_end": 1428224400000,
                    "secs_finished": 1428222945000,
                    "team1_roster_id": 1173,
                    "team2_roster_id": 383,
                    "team_1_name": "JJVA 18N TEAM ROX GINNY",
                    "team_2_name": "USA SOUTH 18 National",
                    "team_ref_name": "CSVC 18 Leesa",
                    "res_team1_roster_id": 1173,
                    "res_team2_roster_id": 383,
                    "res_winner": "1",
                    "scores": "2-0  (25-13,25-18)"
                }, {
                    "match_id": "ff61e8cb-1e8b-4073-888f-4f9b74ba91fc",
                    "match_name": "R3D1BRM2",
                    "division_id": 171,
                    "division_name": "18 USA",
                    "division_short_name": "18U",
                    "date_start": 1428224400000,
                    "date_end": 1428228000000,
                    "secs_finished": 1428226515000,
                    "team1_roster_id": 3674,
                    "team2_roster_id": 2417,
                    "team_1_name": "KPVA 18-1",
                    "team_2_name": "CSVC 18 Leesa",
                    "team_ref_name": "USA SOUTH 18 National",
                    "res_team1_roster_id": 3674,
                    "res_team2_roster_id": 2417,
                    "res_winner": "2",
                    "scores": "2-0  (26-24,25-13)"
                }, {
                    "match_id": "bee1873e-643e-4496-baca-1eb904f587d3",
                    "match_name": "R3D1BRM3",
                    "division_id": 171,
                    "division_name": "18 USA",
                    "division_short_name": "18U",
                    "date_start": 1428228000000,
                    "date_end": 1428231600000,
                    "secs_finished": 1428229996000,
                    "team1_roster_id": 3203,
                    "team2_roster_id": 1173,
                    "team_1_name": "Tsunami 18-Joe",
                    "team_2_name": "JJVA 18N TEAM ROX GINNY",
                    "team_ref_name": "KPVA 18-1",
                    "res_team1_roster_id": 3203,
                    "res_team2_roster_id": 1173,
                    "res_winner": "2",
                    "scores": "2-0  (25-10,25-22)"
                }]
            }, {
                "event_id": 21,
                "court_id": "7a0fa00d-b38f-406a-8902-e251b6caccc7",
                "court_name": "Ct 12 Hall B",
                "matches": [{
                    "match_id": "46af6000-8367-485e-9411-7e1e95fe6f67",
                    "match_name": "R3D2F1M1",
                    "division_id": 171,
                    "division_name": "18 USA",
                    "division_short_name": "18U",
                    "date_start": 1428220800000,
                    "date_end": 1428224400000,
                    "secs_finished": 1428224190000,
                    "team1_roster_id": 363,
                    "team2_roster_id": 3738,
                    "team_1_name": "AVA 18 ATTACK",
                    "team_2_name": "Mobile Storm 18 Elite",
                    "team_ref_name": "Venom 18-1",
                    "res_team1_roster_id": 363,
                    "res_team2_roster_id": 3738,
                    "res_winner": "1",
                    "scores": "2-0  (25-18,25-22)"
                }, {
                    "match_id": "3869a97c-9e16-4d92-b20b-46469e016097",
                    "match_name": "R3D2F1M2",
                    "division_id": 171,
                    "division_name": "18 USA",
                    "division_short_name": "18U",
                    "date_start": 1428224400000,
                    "date_end": 1428228000000,
                    "secs_finished": 1428227477000,
                    "team1_roster_id": 2641,
                    "team2_roster_id": 1924,
                    "team_1_name": "St. Pete 18 Teal",
                    "team_2_name": "Venom 18-1",
                    "team_ref_name": "Mobile Storm 18 Elite",
                    "res_team1_roster_id": 2641,
                    "res_team2_roster_id": 1924,
                    "res_winner": "1",
                    "scores": "2-0  (25-18,25-15)"
                }, {
                    "match_id": "819dd407-289f-47a2-bbaa-82a2b28dda48",
                    "match_name": "R3D2F1M3",
                    "division_id": 171,
                    "division_name": "18 USA",
                    "division_short_name": "18U",
                    "date_start": 1428228000000,
                    "date_end": 1428231600000,
                    "secs_finished": 1428231334000,
                    "team1_roster_id": 392,
                    "team2_roster_id": 363,
                    "team_1_name": "NRV 18Gold",
                    "team_2_name": "AVA 18 ATTACK",
                    "team_ref_name": "Venom 18-1",
                    "res_team1_roster_id": 392,
                    "res_team2_roster_id": 363,
                    "res_winner": "2",
                    "scores": "2-0  (25-22,25-19)"
                }]
            }, {
                "event_id": 21,
                "court_id": "4d2f8166-4b5d-4858-bb18-5b1678f57412",
                "court_name": "Ct 13 Hall B",
                "matches": [{
                    "match_id": "20f3cfe3-a599-4ae3-96d4-574f7ab7c6df",
                    "match_name": "R3D2F2M1",
                    "division_id": 171,
                    "division_name": "18 USA",
                    "division_short_name": "18U",
                    "date_start": 1428220800000,
                    "date_end": 1428224400000,
                    "secs_finished": 1428223424000,
                    "team1_roster_id": 4434,
                    "team2_roster_id": 3067,
                    "team_1_name": "CCCVBC 18 Black",
                    "team_2_name": "CSRA Heat 18 Gold",
                    "team_ref_name": "Tampa Attack 18-1",
                    "res_team1_roster_id": 4434,
                    "res_team2_roster_id": 3067,
                    "res_winner": "1",
                    "scores": "2-0  (25-20,25-21)"
                }, {
                    "match_id": "8aeedf26-6de1-4bc4-8eef-7a03e7cc69d7",
                    "match_name": "R3D2F2M2",
                    "division_id": 171,
                    "division_name": "18 USA",
                    "division_short_name": "18U",
                    "date_start": 1428224400000,
                    "date_end": 1428228000000,
                    "secs_finished": 1428227157000,
                    "team1_roster_id": 4381,
                    "team2_roster_id": 4171,
                    "team_1_name": "Mid-Coast 18-1",
                    "team_2_name": "Tampa Attack 18-1",
                    "team_ref_name": "CSRA Heat 18 Gold",
                    "res_team1_roster_id": 4381,
                    "res_team2_roster_id": 4171,
                    "res_winner": "1",
                    "scores": "2-0  (25-17,25-20)"
                }, {
                    "match_id": "fdfffea5-4c88-4788-97fd-3d4a57ca0b32",
                    "match_name": "R3D2F2M3",
                    "division_id": 171,
                    "division_name": "18 USA",
                    "division_short_name": "18U",
                    "date_start": 1428228000000,
                    "date_end": 1428231600000,
                    "secs_finished": 1428231848000,
                    "team1_roster_id": 412,
                    "team2_roster_id": 4434,
                    "team_1_name": "EliteVBTC 18 Blue",
                    "team_2_name": "CCCVBC 18 Black",
                    "team_ref_name": "Tampa Attack 18-1",
                    "res_team1_roster_id": 412,
                    "res_team2_roster_id": 4434,
                    "res_winner": "1",
                    "scores": "2-1  (25-18,23-25,15-8)"
                }]
            }, {
                "event_id": 21,
                "court_id": "4d8b63af-4823-4197-b0df-fd4cc92f333c",
                "court_name": "Ct 14 Hall B",
                "matches": [{
                    "match_id": "beff207f-c541-4ed2-b166-805fb8f9185d",
                    "match_name": "R3D2F3M1",
                    "division_id": 171,
                    "division_name": "18 USA",
                    "division_short_name": "18U",
                    "date_start": 1428220800000,
                    "date_end": 1428224400000,
                    "secs_finished": 1428223444000,
                    "team1_roster_id": 3077,
                    "team2_roster_id": 3861,
                    "team_1_name": "KVC 18-1",
                    "team_2_name": "Waves 18-1",
                    "team_ref_name": "CUVC 18 Grayson",
                    "res_team1_roster_id": 3077,
                    "res_team2_roster_id": 3861,
                    "res_winner": "2",
                    "scores": "2-0  (25-23,25-17)"
                }, {
                    "match_id": "ca08047b-23fb-49e1-8bf1-340863e7fc04",
                    "match_name": "R3D2F3M2",
                    "division_id": 171,
                    "division_name": "18 USA",
                    "division_short_name": "18U",
                    "date_start": 1428224400000,
                    "date_end": 1428228000000,
                    "secs_finished": 1428226997000,
                    "team1_roster_id": 3426,
                    "team2_roster_id": 619,
                    "team_1_name": "ATLBoom 18 Diamond Rox",
                    "team_2_name": "CUVC 18 Grayson",
                    "team_ref_name": "KVC 18-1",
                    "res_team1_roster_id": 3426,
                    "res_team2_roster_id": 619,
                    "res_winner": "1",
                    "scores": "2-0  (25-19,25-17)"
                }, {
                    "match_id": "d3d0381d-4bd0-4e36-b5d1-d3e7d3fc1813",
                    "match_name": "R3D2F3M3",
                    "division_id": 171,
                    "division_name": "18 USA",
                    "division_short_name": "18U",
                    "date_start": 1428228000000,
                    "date_end": 1428231600000,
                    "secs_finished": 1428230378000,
                    "team1_roster_id": 1043,
                    "team2_roster_id": 3861,
                    "team_1_name": "NC Elite 18 Cardinal",
                    "team_2_name": "Waves 18-1",
                    "team_ref_name": "CUVC 18 Grayson",
                    "res_team1_roster_id": 1043,
                    "res_team2_roster_id": 3861,
                    "res_winner": "2",
                    "scores": "2-0  (25-18,25-10)"
                }]
            }, {
                "event_id": 21,
                "court_id": "e825a2fb-3aa9-47b3-aaae-7f74fdd5b03b",
                "court_name": "Ct 15 Hall B",
                "matches": [{
                    "match_id": "f8d76458-2bd6-42bd-9e33-fa8464fbf2cb",
                    "match_name": "R3D2F4M1",
                    "division_id": 171,
                    "division_name": "18 USA",
                    "division_short_name": "18U",
                    "date_start": 1428220800000,
                    "date_end": 1428224400000,
                    "secs_finished": 1428223269000,
                    "team1_roster_id": 4399,
                    "team2_roster_id": 4511,
                    "team_1_name": "Cobra VBC 18-1",
                    "team_2_name": "Mobile Storm 18-2",
                    "team_ref_name": "UPPS 18-1",
                    "res_team1_roster_id": 4399,
                    "res_team2_roster_id": 4511,
                    "res_winner": "2",
                    "scores": "2-0  (25-17,25-10)"
                }, {
                    "match_id": "cba05d10-f452-4850-9c0d-c50c3e91ca05",
                    "match_name": "R3D2F4M2",
                    "division_id": 171,
                    "division_name": "18 USA",
                    "division_short_name": "18U",
                    "date_start": 1428224400000,
                    "date_end": 1428228000000,
                    "secs_finished": 1428227016000,
                    "team1_roster_id": 1859,
                    "team2_roster_id": 4215,
                    "team_1_name": "EVOLUTION 18-1 ADIDAS BLK",
                    "team_2_name": "UPPS 18-1",
                    "team_ref_name": "Cobra VBC 18-1",
                    "res_team1_roster_id": 1859,
                    "res_team2_roster_id": 4215,
                    "res_winner": "1",
                    "scores": "2-0  (25-17,25-19)"
                }, {
                    "match_id": "807474ef-3866-4b8b-b481-487caa4460c4",
                    "match_name": "R3D2F4M3",
                    "division_id": 171,
                    "division_name": "18 USA",
                    "division_short_name": "18U",
                    "date_start": 1428228000000,
                    "date_end": 1428231600000,
                    "secs_finished": 1428230670000,
                    "team1_roster_id": 4525,
                    "team2_roster_id": 4511,
                    "team_1_name": "Crossfire 18-1",
                    "team_2_name": "Mobile Storm 18-2",
                    "team_ref_name": "UPPS 18-1",
                    "res_team1_roster_id": 4525,
                    "res_team2_roster_id": 4511,
                    "res_winner": "1",
                    "scores": "2-0  (25-23,25-15)"
                }]
            }, {
                "event_id": 21,
                "court_id": "40cb7c3b-d185-41c3-8ffe-b8a2a75cf368",
                "court_name": "Ct 16 Hall B",
                "matches": [{
                    "match_id": "5cb2e9ea-a1b7-4b6f-b1e5-66d4ac08cedd",
                    "match_name": "R3D1GM1",
                    "division_id": 170,
                    "division_name": "18 American",
                    "division_short_name": "18A",
                    "date_start": 1428220800000,
                    "date_end": 1428224400000,
                    "secs_finished": 1428223413000,
                    "team1_roster_id": 673,
                    "team2_roster_id": 2175,
                    "team_1_name": "Skyline 18 Black",
                    "team_2_name": "USPAV 18-1",
                    "team_ref_name": "PSV 18 Black",
                    "res_team1_roster_id": 673,
                    "res_team2_roster_id": 2175,
                    "res_winner": "1",
                    "scores": "2-0  (25-18,25-14)"
                }, {
                    "match_id": "d7ddecd0-2153-4b03-86f9-92f6c860d50e",
                    "match_name": "R3D1GM2",
                    "division_id": 170,
                    "division_name": "18 American",
                    "division_short_name": "18A",
                    "date_start": 1428224400000,
                    "date_end": 1428228000000,
                    "secs_finished": 1428227470000,
                    "team1_roster_id": 3585,
                    "team2_roster_id": 388,
                    "team_1_name": "PSV 18 Black",
                    "team_2_name": "Southwest 18 Asics",
                    "team_ref_name": "USPAV 18-1",
                    "res_team1_roster_id": 3585,
                    "res_team2_roster_id": 388,
                    "res_winner": "1",
                    "scores": "2-0  (25-17,25-15)"
                }, {
                    "match_id": "ba3a2136-010f-43e6-bba7-4dee12459bb3",
                    "match_name": "R3D1GM9",
                    "division_id": 170,
                    "division_name": "18 American",
                    "division_short_name": "18A",
                    "date_start": 1428228000000,
                    "date_end": 1428231600000,
                    "secs_finished": 1428232599000,
                    "team1_roster_id": 673,
                    "team2_roster_id": 3585,
                    "team_1_name": "Skyline 18 Black",
                    "team_2_name": "PSV 18 Black",
                    "team_ref_name": "Southwest 18 Asics",
                    "res_team1_roster_id": 673,
                    "res_team2_roster_id": 3585,
                    "res_winner": "1",
                    "scores": "2-0  (25-18,25-22)"
                }]
            }, {
                "event_id": 21,
                "court_id": "48f8c0a1-fd6b-44ff-bef9-973f83718777",
                "court_name": "Ct 17 Hall B",
                "matches": [{
                    "match_id": "52f631fc-7387-4255-a610-23c185b2caf5",
                    "match_name": "R3D1GM4",
                    "division_id": 170,
                    "division_name": "18 American",
                    "division_short_name": "18A",
                    "date_start": 1428220800000,
                    "date_end": 1428224400000,
                    "secs_finished": 1428226468000,
                    "team1_roster_id": 1072,
                    "team2_roster_id": 1432,
                    "team_1_name": "Atlanta Extreme 18-1 Kaepa",
                    "team_2_name": "TUVA 18 United",
                    "team_ref_name": "A5 18-3",
                    "res_team1_roster_id": 1072,
                    "res_team2_roster_id": 1432,
                    "res_winner": "1",
                    "scores": "2-0  (27-25,25-22)"
                }, {
                    "match_id": "447f6c9a-15ab-4499-ba0d-578670cac026",
                    "match_name": "R3D1GM3",
                    "division_id": 170,
                    "division_name": "18 American",
                    "division_short_name": "18A",
                    "date_start": 1428224400000,
                    "date_end": 1428228000000,
                    "secs_finished": 1428228114000,
                    "team1_roster_id": 1225,
                    "team2_roster_id": 529,
                    "team_1_name": "360VB 18 Kaos/Black",
                    "team_2_name": "A5 18-3",
                    "team_ref_name": "TUVA 18 United",
                    "res_team1_roster_id": 1225,
                    "res_team2_roster_id": 529,
                    "res_winner": "2",
                    "scores": "2-1  (25-17,21-25,15-9)"
                }, {
                    "match_id": "86177530-0b3d-4e51-94be-7599b3c24c98",
                    "match_name": "R3D1GM10",
                    "division_id": 170,
                    "division_name": "18 American",
                    "division_short_name": "18A",
                    "date_start": 1428228000000,
                    "date_end": 1428231600000,
                    "secs_finished": 1428231049000,
                    "team1_roster_id": 529,
                    "team2_roster_id": 1072,
                    "team_1_name": "A5 18-3",
                    "team_2_name": "Atlanta Extreme 18-1 Kaepa",
                    "team_ref_name": "360VB 18 Kaos/Black",
                    "res_team1_roster_id": 529,
                    "res_team2_roster_id": 1072,
                    "res_winner": "1",
                    "scores": "2-0  (25-13,25-14)"
                }]
            }, {
                "event_id": 21,
                "court_id": "f51e0f68-c35b-4d46-b5df-1c5746685735",
                "court_name": "Ct 18 Hall B",
                "matches": [{
                    "match_id": "48755c3e-1812-4978-8308-083c0a12c7e6",
                    "match_name": "R3D1GM5",
                    "division_id": 170,
                    "division_name": "18 American",
                    "division_short_name": "18A",
                    "date_start": 1428220800000,
                    "date_end": 1428224400000,
                    "secs_finished": 1428224066000,
                    "team1_roster_id": 3403,
                    "team2_roster_id": 532,
                    "team_1_name": " GA5 Volleyball Club F18-1 April",
                    "team_2_name": "CAVC 18 Silver",
                    "team_ref_name": "ProLink 18N Chris",
                    "res_team1_roster_id": 3403,
                    "res_team2_roster_id": 532,
                    "res_winner": "1",
                    "scores": "2-1  (16-25,25-14,15-13)"
                }, {
                    "match_id": "a81ecfef-f902-4c50-a236-fd9366ae3c4f",
                    "match_name": "R3D1GM6",
                    "division_id": 170,
                    "division_name": "18 American",
                    "division_short_name": "18A",
                    "date_start": 1428224400000,
                    "date_end": 1428228000000,
                    "secs_finished": 1428227288000,
                    "team1_roster_id": 3027,
                    "team2_roster_id": 4174,
                    "team_1_name": "ProLink 18N Chris",
                    "team_2_name": "MBurg 18-1",
                    "team_ref_name": "CAVC 18 Silver",
                    "res_team1_roster_id": 3027,
                    "res_team2_roster_id": 4174,
                    "res_winner": "2",
                    "scores": "2-0  (25-13,25-17)"
                }, {
                    "match_id": "544f76d1-b3f9-4512-a0ec-d7e0a21fc727",
                    "match_name": "R3D1GM11",
                    "division_id": 170,
                    "division_name": "18 American",
                    "division_short_name": "18A",
                    "date_start": 1428228000000,
                    "date_end": 1428231600000,
                    "secs_finished": 1428231204000,
                    "team1_roster_id": 3403,
                    "team2_roster_id": 4174,
                    "team_1_name": " GA5 Volleyball Club F18-1 April",
                    "team_2_name": "MBurg 18-1",
                    "team_ref_name": "ProLink 18N Chris",
                    "res_team1_roster_id": 3403,
                    "res_team2_roster_id": 4174,
                    "res_winner": "1",
                    "scores": "2-0  (25-22,25-22)"
                }]
            }, {
                "event_id": 21,
                "court_id": "5d536546-355f-454d-b4c9-7b5de1d95f2c",
                "court_name": "Ct 19 Hall B",
                "matches": [{
                    "match_id": "cf4700bf-7cc8-403d-89af-543fe5da2ef1",
                    "match_name": "R3D1GM8",
                    "division_id": 170,
                    "division_name": "18 American",
                    "division_short_name": "18A",
                    "date_start": 1428220800000,
                    "date_end": 1428224400000,
                    "secs_finished": 1428224784000,
                    "team1_roster_id": 4269,
                    "team2_roster_id": 705,
                    "team_1_name": "COLAVOL 18-1",
                    "team_2_name": "RCVC 18-1",
                    "team_ref_name": "Top Flight 18 Elite",
                    "res_team1_roster_id": 4269,
                    "res_team2_roster_id": 705,
                    "res_winner": "2",
                    "scores": "2-1  (24-26,25-23,15-5)"
                }, {
                    "match_id": "02f0f93f-db13-4e8e-a3a0-da82d98b4917",
                    "match_name": "R3D1GM7",
                    "division_id": 170,
                    "division_name": "18 American",
                    "division_short_name": "18A",
                    "date_start": 1428224400000,
                    "date_end": 1428228000000,
                    "secs_finished": 1428229444000,
                    "team1_roster_id": 757,
                    "team2_roster_id": 2160,
                    "team_1_name": "Hot Shotz 18-1",
                    "team_2_name": "Top Flight 18 Elite",
                    "team_ref_name": "COLAVOL 18-1",
                    "res_team1_roster_id": 757,
                    "res_team2_roster_id": 2160,
                    "res_winner": "2",
                    "scores": "2-1  (25-19,22-25,16-14)"
                }, {
                    "match_id": "1a73804a-cae5-4fad-b05b-8930535dba6a",
                    "match_name": "R3D1GM12",
                    "division_id": 170,
                    "division_name": "18 American",
                    "division_short_name": "18A",
                    "date_start": 1428228000000,
                    "date_end": 1428231600000,
                    "secs_finished": 1428233060000,
                    "team1_roster_id": 2160,
                    "team2_roster_id": 705,
                    "team_1_name": "Top Flight 18 Elite",
                    "team_2_name": "RCVC 18-1",
                    "team_ref_name": "Hot Shotz 18-1",
                    "res_team1_roster_id": 2160,
                    "res_team2_roster_id": 705,
                    "res_winner": "2",
                    "scores": "2-0  (25-21,25-19)"
                }]
            }, {
                "event_id": 21,
                "court_id": "f4e80dd3-d15c-4f4c-a405-34d3f1ef4fde",
                "court_name": "Ct 20 Hall B",
                "matches": [{
                    "match_id": "0f6e3010-4db0-4e4d-a47f-cc761dc5b6fd",
                    "match_name": "R3D1F1AM1",
                    "division_id": 170,
                    "division_name": "18 American",
                    "division_short_name": "18A",
                    "date_start": 1428220800000,
                    "date_end": 1428224400000,
                    "secs_finished": 1428223455000,
                    "team1_roster_id": 4053,
                    "team2_roster_id": 433,
                    "team_1_name": "SeVA 18-1",
                    "team_2_name": "Synergy 18-1",
                    "team_ref_name": "Maine Jrs. 18 Gold",
                    "res_team1_roster_id": 4053,
                    "res_team2_roster_id": 433,
                    "res_winner": "2",
                    "scores": "2-0  (25-14,25-22)"
                }, {
                    "match_id": "078ff504-3a8f-4c28-b560-175662073e3a",
                    "match_name": "R3D1F1AM2",
                    "division_id": 170,
                    "division_name": "18 American",
                    "division_short_name": "18A",
                    "date_start": 1428224400000,
                    "date_end": 1428228000000,
                    "secs_finished": 1428227857000,
                    "team1_roster_id": 2335,
                    "team2_roster_id": 433,
                    "team_1_name": "Maine Jrs. 18 Gold",
                    "team_2_name": "Synergy 18-1",
                    "team_ref_name": "SeVA 18-1",
                    "res_team1_roster_id": 2335,
                    "res_team2_roster_id": 433,
                    "res_winner": "2",
                    "scores": "2-0  (27-25,25-18)"
                }, {
                    "match_id": "d932d885-ffc9-46e7-92e9-6a67c439e700",
                    "match_name": "R3D1F1AM3",
                    "division_id": 170,
                    "division_name": "18 American",
                    "division_short_name": "18A",
                    "date_start": 1428228000000,
                    "date_end": 1428231600000,
                    "secs_finished": 1428232258000,
                    "team1_roster_id": 4294,
                    "team2_roster_id": 3587,
                    "team_1_name": "CFVC 18-1",
                    "team_2_name": "CUVC 18-3 Casey",
                    "team_ref_name": "Maine Jrs. 18 Gold",
                    "res_team1_roster_id": 4294,
                    "res_team2_roster_id": 3587,
                    "res_winner": "1",
                    "scores": "2-1  (25-23,19-25,15-5)"
                }]
            }, {
                "event_id": 21,
                "court_id": "f3db1520-bb79-4443-b1e5-f21104aeab2b",
                "court_name": "Ct A2 Hall A3",
                "matches": [{
                    "match_id": "35ddf84e-3143-46cd-9f21-8d17a5008392",
                    "match_name": "R3D2F5BM1",
                    "division_id": 175,
                    "division_name": "13 American",
                    "division_short_name": "13A",
                    "date_start": 1428220800000,
                    "date_end": 1428224400000,
                    "secs_finished": 1428222937000,
                    "team1_roster_id": 1730,
                    "team2_roster_id": 3391,
                    "team_1_name": "CFV 13 Typhoon",
                    "team_2_name": " GA5 Volleyball Club F13-2 Kelsey",
                    "team_ref_name": "NAVC 13-3 Claire",
                    "res_team1_roster_id": 1730,
                    "res_team2_roster_id": 3391,
                    "res_winner": "1",
                    "scores": "2-0  (25-9,25-12)"
                }, {
                    "match_id": "3a260556-2fcd-47fe-89ae-457bfdf45b82",
                    "match_name": "R3D2F5BM2",
                    "division_id": 175,
                    "division_name": "13 American",
                    "division_short_name": "13A",
                    "date_start": 1428224400000,
                    "date_end": 1428228000000,
                    "secs_finished": 1428227615000,
                    "team1_roster_id": 3225,
                    "team2_roster_id": 3106,
                    "team_1_name": "Tsunami Dekalb 13-Bill",
                    "team_2_name": "NAVC 13-3 Claire",
                    "team_ref_name": " GA5 Volleyball Club F13-2 Kelsey",
                    "res_team1_roster_id": 3225,
                    "res_team2_roster_id": 3106,
                    "res_winner": "2",
                    "scores": "2-1  (20-25,25-10,16-14)"
                }, {
                    "match_id": "0c81f3c2-625b-4a8f-aa23-5efa9ac09d38",
                    "match_name": "R3D2F5BM3",
                    "division_id": 175,
                    "division_name": "13 American",
                    "division_short_name": "13A",
                    "date_start": 1428228000000,
                    "date_end": 1428231600000,
                    "secs_finished": 1428232672000,
                    "team1_roster_id": 2740,
                    "team2_roster_id": 1730,
                    "team_1_name": "Emerald Coast 13 Regional",
                    "team_2_name": "CFV 13 Typhoon",
                    "team_ref_name": "Tsunami Dekalb 13-Bill",
                    "res_team1_roster_id": 2740,
                    "res_team2_roster_id": 1730,
                    "res_winner": "2",
                    "scores": "2-1  (25-18,15-25,15-12)"
                }]
            }],
            "hours": [{
                "time": 8,
                "time12": "8 AM"
            }, {
                "time": 9,
                "time12": "9 AM"
            }, {
                "time": 10,
                "time12": "10 AM"
            }, {
                "time": 11,
                "time12": "11 AM"
            }, {
                "time": 12,
                "time12": "12 PM"
            }, {
                "time": 13,
                "time12": "1 PM"
            }, {
                "time": 14,
                "time12": "2 PM"
            }, {
                "time": 15,
                "time12": "3 PM"
            }, {
                "default": "0"
            }],
            "divisions": [{
                "division_id": 172,
                "division_name": "12 American",
                "gender": "female"
            }, {
                "division_id": 173,
                "division_name": "12 National",
                "gender": "female"
            }, {
                "division_id": 175,
                "division_name": "13 American",
                "gender": "female"
            }, {
                "division_id": 174,
                "division_name": "13 Open",
                "gender": "female"
            }, {
                "division_id": 158,
                "division_name": "14 American",
                "gender": "female"
            }, {
                "division_id": 157,
                "division_name": "14 Open",
                "gender": "female"
            }, {
                "division_id": 159,
                "division_name": "14 USA",
                "gender": "female"
            }, {
                "division_id": 161,
                "division_name": "15 American",
                "gender": "female"
            }, {
                "division_id": 160,
                "division_name": "15 Open",
                "gender": "female"
            }, {
                "division_id": 162,
                "division_name": "15 USA",
                "gender": "female"
            }, {
                "division_id": 164,
                "division_name": "16 American",
                "gender": "female"
            }, {
                "division_id": 163,
                "division_name": "16 Open",
                "gender": "female"
            }, {
                "division_id": 165,
                "division_name": "16 USA",
                "gender": "female"
            }, {
                "division_id": 167,
                "division_name": "17 American",
                "gender": "female"
            }, {
                "division_id": 166,
                "division_name": "17 Open",
                "gender": "female"
            }, {
                "division_id": 168,
                "division_name": "17 USA",
                "gender": "female"
            }, {
                "division_id": 170,
                "division_name": "18 American",
                "gender": "female"
            }, {
                "division_id": 169,
                "division_name": "18 Open",
                "gender": "female"
            }, {
                "division_id": 171,
                "division_name": "18 USA",
                "gender": "female"
            }]
        }
    };
    $scope.event = currentEvent;
    $scope.courts = [];
    $scope.hours = [];
    $scope.divisions_array = [];

    $scope.isDataLoaded = false;

    $rootScope.pageTitle = "Court Grid";

    $scope.courts_init = currentSchedule.data.courts;
    $scope.hours_init = currentSchedule.data.hours;
    $scope.divisions_init = currentSchedule.data.divisions;

    console.log($scope.courts_init, $scope.hours_init, $scope.divisions_init)

    if ($scope.hours_init) {
        var defHour = $scope.hours_init[$scope.hours_init.length - 1].default;
    }

    $scope.getStartHours = function(defHour) {
        $scope.default_start_hour = defHour;
        $scope.hours_init.splice($scope.hours_init.length - 1, 1);
        $scope.startHours = $scope.hours_init;
        $scope.start_hour = null;

        if ($scope.default_start_hour > $scope.startHours[$scope.startHours.length - 1] ||
            $scope.default_start_hour < $scope.startHours[0]) {
            $scope.start_hour = $scope.startHours[0];
        }

        if (!$scope.start_hour) {
            $scope.startHours.forEach(function(hour, startPos, arr) {
                if (hour.time == $scope.default_start_hour) {
                    $scope.start_hour = hour;
                }
            });
        }

        if (!$scope.start_hour) {
            $scope.startHours.forEach(function(hour, startPos, arr) {
                if ($scope.default_start_hour - hour.time == 1 ||
                    $scope.default_start_hour - hour.time == 2 ||
                    $scope.default_start_hour - hour.time == 3) {
                    $scope.start_hour = hour;
                }
            });
        }

    };

    if ($scope.hours_init) {
        $scope.getStartHours(defHour);
    }

    $scope.hours_count = 3;
    $scope.hoursCountArray = [1, 2, 3, 4, 5, 6];

    if ($rootScope.courtFilters.event_id == $stateParams.event) {
        if ($rootScope.courtFilters.event_day) {
            $scope.last_event_day = $rootScope.courtFilters.event_day;
        }

        if ($rootScope.courtFilters.start_hour) {
            var def_hour = $rootScope.courtFilters.start_hour.time;
            $scope.getStartHours(def_hour);
        }

        if ($rootScope.courtFilters.hours_count) {
            $scope.hours_count = $rootScope.courtFilters.hours_count;
        }
    }

    $scope.courts = angular.copy($scope.courts_init);

    setTableData($scope.courts);

    $scope.days = {
        date_start: moment.utc(currentEvent.date_start, "MM/DD/YYYY"),
        date_end: moment.utc(currentEvent.date_end, "MM/DD/YYYY")
    };

    if ($scope.days.date_end && $scope.days.date_start) {
        $scope.event_day = $scope.days.date_end;
        $scope.days_count = $scope.days.date_end.diff($scope.days.date_start, 'days');
    }

    $scope.days.days_array = (function() {
        var res = [];
        for (var i = 0; i <= $scope.days_count; i++) {
            res.push(moment.utc($scope.days.date_start).add(i, 'd'));
        }
        return res;
    })();

    if (moment($scope.event_day).isAfter(new Date())) {
        if ($scope.days.date_start) {
            $scope.event_day = $scope.days.date_start;
        }
    }

    $scope.current_date = moment(new Date()).utc().format('YYYY-MM-DD');

    if ($scope.days.days_array && $scope.days.days_array.length) {
        $scope.days.days_array.forEach(function(date) {
            if (moment($scope.event_day).format('YYYY-MM-DD') == moment(date).format('YYYY-MM-DD')) {
                $scope.event_day = date;
            }
            if (date.format('YYYY-MM-DD') == $scope.current_date) {
                $scope.event_day = date;
            }
            if (date.format('YYYY-MM-DD') == $scope.last_event_day) {
                $scope.event_day = date;
            }
        });
    }

    $rootScope.courtFilters.event_id = $stateParams.event;

    $scope.$watch('event_day', function(newVal, oldVal) {
        if (!newVal || oldVal == newVal) return;
        $scope.isDataLoaded = false;
        var day = moment(newVal).format('YYYY-MM-DD');
        $rootScope.courtFilters.event_day = day;
        // var time = $scope.start_hour.time;
        var hoursCount = $scope.hours_count;

        eswService.getCourtSchedule($stateParams.event, day, 0, hoursCount)
            .then(response => {
                $scope.hours_init = response.data.hours;
                if ($scope.hours_init) {
                    var def_hour = $scope.hours_init[$scope.hours_init.length - 1].default;
                    $scope.getStartHours(def_hour);
                }

                $scope.divisions_init = response.data.divisions;
                $scope.courts = response.data.courts;
                $scope.courts_init = angular.copy($scope.courts);
                setTableData($scope.courts);
            })
    });

    $scope.isSameTime = function(match, index) {
        console.log(match, index)
        if ($scope.hours[index]) {
            return match.date_start === $scope.hours[index].date_start;
        }
        return false;
    };

    $scope.$watch('event_division', function(newVal, oldVal) {
        $rootScope.courtFilters.division = newVal; //$scope.event_division;

        $scope.isDataLoaded = false;
        angular.element($window).triggerHandler('resize');

        $scope.courts = angular.copy($scope.courts_init);

        var courts = $filter('filter')(angular.copy($scope.courts), $scope.hasMatches);

        $scope.hours = getHours(courts);

        courts = $filter('filter')(angular.copy($scope.courts_init), $scope.hasMatches);

        $window._.each(courts, function(court) {
            court.matches = $filter('filter')(angular.copy(court.matches), $scope.byEventDivision());
            court.matches = matchesFilter(court.matches, false);
            court.matches = addEmptyBoxes(court.matches, $scope.hours);
            if (court.court_name && court.court_name.indexOf('Ct') != -1) {
                court.short_court_name = court.court_name.substr(court.court_name.indexOf('Ct'));
            }
        });

        if (courts.length) {
            for (var i = 0; i < courts[0].matches.length; i++) {
                if (courts[0].matches[i] && typeof courts[0].matches[i].match_name == 'undefined') {
                    var counter = 0,
                        pos;
                    for (var j = 0; j < courts.length; j++) {
                        if (courts[j].matches[i] && typeof courts[j].matches[i].match_name == 'undefined') {
                            pos = i;
                            counter++;
                        } else {
                            counter = 0;
                            break;
                        }
                    }

                    if (counter == courts.length) {
                        for (var k = 0; k < courts.length; k++) {
                            courts[k].matches.splice(pos, 1);
                        }
                        $scope.hours.splice(pos, 1);
                    }
                }
            }
        }

        $scope.courts = angular.copy(courts);
        $scope.isDataLoaded = true;

        $rootScope.$emit('courts-grid:updated');
        $rootScope.$emit('auto-height:change');
    });

    $scope.changeHours = function() {
        $scope.isDataLoaded = false;
        var event_day = moment($scope.event_day).format('YYYY-MM-DD');
        var time = $scope.start_hour.time;
        var hoursCount = $scope.hours_count;

        $rootScope.courtFilters.start_hour = $scope.start_hour;
        $rootScope.courtFilters.hours_count = $scope.hours_count;

        eswService.getCourtSchedule($stateParams.event, event_day, time, hoursCount)
            .then(response => {
                $scope.divisions_init = response.data.divisions;

                $scope.courts_init = response.data.courts;
                var courts = angular.copy($scope.courts_init);

                setTableData(courts);

                $scope.isDataLoaded = true;
            })
    };

    function getHours(courts) {
        return $window._.sortBy(
            $window._.unique(
                $window._.union(
                    $window._.reduce(courts, function(memo, court) {
                        return memo.concat($window._.pluck(court.matches, 'date_start'));
                    }, [])
                ),
                function(time) {
                    return $filter('UTCdate')(time, 'hh:mm A');
                }
            )
        );
    }

    function getDivisions(courts) {
        return $window._.sortBy(
            $window._.unique(
                $window._.union(
                    $window._.reduce(courts, function(memo, court) {
                        return memo.concat($window._.pluck(court.matches, 'division_id'));
                    }, [])
                )
            )
        );
    }

    function addEmptyBoxes(matches, hours) {
        for (var j = 0; j < matches.length; j++) {
            for (var i = 0; i < hours.length; i++) {
                if (matches[j] && hours[i] != matches[j].date_start) {
                    matches.splice(j, 0, {
                        date_start: hours[i]
                    });
                }
                j++;
            }
        }

        if (matches.length < hours.length) {
            for (var i = matches.length; i < hours.length; i++) {
                matches.push({
                    date_start: $scope.hours[i]
                });
            }
        }

        return matches;
    }

    function matchesFilter(matches, makeDivisions) {
        return $window._.sortBy(matches, function(match) {
            return match.date_start;
        });
    }

    function setTableData(courts) {
        try {
            var hours = getHours(courts);
            var divisions = getDivisions(courts);

            $window._.each(courts, function(court) {
                court.matches = matchesFilter(court.matches, true);
                court.matches = addEmptyBoxes(court.matches, hours);
                if (court.court_name && court.court_name.indexOf('Ct') != -1) {
                    court.short_court_name = court.court_name.substr(court.court_name.indexOf('Ct'));
                }
            });

            $scope.divisions_array = angular.copy($scope.divisions_init);
            $scope.event_division = {};

            /*$scope.divisions_array.forEach(function(div) {
                for (var i=0; i<divisions.length; i++) {
                    if (div.division_id == divisions[i]) {
                        div.bold = true;
                    }
                }
            });*/

            console.log($scope.divisions_array);

            if ($rootScope.courtFilters.division) {
                $scope.event_division = $rootScope.courtFilters.division;

                $scope.divisions_array.forEach(function(div) {
                    if (div.division_id == $scope.event_division.division_id) {
                        $scope.event_division = div;
                    }
                });
            }

            console.log(hours);

            $scope.hours = hours;
            $scope.courts = courts;
            $scope.isDataLoaded = true;

            var courtsFiltered = angular.copy(courts);
            courtsFiltered = $filter('filter')(courtsFiltered, $scope.hasMatches);

            var isEmpty = false;
            isEmpty = courtsFiltered.every(function(crt) {
                crt.matches = $filter('filter')(crt.matches, $scope.byEventDivision);
                return !crt.matches.length;
            });

            if (isEmpty) {
                if ($scope.days.days_array && $scope.days.days_array.length) {
                    $scope.days.days_array.forEach(function(date, i, dates) {
                        if (!moment($scope.event_day).format('YYYY-MM-DD').isAfter(moment(date).format('YYYY-MM-DD'))) {
                            $scope.event_day = dates[i + 1];
                        }
                    });
                }
            }

            $rootScope.$emit('courts-grid:updated');
            $rootScope.$emit('auto-height:change');

        } catch (e) {
            console.log('Schedule error:', e);
        }
    }

    $scope.hasMatches = function(court) {
        return function(court) {
            if ($scope.event_division && typeof $scope.event_division.division_id == 'undefined' ||
                !$scope.event_division) {
                return true;
            }

            if (court.matches) {
                return $filter('filter')(court.matches, $scope.event_division.division_id).length ? true : false;
            } else {
                return true;
            }
        }
    };

    $scope.byEventDivision = function(match) {
        return function(match) {
            if ($scope.event_division && typeof $scope.event_division.division_id == 'undefined' ||
                !$scope.event_division ||
                !match.division_id) {
                return true;
            }

            if ($scope.event_division && match.division_id == $scope.event_division.division_id) {
                return true;
            } else {
                return false;
            }
        }
    };

}
