<span class="pull-left" ng-if="$ctrl.hideLeftArrow">
    <button ng-click="$ctrl.changeDuration({type: 'prev'})"
            type="button"
            class="btn btn-default btn-xs"
            ng-disabled="$ctrl.disableLeftArrow"
    >
        <i class="fa fa-chevron-left"></i>
    </button>
</span>
<ng-transclude></ng-transclude>
<span class="pull-right" ng-if="$ctrl.hideRightArrow">
    <button ng-click="$ctrl.changeDuration({type: 'next'})"
            type="button"
            class="btn btn-default btn-xs"
            ng-disabled="$ctrl.disableRightArrow"
    >
        <i class="fa fa-chevron-right"></i>
    </button>
</span>
