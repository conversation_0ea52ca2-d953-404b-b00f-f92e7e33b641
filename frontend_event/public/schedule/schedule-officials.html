<div class="container">
	<h3 class="esw_title form-inline">
		<span class="spacer-sm-r">{{::event.long_name}}</span>		
	</h3>

	<form class="form schedule-form">
		<div class="form-group">
		    <label for="date">Event Day</label>
			<select class="form-control spacer-mobile-sm-t" id="date" ng-model="event_day" ng-options="day | UTCdate:'ddd MM/DD' for (day_key , day) in days.days_array"></select>
		</div>

		<div class="form-group">
			<label for="start_hour">Start At</label>
			<select class="form-control spacer-mobile-sm-t" id="start_hour" ng-model="start_hour" ng-change="changeHours()" ng-options="hour.time12 for hour in startHours">
			</select>
		</div>

		<div class="form-group">
			<label for="hours_count">Duration</label>
			<select class="form-control spacer-mobile-sm-t" id="hours_count" ng-model="hours_count" ng-change="changeHours()" ng-options="div | formatHour for div in hoursCountArray">
			</select>
		</div>

		<div class="form-group">
			<label for="division">Division</label>
			<select class="form-control spacer-mobile-sm-t" id="division" ng-model="event_division" ng-options="((div.gender == 'female') ? '&#9792;' : '&#9794;') + ' ' + div.division_name for div in divisions_array | orderBy: 'division_name'">
				<option value="">All Divisions</option>
			</select>
		</div>
	</form>

	<div ng-if="!isDataLoaded" class="spacer-sm-r">Loading courts grid data. Please wait...</div>
</div>

<br/>

<div class="row">
	<div class="col-md-3">
		<h4>Officials</h4>
		<!-- <ul class="list-group officials-list" draggable-officials="officials-list" droppable-selector=".schedule-officials-table > tbody > tr > td" destination-on-drop="dropHandler" source-on-drop="dropHandler"> -->
		<ul class="list-group officials-list" drop="true" jqyoui-droppable="droppableSourceOptions" jqyoui-options="jquiDroppableSourceOptions">
			<a href class="list-group-item official" ng-repeat="official in officials | orderBy:'division_max_age' track by official.id" ng-class="{'disabled': disableOfficial(official)}" drag="true" jqyoui-draggable="draggableOptions(official, $index)" jqyoui-options="jquiDraggableOptions">{{official.name}}</a>
		</ul>
	</div>
	<div class="col-md-9">
		<div class="courts-schedule-table" scroll-propagation auto-height additional-height="5">
			<table class="table table-bordered table-condensed table-striped text-center schedule-officials-table"
                   
                   ng-if="isDataLoaded"
            >
				<thead ng-if="!hideGrid">
					<tr class="court-heading">
						<th class="col-1">
							<span class="court-tablet">{{::event_day | UTCdate:'ddd MM/DD'}}</span> 
							<span class="court-mobile">{{::event_day | UTCdate:'MM/DD'}}</span>
						</th>
						<th ng-repeat="hour in hours">
							<b>{{::hour | UTCdate:'hh:mm A'}}</b>
						</th>
					</tr>
				</thead>
				<tbody selectable selectable-list="court.matches" selectable-out="selectedMatches" selectable-options="{filter:'td:not(.official-assigned)'}">
					<tr ng-repeat="court in courts | filter: hasMatches()">
						<td class="col-1">
							<span class="court-tablet"><a ui-sref="events.event.courts.courtdetails({court: court.court_id})">{{::court.court_name}}</a></span>
							<span class="court-mobile"><a ui-sref="events.event.courts.courtdetails({court: court.court_id})">{{::court.short_court_name}}</a></span>
						</td>
						<!-- <td ui-sref="events.event.matches.match({match: match.match_id})" ng-repeat="match in court.matches | filter: byEventDivision() | limitTo:hours.length">
							<div>{{::match.division_short_name}} {{::match.match_name}}</div>
						</td> -->
						<td ng-repeat="match in court.matches | filter: byEventDivision() | limitTo:hours.length" ng-class="{'official-assigned': isOfficialsAssigned(match.officials)}" 
									drop="true" 
									jqyoui-droppable="droppableOptions(match, court, $index)" 
									jqyoui-options="jquiDroppableOptions">
							<div>{{::match.division_short_name}} {{::match.match_name}}</div>
							<div class="list-group-item official"ng-repeat="official in match.officials" 
									drag="true" 
									jqyoui-draggable="draggableTableOptions(match, court, $index)" 
									jqyoui-options="jquiDraggableOptions">{{official.name}}</div>
						</td>
					</tr>
				</tbody>
			</table>
		</div>
		<pre>{{selectedMatches | json}}</pre>
	</div>
</div>
