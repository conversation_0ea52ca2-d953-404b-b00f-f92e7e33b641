'use strict';

angular.module('SportWrench').service('UtilsService', UtilsService);

UtilsService.$inject = ['$location', 'SWT_URL', 'SALES_HUB_URL'];

function UtilsService ($location, SWT_URL, SALES_HUB_URL){
    this.SWT_URL = SWT_URL;
    this.SALES_HUB_URL = SALES_HUB_URL;
}

UtilsService.prototype.getTicketsDirectLink = function (ticketsCode, isAssignedTickets) {
    return isAssignedTickets
        ? `${this.SWT_URL}/buy?event=${ticketsCode}`
        : `${this.SWT_URL}/#/events/${ticketsCode}`;
};

UtilsService.prototype.getTicketsSalesHubLink = function (salesHubPosID) {
    return `${this.SALES_HUB_URL}/point-of-sales/${salesHubPosID}/purchase-tickets`
}
