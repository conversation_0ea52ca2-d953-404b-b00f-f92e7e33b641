'use strict';

const Joi = require('joi');

const { SW_FEE_PAYER, TEAMS_SW_FEE_COLLECTION_MODE } = require('../lib/joi-constants');

const PAYERS = Object.values(SW_FEE_PAYER);
const TEAMS_SW_FEE_MODES = Object.values(TEAMS_SW_FEE_COLLECTION_MODE);

const feesPayerErrorMessage = errors => {
    return errors.map(err => {
        if (err.type === 'any.allowOnly') {
			err.message = 'Sw Fee Payer and Payment Provider Fee Payer must be equal values';
		}

        return err;
    });
};

module.exports = {
	event_monetary: Joi.object().keys({
		teams_entry_sw_fee: Joi.number().required().allow(null).min(0).label('Teams Reg SW Fee'),
		tickets_sw_fee: Joi.number().required().allow(null).min(0).label('Tickets Reg SW Fee'),
        exhibitors_sw_fee: Joi.number().required().allow(null).min(0).label('Exhibitors SW Fee'),
		stripe_teams_percent: Joi.number().required().min(0).label('Stripe Teams percent'),
		stripe_tickets_percent: Joi.number().required().min(0).label('Stripe Tickets percent'),
        stripe_exhibitors_percent: Joi.number().required().min(0).label('Stripe Exhibitors percent'),
		tickets_sw_fee_payer: Joi.string().valid(...PAYERS).required().label('Sw Fee Payer for Tickets'),
		tickets_provider_fee_payer: Joi.string()
			.valid(Joi.ref('tickets_sw_fee_payer'))
			.error(feesPayerErrorMessage)
			.required()
			.label('Payment Provider Fee Payer for Tickets'),
		teams_sw_fee_payer: Joi.string().valid(...PAYERS).required().label('Sw Fee Payer for Teams'),
        stripe_teams_fee_payer: Joi.string().valid(...PAYERS).required().label('Stripe Fee Payer for Teams'),
        show_ncsa_athlete_form: Joi.boolean().default(false).label('Switch on NCSA API'),
        no_junk_tax_prices: Joi.boolean().default(false).label('No Junk Tax Prices'),
        ncsa_event_id: Joi.number().integer().required().allow(null).label('Specific Event ID'),
        teams_sw_fee_mode: Joi.string().allow(null).valid(...TEAMS_SW_FEE_MODES).label('Teams SW Fee mode'),
        use_merchandise_sales: Joi.boolean().default(false).label('Use Merchandise Sales'),
	}).rename('teams_sw_fee', 'teams_entry_sw_fee')
}
