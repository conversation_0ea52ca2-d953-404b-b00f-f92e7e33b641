'use strict';

const Jo<PERSON> = require('joi');

let createEOKeys = {
    title 				: Joi.string().required().trim().max(200).lowercase().label('Title'),
    email_template_group: Joi.string().required().label('Template Type'),
	event_id 			: Joi.number().min(1).when('visibility_scope', {
		is 			: 'event',
		then 		: Joi.required(),
		otherwise 	: Joi.strip()
	}).label('Event ID'),
	event_owner_id 		: Joi.number().required().min(1).label('Event Owner ID'),
	email_template_id 	: Joi.number().required().label('Basic Layout'),
	visibility_scope 	: Joi.string().allow('event', 'eo').required().label('Template for'),
    email_template_type : Joi.string().required().label('Template type')
}

let createAdminKeys = _.omit(createEOKeys, 'event_id', 'event_owner_id', 'visibility_scope');
    createAdminKeys.email_template_type = Joi.string().optional().allow(null).label('Notification Type');

module.exports = {
	editTemplate: Joi.object().keys({
		unlayer_json  	 	: Joi.object().required().label('React Email Editor\'s JSON'),
		email_html   	: Joi.string().required().label('Email HTML'),
		title 		 	: Joi.string().max(200).required().label('Title'),
		email_subject 	: Joi.string().max(998).optional().allow(null).label('Email Subject')
	})
        .rename('json' , 'unlayer_json')
        .rename('html' , 'email_html'),

	createTemplate: Joi.object().keys(createEOKeys)
		.rename('group'           , 'email_template_group')
        .rename('template_id'     , 'email_template_id')
        .rename('type'            , 'email_template_type'),

	createTemplateAdmin: Joi.object().keys(createAdminKeys)
		.rename('group'           , 'email_template_group')
        .rename('template_id'     , 'email_template_id')
        .rename('type'            , 'email_template_type')
}
