module.exports = async function volleyStationEventAccess(req, res, next) {
    const eventId = parseInt(req.params.eventId);

    if (!eventId || isNaN(eventId)) {
        return res.validation('Invalid event identifier passed');
    }

    try {
        const enabled = await VolleyStationService.isEnabledForEvent(eventId);
        if (!enabled) {
            return res.status(403).json({
                message: 'Event not available for VolleyStation integration'
            });
        }
        next();
    } catch (err) {
        loggers.errors_log.error('VolleyStation event access error:', err);
        return res.status(500).json({
            message: 'Unable to verify event access'
        });
    }
};
