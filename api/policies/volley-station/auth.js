const crypto = require('crypto');

module.exports = function (req, res, next) {
    const authToken = req.get('Authorization');

    if (!authToken) {
        res.status(401).json({ message: 'Authorization token is missing' });
        return;
    }

    if (authToken === sails.config.volleyStation.apiKey) {
        req.user = req.user || {};
        req.user.clientId = getAuthorizedClientId(authToken)
        next();
    } else {
        res.status(401).json({ message: 'Authorization token invalid' });
    }
};

function getAuthorizedClientId (authToken) {
    return crypto.createHash('sha256')
        .update(authToken)
        .digest('hex')
        .slice(0, 8);
}
