const argv = require('optimist').argv;
const winston = require('winston');
const path = require('path');
const WinstonGraylog2 = require('winston-graylog2');
const WinstonSyslog = require('../../lib/log/WinstonSyslog');
const SWLoggerService = require('../../lib/log/SWLogger');
const SWLogger = new SWLoggerService(winston);

module.exports = (sails) => ({
    configure() {
        const logDir = process.env.SW_LOGGER_FILE_PATH || '../logs';
        const prefix = argv.prod ? 'prod_' : 'dev_';

        function logfile(name) {
            return path.join(logDir, prefix + name);
        }

        function logLevel() {
            return argv.prod ? 'warn' : 'silly';
        }

        const transports = [];
        if(sails.config.log.syslogOptions) {
            const transport = new WinstonSyslog(sails.config.log.syslogOptions);
            transports.push(transport);
        }
        if(sails.config.log.graylogOptions) {
            const transport = new WinstonGraylog2(sails.config.log.graylogOptions);
            transports.push(transport);
        }

        const purchase_log = SWLogger.add('purchase_log', {
            console: {
                level: logLevel(),
                colorize: 'true',
                label: 'PURCHASE'
            },
            file: {
                filename: logfile('purcahse.log')
            },
        });
        const errors_log = SWLogger.add('errors_log', {
            console: {
                level: 'error',
                colorize: 'true',
                label: 'ERROR'
            },
            file: {
                filename: logfile('errors.log')
            },
            transports,
        });
        const debug_log = SWLogger.add('debug_log', {
            console: {
                level: logLevel(),
                colorize: 'true',
                label: 'DEBUG'
            },
            file: {
                filename: logfile('application.log')
            },
        });
        const event_notifications = SWLogger.add('event_notifications', {
            console: {
                level: logLevel(),
                colorize: true,
                label: 'EVENT NOTIFICATIONS'
            },
            file: {
                filename: logfile('event_notifications.log')
            },
        });
        const sent_notifications = SWLogger.add('sent_notifications', {
            console: {
                level: logLevel(),
                colorize: true,
                label: '[Notification sent]'
            },
            file: {
                filename: logfile('sent_notifications.log')
            },
        });
        const webpoint_parse = SWLogger.add('webpoint_parse', {
            console: {
                level: logLevel(),
                colorize: true,
                label: 'WEBPOINT PARSE'
            },
            file: {
                filename: logfile('webpoint_parse.log')
            },
        });
        const sw_errors = SWLogger.add('sw_frontend_errors', {
            console: {
                level: 'error',
                colorize: true,
                label: 'SW CLIENT'
            },
            file: {
                filename: logfile('sw_frontend_errors.log')
            },
        });
        const esw_errors = SWLogger.add('esw_frontend_errors', {
            console: {
                level: 'error',
                colorize: true,
                label: 'ESW CLIENT'
            },
            file: {
                filename: logfile('esw_frontend_errors.log')
            },
        });

        global.loggers = {
            errors_log,
            debug_log,
            purchase_log,
            event_notifications,
            sent_notifications,
            wp: webpoint_parse,
            sw_client: sw_errors,
            esw_client: esw_errors,
        };

        global.logger = debug_log;

        process.on('unhandledRejection', (err, p) => {
            errors_log.error('Caught an unhandled rejection', JSON.stringify(err), p);
        });
        process.on('uncaughtException', (err) => {
            errors_log.error('Caught an unhandled exception', err);
        });
    }
});
