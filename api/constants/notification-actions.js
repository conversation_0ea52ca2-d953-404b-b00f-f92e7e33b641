
module.exports = {
    TEAM_HOUSING_STATUS_CHANGED: 'team.housing.status-change',
    TEAM_HOUSING_NOTE_ADDED: 'team.housing.note',
    TEAM_ONLINE_CHECKIN_UNCHECKED_IN: 'team.online-checkin.uncheckedin',
    TEAM_ONLINE_CHECKIN_CHECKED_IN: 'team.online-checkin.checkedin',
    TEAM_CHECKIN_NOT_CHECKED_IN: 'team.checkin.notcheckedin',
    TEAM_CHECKIN_CHECKED_IN: 'team.checkin.checkedin',
    TEAM_CHECKIN_STATUS_PENDING: 'team.checkin.pending',
    TEAM_ONLINE_CHECKIN_SCANNED: 'team.online-checkin.scanned',
    TEAM_CHECKIN_STATUS_ALERT: 'team.checkin.alert',
    ACS_PIN_CODE_GENERATED: 'acs.pincode.code.generated',
    ACS_PIN_CODE_EMAIL_SENT: 'acs.pincode.email.sent',
    <PERSON><PERSON>_<PERSON><PERSON>_CODE_SMS_SENT: 'acs.pincode.sms.sent',
    TEAM_ROSTER_STAFF_CHANGED_EO: 'team.roster.staff.changed.eo',
    TEAM_ROSTER_STAFF_CHECKIN_ACTIVATED: 'team.roster.staff.checkin.activated',
    TEAM_ROSTER_STAFF_CHECKIN_DEACTIVATED: 'team.roster.staff.checkin.deactivated',
    TEAM_ROSTER_ATHLETE_CHANGED_EO: 'team.roster.athlete.changed',
    TEAM_ROSTER_STAFF_REMOVED_BY_SYSTEM: 'team.roster.staff.removed.system',
    TEAM_ROSTER_ATHLETE_REMOVED_BY_SYSTEM: 'team.roster.athlete.removed.system',
    TEAM_MEMBER_ADDED: 'team.member.add',
    TEAM_MEMBER_REMOVED: 'team.member.remove',
    TEAM_ROSTER_LOCKED_BY_DEADLINE: 'team.roster.lock.deadline',
    TEAM_ROSTER_LOCKED_BY_ONLINE_CHECKING: 'team.roster.lock.online-checkin',
    TEAM_ROSTER_UNLOCKED_BY_EVENT_OWNER: 'team.roster.unlock.eo',
    TEAM_ROSTER_LOCKED_BY_EO: 'team.roster.lock.eo',
    TEAM_ROSTER_VALID_MARK_ADDED: 'team.roster.valid-mark.added',
    TEAM_ROSTER_VALID_MARK_REMOVED: 'team.roster.valid-mark.removed',
    TEAM_BID_ACCEPTED_BY_CLUB: 'team.bid.agreement.accepted.club',
    TEAM_BID_DECLINED_BY_CLUB: 'team.bid.agreement.declined.club',
    TEAM_BID_ACCEPTED_BY_EO: 'team.bid.agreement.accepted.eo',
    TEAM_BID_DECLINED_BY_EO: 'team.bid.agreement.declined.eo',
    REGISTRATION_PAID_BY_CARD: 'payment.card.paid',
    REGISTRATION_PAYMENT_TYPE_CHANGED_FROM_CHECK_TO_CARD: 'payment.card.change_from_check',
    REGISTRATION_PAYMENT_MAID_BY_CARD_REFUNDED: 'payment.card.refunded',
    REGISTRATION_PAID_BY_CHECK: 'payment.check.paid',
    REGISTRATION_PAID_BY_CHECK_CANCELED: 'payment.check.canceled',
    REGISTRATION_PAID_BY_CHECK_PENDING: 'payment.check.pending',
    REGISTRATION_PAID_BY_ACH_PAID: 'payment.ach.paid',
    REGISTRATION_PAID_BY_ACH_PENDING: 'payment.ach.pending',
    REGISTRATION_PAID_BY_ACH_CANCELED: 'payment.ach.canceled',
    REGISTRATION_PAYMENT_REFUNDED: 'payment.refunded',
    REGISTRATION_PAYMENT_REFUNDED_PARTIALLY: 'payment.refunded.partial',
    REGISTRATION_PAYMENT_CANCELLED: 'payment.canceled',
    REGISTRATION_PAID_BY_CHECK_PARTIALLY_CANCELED: 'payment.partially-canceled',
    TEAM_PAYMENT_STATUS_CHANGED_TO_PAID_BY_EO: 'team.paid-status.change.eo.paid',
    TEAM_PAYMENT_STATUS_CHANGED_TO_NOT_PAID_BY_EO: 'team.paid-status.change.eo.not-paid',
    TEAM_PAYMENT_REFUND_FULL: 'purchase.team.refund.full',
    TEAM_PAYMENT_REFUND_PARTIAL: 'purchase.team.refund.partial',
    PURCHASE_EMAIL_SENT: 'purchase.email.sent',
    TEAM_STATUS_CHANGED_TO_PENDING: 'team.entry.pending',
    TEAM_STATUS_CHANGED_TO_ACCEPTED: 'team.entry.accepted',
    TEAM_STATUS_CHANGED_TO_DECLINED: 'team.entry.declined',
    TEAM_STATUS_CHANGED_TO_WAITING: 'team.entry.waiting',
    TEAM_ENTRY_PAYMENT_CARD: 'team.entry-payment.card',
    TEAM_ENTRY_PAYMENT_CHECK: 'team.entry-payment.check',
    TEAM_ENTERED_ON_EVENT: 'team.entered',
    TEAM_DELETED_FROM_EVENT: 'team.deleted',
    TEAM_CHANGED_DIVISION: 'team.division.changed',
    TEAM_CHANGED_GENDER: 'team.gender.changed',
    TEAM_CHANGED_NAME: 'team.name.changed',
    TEAM_CHANGED_AGE: 'team.age.changed',
    TEAM_CHANGED_RANK: 'team.rank.changed',
    TEAM_CHANGED_DATA: 'team.data.changed',
    TEAM_ROSTER_CHANGED: 'team.roster.changed',
    TEAM_ROSTER_UNLOCKED: 'team.roster.unlocked',
    TEAM_DATA_CHANGED: 'team.data.changed',
    EMAIL_SENT: 'email.sent',
    DOUBLES_VALIDATION_SUCCESS: 'doubles.validation.success',
    DOUBLES_VALIDATION_ERROR: 'doubles.validation.error',
    DOUBLES_PAYMENT_ERROR: 'doubles.payment.error',
    DOUBLES_TEAM_VALIDATION_ERROR: 'doubles.team.validation.error',
    CLUB_ENTERED: 'club.entered',
    CLUB_DELETED: 'club.deleted',
    TEAM_ENTERED: 'team.entered',
    TEAM_DELETED: 'team.deleted',
    CLUB_HOUSING_NOTE: 'club.housing.note',
    CLUB_MARKED_LOCAL: 'club.marked.local',
    CLUB_MARKED_NOT_LOCAL: 'club.marked.notlocal',
    CLUB_LOCATION_CHANGED: 'club.location.changed',
    CLUB_INVOICE_REFUND_FULL: 'club-invoice.purchase.refund.full',
    CLUB_INVOICE_CANCEL: 'club-invoice.purchase.cancel',
    EVENT_PAYMENT_METHOD_REMOVED: 'event.payment.method.removed',

    /*
    * Actions for club_history table
    * */
    CLUB_TEAM_ROSTER_ATHLETE_REMOVED_BY_SYSTEM: 'team.roster.athlete.removed.system',
    CLUB_TEAM_ROSTER_STAFF_REMOVED_BY_SYSTEM: 'team.roster.staff.removed.system',
    CLUB_STAFF_SANCTIONING_USAV_REMOVED_BY_SYSTEM: 'staff.sanctioning.usav.removed.system',
    CLUB_ATHLETE_SANCTIONING_USAV_REMOVED_BY_SYSTEM: 'athlete.sanctioning.usav.removed.system',
    CLUB_ATHLETE_DELETED_BY_SYSTEM: 'athlete.deleted.system',
    CLUB_STAFF_DELETED_BY_SYSTEM: 'staff.deleted.system',

}
