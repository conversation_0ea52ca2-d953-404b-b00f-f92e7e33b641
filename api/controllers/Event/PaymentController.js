'use strict';

const co = require('co');
const swUtils = require('../../lib/swUtils');

var ORDER_ALIASES = {
    'created'                   : 'p.created',
    'date_paid'                 : 'p.date_paid',
    'amount'                    : 'p.amount',
    'status'                    : 'p.status',
    'type'                      : 'p.type',
    'purchase_id'               : 'p.purchase_id',
    'teams_count'               : 'teams_count',
    'club_name'                 : 'rc.club_name',
    'stripe_balance_available'  : 'p.stripe_balance_available',
    'collected_sw_fee'          : 'p.collected_sw_fee',
    'additional_fee_amount'     : 'p.additional_fee_amount'
};

module.exports = {
    // GET /api/event/:event/payments
    index: function (req, res) {
    
        let $limit = parseInt(req.query.limit, 10);
        let $page = parseInt(req.query.page, 10);
    
        if(!$limit || swUtils.isNumberLessThanZero($limit)) {
            $limit = 100;
        }
        if(!$page || swUtils.isNumberLessThanZero($page)) {
            $page = 1;
        }
        
        let $event_id       = +req.params.event;
        let $roster_club    = +req.query.roster_club;
        let $roster_team_id = +req.query.team;
        let orderBy         = req.query.order;
        let sortDirection   = req.query.direction;
        let limit           = $limit * $page;
        let availability    = req.query.availability;
        let type            = req.query.type;
        let search          = swUtils.escapeStr(req.query.search || '');
        let status          = req.query.statuses;

        if(!$event_id) {
            return res.validation('Invalid Event Identifier');
        }

        let selection =
            ['p.purchase_id', 'p.date_refunded', 'p.canceled_date', 'p.amount', 'p.status', 'rc.club_name',
             'p.type', 'p.memo', 'p.check_num',  'p.email', 'p.phone', 'p.roster_club_id', 'p.notes', 'received_amount',
             'p.stripe_charge_id',  'p.card_name', 'p.card_last_4', 'p.status', 'p.stripe_balance_available',
             'p.amount_refunded', 'p.collected_sw_fee', 'p.additional_fee_amount', 'e.teams_sw_fee_payer',
             'e.stripe_teams_fee_payer', 'e.payment_hub_teams_fee_payer', 'p.payment_provider'
            ];

        let query = squel.select().from('purchase', 'p')
            .fields(selection)
            .field('p.amount_refunded', 'total_refunded_amount')
            .field(`(p.dispute_status IS NOT NULL AND p.dispute_status <> 'won')`, 'has_not_won_dispute')
            .field('p.dispute_status')
            .field('e.stripe_teams_fixed', 'stripe_fixed')
            .field(`TO_CHAR((p.dispute_created::timestamptz AT TIME ZONE e.timezone), 'MM/DD/YYYY HH12:MI am') "dispute_created"`)
            .field(`TO_CHAR((p.date_refunded::timestamptz AT TIME ZONE e.timezone), 'MM/DD/YYYY HH12:MI am') "refunded"`)
            .field('(EXTRACT(EPOCH FROM p."received_date") * 1000)::BIGINT', 'received_at')
            .field('COUNT(pt.*)::INTEGER', 'teams_count')
            .field(
                squel.case()
                    .when('p.stripe_percent IS NULL AND e.stripe_teams_percent IS NULL')
                    .then(squel.str('0::INT'))
                    .else(squel.str('COALESCE(p.stripe_percent, e.stripe_teams_percent) / 100'))
                , 'percent')
            .field('(p.date_paid IS NOT NULL AND p.check_num IS NOT NULL)', 'received')
            .field(`TO_CHAR((p.created::timestamptz AT TIME ZONE e.timezone), 'MM/DD/YYYY, HH12:MI am') "created"`)
            .field(`TO_CHAR(p.date_paid::TIMESTAMPTZ AT TIME ZONE e.timezone, 'MM/DD/YYYY, HH12:MI am') "date_paid"`)
            .field(`
                (   
                    COALESCE((INITCAP(u.first) || ' '), '') || COALESCE((initcap(u.last)), '') || (
                        CASE  
                            WHEN p.event_owner_id <> 0 THEN ' (Event Owner)'  
                                WHEN p.club_owner_id <> 0 THEN ' (Club Director)'
                                ELSE ''  
                            END
                        ) 
                    )
                `, 'payer')
            .field(`
                (
                    SELECT ARRAY_AGG(ROW_TO_JSON(pts)) 
                        FROM (
                            SELECT DISTINCT ON (d.division_id) d.short_name, d.division_id
                            FROM  division d
                            LEFT JOIN purchase_team pt 
                              ON d.division_id = pt.division_id 
                            WHERE pt.purchase_id = p.purchase_id
                        ) "pts"
                    )
                `, 'divisions')
            .field(`
                (
                    SELECT ARRAY_AGG(ROW_TO_JSON(tms))
                        FROM (
                            SELECT rt.team_name, rt.organization_code, div.short_name "division_name", 
                                rt.roster_team_id, COALESCE(pt."division_fee", pt."event_fee") "reg_fee",
                                rt.discount, pt.surcharge
                            FROM roster_team rt
                            LEFT JOIN purchase_team pt
                                ON rt.roster_team_id = pt.roster_team_id
                            LEFT JOIN division div
                                ON rt.division_id = div.division_id
                            WHERE pt.purchase_id = p.purchase_id
                        ) "tms"
                )
            `, 'teams')
            // WARNING!
            //
            // If the team has a discount at the stage of creating payments
            // and if you refund payment of any other team,
            // the team with a discount will also be marked as REFUND.
            .field(
                `(
                    SELECT ARRAY_AGG(ROW_TO_JSON(rfnd))
                        FROM (
                            SELECT 
                                COALESCE(pt.division_fee, pt.event_fee) "amount", 
                                (p.status = 'canceled') "is_full",
                                r_t.team_name, 
                                d.short_name "division",
                                (
                                    CASE
                                        WHEN pt.canceled IS NOT NULL
                                        THEN COALESCE(pt.division_fee, pt.event_fee) + COALESCE(d.credit_surcharge, e.credit_surcharge, 0)
                                        ELSE r_t.discount
                                    END
                                ) discount
                            FROM purchase pur
                            LEFT JOIN purchase_team pt
                              ON pt.purchase_id = pur.purchase_id
                            LEFT JOIN roster_team r_t
                              ON r_t.roster_team_id = pt.roster_team_id
                            LEFT JOIN division d
                              ON d.division_id = r_t.division_id  
                            WHERE pur.date_refunded IS NOT NULL
                              AND pur.type IN ('card','ach')
                              AND pur.purchase_id = p.purchase_id
                              AND (pt.canceled IS NOT NULL OR pt.amount < COALESCE(pt.division_fee, pt.event_fee, 0))
                        ) "rfnd"
                )`
            , 'refunds')
            .field(`count(p.*) OVER()::INT`, 'count')
            .field('teams_balance_info');

        query.left_join('user', 'u', 'p.user_id = u.user_id');
        query.left_join('roster_club', 'rc', 'rc.roster_club_id = p.roster_club_id');
        query.left_join('purchase_team', 'pt', 'pt.purchase_id = p.purchase_id AND p.event_id = pt.event_id');
        query.left_join('event', 'e', 'e.event_id = p.event_id');

        query.where("p.payment_for = 'teams' AND p.event_id = ?", $event_id);

        query.group(
            "u.first, u.last, e.timezone, p.received_date, e.credit_surcharge, e.stripe_teams_fixed, " +
            "e.stripe_teams_percent, "
            + selection.join(',')
        );

        if (orderBy && (sortDirection === 'asc' || sortDirection === 'desc')) {
            if (orderBy === 'collected_sw_fee' || orderBy === 'additional_fee_amount') {
                query.order(`p.type = 'card'`, false);
            }

            query.order(ORDER_ALIASES[orderBy], sortDirection==='asc');
        } else {
            query.order("p.created", false);
        }

        if(limit && Number.isInteger(limit)) {
            query.limit(limit);
        }

        if($roster_club) {
            query.where("p.roster_club_id = ?", $roster_club);
        }

        if($roster_team_id) {
            query.where("pt.roster_team_id = ?", $roster_team_id);
        }

        if(availability && availability === 'null') {
            query.where("p.stripe_balance_available IS NULL AND p.type = 'card'");
        } else if (availability === 'not_null') {
            query.where("p.stripe_balance_available IS NOT NULL");
        }

        if(type) {
            if(_.isArray(type)) {
                query.where(`p.type IN ('${type.join("','")}')`);
            } else {
                query.where("p.type = ?", type);
            }
        }

        if (status) {
            let statusExpr = squel.expr();

            if (status === 'paid' || status.includes('paid')) {
                statusExpr.or("p.status = 'paid'");
            }

            if (status === 'canceled' || status.includes('canceled')) {
                statusExpr.or("p.status = 'canceled' OR p.canceled_date IS NOT NULL");
            }

            if (status === 'pending' || status.includes('pending')) {
                statusExpr.or("p.status = 'pending' AND p.canceled_date IS NULL");
            }

            if(status === 'disputed' || status.includes('disputed')) {
                statusExpr.or('p.dispute_status IS NOT NULL')
            }

            query.where(statusExpr);
        }

        if(search) {
            let formattedSearch = '%' + search + '%';

            let teamsSearchQuery =
                squel.select().from('roster_team', 'rst')
                    .field('rst.team_name')
                    .left_join('purchase_team', 'ptm', 'rst.roster_team_id = ptm.roster_team_id')
                    .where('ptm.purchase_id = p.purchase_id')
                    .where('rst.organization_code::text ILIKE ? OR rst.team_name::text ILIKE ?', formattedSearch, formattedSearch);

            let expr = squel.expr()
                .and('p.purchase_id::text ILIKE ?', formattedSearch)
                .or(`EXISTS (${teamsSearchQuery})`)
                .or('rc.club_name::text ILIKE ?', formattedSearch);

            query.where(expr);
        }

        Db.query(query)
        .then(result => {

            res.status(200).json({ payments: result.rows });
        }).catch(res.customRespError.bind(res));
    },

    // GET /api/event/:event/payment/:purchase/teams
    teams: function (req, res) {
        let purchase_id = +req.params.purchase;
        let event_id    = +req.params.event;

        if(!purchase_id) {
            return res.validation('Invalid purchase id passed');
        }

        if (!event_id) {
            return res.validation('Invalid event id passed');
        }

        let query = 
            `SELECT  
                pt.purchase_team_id, pt.amount, pt.canceled, pt.division_fee, pt.event_fee, pt.division_id, 
                COALESCE(pt."division_fee", pt."event_fee") "reg_fee",
                pt.event_id, pt.roster_team_id, pt.surcharge, rt.status_paid,    
                rt.team_name, rt.discount, d.name "division_name",
                COALESCE(pt.sw_fee, e.teams_entry_sw_fee) "sw_fee"
            FROM "purchase_team" pt   
            LEFT JOIN "roster_team" rt    
              ON rt.roster_team_id = pt.roster_team_id   
            LEFT JOIN "division" d    
              ON d.division_id = pt.division_id    
            INNER JOIN "purchase" p 
                ON p.purchase_id = pt.purchase_id 
                AND p.payment_for = 'teams' 
            JOIN event e ON e.event_id = p.event_id    
            WHERE pt.purchase_id = $1  
              AND pt.event_id = $2
            ORDER BY d.sort_order, d.gender, d.max_age DESC, d.level_sort_order, d.level`;

        Db.query(query, [purchase_id, event_id])
        .then(result => result.rows.map(item => {

            item.amount         = +item.amount || 0;
            item.division_fee   = +item.division_fee || 0;
            item.event_fee      = +item.event_fee || 0;
            item.surcharge      = +item.surcharge || 0;
            item.discount       = +item.discount || 0;
            item.reg_fee        = +item.reg_fee || 0;
            item.sw_fee         = +item.sw_fee || 0;

            return item;
        }))
        .then(payment_teams => {
            res.status(200).json({ payment_teams });
        })
        .catch(res.customRespError.bind(res));
    },
    // put /api/event/:event/payment/:purchase/receive
    receive: function (req, res) {
        let purchaseID     = Number(req.params.purchase);
        let eventID        = Number(req.params.event);

        let {
            date_paid       : datePaid, 
            check_num       : checkNum, 
            received_amount : receivedAmount
        } = req.body;

        TeamsPaymentService.receiveCheckPayment({
            purchaseID,
            eventID,
            datePaid,
            checkNum,
            receivedAmount
        })
        .then(() => {
            res.ok();
        }).catch(res.customRespError.bind(res));
    },
    // get /api/event/:event/payment/:purchase/refund
    refund: async function (req, res) {
        let purchaseID = parseInt(req.params.purchase, 10),
            eventID    = parseInt(req.params.event, 10),
            userID     = req.user.user_id;

        if(!purchaseID) {
            return res.validation('No payment identifier specified');
        }

        try {
            let teams = await TeamsPaymentService.refund.full.proceed(purchaseID, userID);

            if(teams && teams.length) {
                __paymentCanceledNotification('payment.refunded', teams, eventID, purchaseID);
            }

            res.status(200).json({ date_refunded: new Date().getTime() });
        } catch (err) {
            res.customRespError(err);
        }

    },
    // post /api/event/:event/payment/:purchase/refund/partial
    partial_refund: function (req, res) {
        const userID     = req.user.user_id;
        
        req.body.payment.event_id = parseInt(req.params.event, 10);

        TeamsPaymentService.refund.partial.proceed(req.body.payment, req.body.teams, userID)
        .then(() => {
            res.status(200).send('OK');
        })
        .catch(res.customRespError.bind(res));
    },
    pay: function (req, res) {
        res.ok();
    },
    // POST /api/event/:event/purchase
    save: function (req, res) {
        let eventID = parseInt(req.params.event, 10);

        req.user.phone = req.user.phone || '';

        TeamsPaymentService.makePayment({
            event_id        : eventID,
            receipt         : req.body.receipt,
            type            : req.body.type,
            total           : parseFloat(req.body.total, 10),
            user            : _.pick(req.user, 'user_id', 'email', 'phone', 'first', 'last'),
            ip              : req.headers['x-forwarded-for'] || req.connection.remoteAddress,
            season          : sails.config.sw_season.current,
            token           : req.body.token,
            roster_club_id  : parseInt(req.body.roster_club_id, 10),
            event_owner_id  : eventOwnerService.findId(eventID, req.user),
            discount        : req.body.discounts || {},
            check_num       : req.body.check_num,
            received_at     : req.body.received_at
        }, TeamsPaymentService.EVENT_OWNER_PAYMENT_MODE).then(id => {

            res.status(200).json({ id });

        }).catch(res.customRespError.bind(res));
    },
    // post /api/event/:event/purchase/:purchase/cancel
    cancel: function (req, res) {
        let eventID    = parseInt(req.params.event, 10),
            purchaseID = parseInt(req.params.purchase, 10);

        if(!eventID) {
            return res.validation('Invalid event identifier');
        }

        let eventOwnerID        = eventOwnerService.findId(eventID, req.user),
            purchaseTeamList    = req.body.purchase_teams;

        TeamsPaymentService.voidCheckPayment(
            eventID, purchaseID, null, eventOwnerID, req.user.user_id, purchaseTeamList)
        .then(data => {
            let {teams, isFullVoid, dateRefunded, canceled} = data;

            if(teams && teams.length) {
                __paymentCanceledNotification('payment.canceled', teams, eventID, purchaseID)
            }

            res.status(200).json({ is_full: isFullVoid, canceled, dateRefunded });
        })
        .catch(res.customRespError.bind(res));
    },
    save_note: function (req, res) {
        var purchase_id = req.param('purchase'),
            notes = req.body.notes;
        purchaseDataManager.saveNote(purchase_id, notes, function (err) {
            if (err) return res.serverError();
            return res[200]();
        })
    },
    // get /api/event/:event/payments/fingerprint/:fingerprint
    fingerprintCharges: function (req, res) {
        co(function* () {
            let $fingerprint = req.params.fingerprint;

            if(!$fingerprint) { 
                throw {
                    validation: 'Invalid Card Fingerprint'
                }
            }

            let dbResult = yield (
                Db.query(
                    `SELECT 
                         TO_CHAR(p.created::TIMESTAMPTZ AT TIME ZONE e.timezone, 'Mon DD, YYYY, HH12:MI AM') "created",
                         p.amount, p.payment_for, p.status,
                         e.name "event_name", p.amount_refunded
                     FROM "purchase" p 
                     INNER JOIN "event" e 
                         ON e.event_id = p.event_id
                     WHERE LOWER(TRIM(p."stripe_card_fingerprint")) = LOWER(TRIM($1))
                     ORDER BY p.created DESC
                     LIMIT 50`,
                    [$fingerprint]
                )
            )

            return dbResult.rows;
        }).then(payments => {
            res.status(200).json({ payments });
        }).catch(err => {
            res.customRespError(err);
        })
        
    },

    // put /api/event/:event/payment/:purchase/item/:purchase_team/replace-team
    replaceTeam: function (req, res) {
        let eventID                   = Number(req.params.event),
            purchaseID                = Number(req.params.purchase),
            purchaseTeamID            = Number(req.params.purchase_team),
            oldRosterTeamID           = Number(req.body.old_team_id),
            newRosterTeamID           = Number(req.body.team_id),
            userID                    = Number(req.user.user_id);

        TeamsPaymentService.changeTeamInPayment({
            eventID, purchaseID, purchaseTeamID, oldRosterTeamID, newRosterTeamID, userID
        }).then(() => {
            res.status(200).send('OK');
        }).catch(res.customRespError.bind(res));
    },

    // get /api/event/:event/payment/:purchase/state
    getPaymentState: function (req, res) {
        let eventID         = Number(req.params.event),
            purchaseID      = Number(req.params.purchase);

        TeamsPaymentService.getPaymentState(eventID, purchaseID)
        .then(payment => {
            res.status(200).json({ payment });
        })
        .catch(res.customRespError.bind(res));
    }
}

function __paymentCanceledNotification (action, teams, event_id, purchase_id, cb) {
    var message = 'Payment for following teams was ', teams_names = '';

    switch(action) {
        case 'payment.refunded':
            message += 'refunded';
            break;
        case 'payment.refunded.partial': /* This action is not in use */
            message += 'partially refunded';
            break;
        case 'payment.canceled':
            message += 'canceled';
            break;
    }
    
    for(var i = 0, l = teams.length; i < l; ++i) {
        if(teams_names.length) teams_names += ', '
        teams_names += '"' + teams[i].team_name + '"'
    }
    
    Db.query(
        'INSERT INTO "event_change" ("event_id", "action", "comments", "purchase_id") \
        VALUES($1, $2, $3, $4)',
        [event_id, action, (message + ': ' + teams_names), purchase_id]
    ).then(() => {
        if(cb) cb();
    }).catch(err => {
        if(cb) {
            cb(err);
        }
        else {
            throw err;
        }
    });
}
