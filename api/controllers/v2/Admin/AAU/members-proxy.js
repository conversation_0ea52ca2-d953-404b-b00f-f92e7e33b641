const fetch= require('node-fetch');
const querystring = require("querystring");
const aauProxyUrl = sails.config.aau.proxyUrl;
const aauProxyKey = sails.config.aau.proxyKey;

module.exports = {
    friendlyName: 'Get Members',
    description: 'Get Members list',
    inputs: {
        club_code: {
            type: 'string',
            description: 'Club Code',
        },
        membership_identifier: {
            type: 'string',
            description: 'Membership Identifier',
        },
        zip_code: {
            type: 'string',
            description: 'Zip Code',
        },
        birth_date: {
            type: 'string',
            description: 'Birth Date',
        },
        last_name: {
            type: 'string',
            description: 'Birth Date',
        }
    },
    exits: {
        success: {
            statusCode: 200
        }
    },
    fn: async function (inputs, exits) {
        let options = {
            method: 'GET',
            headers: {
                "Content-type": "text/xml"
            }
        };

        const qs = querystring.stringify({ ...inputs, aau_proxy_key: aauProxyKey });
        const url = `${aauProxyUrl}?${qs}`;

        fetch(url, options)
            .then (response => response.json())
            .then((members) => exits.success(members))
            .catch(err => this.res.customRespError(err));
    },
};
