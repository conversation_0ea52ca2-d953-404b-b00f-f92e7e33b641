module.exports = {
    friendlyName: 'Purchase status',
    description: 'Returns purchase status',

    fn: async function() {
        const paymentIntentID = this.req.query.payment_intent_id;
        const paymentHubPaymentIntentId = this.req.query.payment_hub_payment_intent_id;

        if(!paymentIntentID && !paymentHubPaymentIntentId) {
            return this.res.validation('Payment Data ID required');
        }

        try {
            const purchase = await __getPaymentStatus({ paymentIntentID, paymentHubPaymentIntentId });

            return { purchase };
        } catch(err) {
            this.res.customRespError(err);
        }
    }
};

function __getPaymentStatus ({paymentIntentID, paymentHubPaymentIntentId}) {
    let query = knex('purchase AS p')
        .select('status', 'purchase_id', 'date_paid')
        
    if(paymentIntentID) {
        query.where('p.payment_intent_id', paymentIntentID);

    }else {
        query.where('p.payment_hub_payment_intent_id', paymentHubPaymentIntentId);
    }

    return Db.query(query).then(result => result?.rows?.[0]);
}


