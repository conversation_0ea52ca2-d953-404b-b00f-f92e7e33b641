'use strict';

const path = require('path');
const spawn = require('child_process').spawn;
const swUtils = require('../../../../lib/swUtils');

const SAILS_CONNECTION = sails.config.connections[sails.config.db.connection];

class ScheduleService {
	constructor (db) {
		this._db = db;
	}

    /**
     * For cases, when a length of the officials list retrieved is less the current required 
     * assignments count, we have to remove assignments for missing slots
     */
    get REMOVE_ASSIGNMENT () {
        return (
            `DELETE FROM "event_official_schedule"
              WHERE "event_id" = $1
                 AND "division_id" = $2
                 AND "match_name" = $3      
                 AND "ref_num" = $4
              RETURNING 
                 'removed'::TEXT "action", "event_official_schedule_id", "ref_num"`
        );
    }

    /**
     * When we received "null" in a certain position,
     * that means we have to leave this slot as a required one and 
     * remove it's content.
     * To mark the slot as a required, we need to fill it with 
     * the default value of "event_official_id" = -1
     */
	get REPLACE_ASSIGNMENT_WITH_DEFAULT_SQL () {
		return (
            `WITH "remove_assignment" AS (
                DELETE FROM "event_official_schedule"
                 WHERE "event_id" = $1
                    AND "division_id" = $2
                    AND "match_name" = $3      
                    AND "ref_num" = $4
                 RETURNING *
            ), "insert_default" AS ( 
                 INSERT INTO "event_official_schedule" (
                    "event_id", "division_id", "match_name", 
                    "ref_num", "event_official_id", "court_id", 
                    "match_start_time"
                 ) SELECT 
                    $1, $2, $3,
                    $4, $5, $6,
                    $7
                RETURNING *
            )
            SELECT * FROM "remove_assignment" 
            UNION ALL 
            SELECT * FROM "insert_default"`
		);
	}

    get DEFAULT_EVENT_OFFICIAL_ID () {
        return -1;
    }

	get OFFICIAL_ASSIGNMENT_SQL () {
		return (
			`WITH check_official AS (
			    SELECT 
                    'validated'::TEXT "action", 
                    NULL::INTEGER "event_official_schedule_id", 
                    NULL::INTEGER "ref_num"
			    FROM "event_official" eof
			    WHERE eof.event_official_id = $5
			        AND eof.work_status = 'approved'
			        AND eof.event_id = $1
			), remove_official AS (
			    DELETE FROM "event_official_schedule" 
			    WHERE "event_id" = $1 
			        AND "division_id" = $2 
			        AND "match_name" = $3 
			        AND "event_official_id" <> $5
			        AND "ref_num" = $4 
			        AND EXISTS (SELECT * FROM check_official) 
			    RETURNING 'removed'::TEXT "action", "event_official_schedule_id", "ref_num" 
			), insert_official AS ( 
			    INSERT INTO "event_official_schedule" ( 
			        "event_id", "division_id", "match_name", "event_official_id", "ref_num",
                    "court_id", "match_start_time"
			    )
			    SELECT 
			        $1, $2, $3, $5, $4, $6, $7
			    WHERE NOT EXISTS ( 
			        SELECT * FROM "event_official_schedule" 
			        WHERE "event_id" = $1 
			            AND "division_id" = $2 
			            AND "match_name" = $3 
			            AND "event_official_id" = $5 
			            AND "ref_num" = $4) 
			            AND EXISTS (SELECT * FROM check_official) 
			    RETURNING 'inserted'::TEXT "action", "event_official_schedule_id", "ref_num"
			)
			 SELECT * FROM remove_official 
			 UNION ALL SELECT * FROM insert_official 
			 UNION ALL SELECT * FROM check_official`);
	}

	get UPDATE_OFFICIALS_QTY_SQL () {
		return (
			`UPDATE "matches" m
 			 SET "officials_assigned" = (
 			 	SELECT COALESCE(COUNT(eos.*), 0)
 			 	FROM "event_official_schedule" eos 
 			 	WHERE eos."match_name" = m.display_name 
 			 		AND eos."division_id" = m.division_id 
 			 		AND eos."event_id" = m.event_id
 			 )
 			 WHERE "match_id"::TEXT = ANY ($1::TEXT[]) 
 			 	AND "event_id" = $2`
		);
	}

	get OFFICIALS_ON_MOVED_MATCHES_SQL () {
		return (
            `SELECT 
            (m.division_id || '-' || m.display_name) "match_id",
            m."display_name" "match_name",

            eos."event_official_id" "official_id",

            FORMAT('%s, %s', u.last, u.first) "official_name",
            eo."schedule_name",

            d."name" "division_name",

            TO_CHAR(m.secs_start, 'HH12:MI AM') "new_start_time",
            TO_CHAR(eos.match_start_time, 'HH12:MI AM') "old_start_time",

            c_new."uuid" "new_court_id",
            c_new."name" "new_court_name",


            c_old."uuid" "old_court_id",
            c_old."name" "old_court_name"
         FROM "event_official_schedule" eos 
         INNER JOIN "event_official" eo 
            ON eo."event_official_id" = eos."event_official_id"
         INNER JOIN "official" o 
            ON o."official_id" = eo."official_id"
         INNER JOIN "user" u 
            ON u."user_id" = o."user_id"
         INNER JOIN "matches" m 
            ON m."display_name" = eos."match_name"
            AND m."division_id" = eos."division_id"
            AND (
                m."court_id"        <> eos."court_id"
                OR m."secs_start"   <> eos."match_start_time"
            )
         INNER JOIN "division" d 
            ON d."division_id" = eos."division_id"
         INNER JOIN "courts" c_old 
            ON c_old."uuid"   = eos."court_id"
            AND c_old."event_id"     = eos."event_id"
         INNER JOIN "courts" c_new 
            ON c_new."uuid"   = m."court_id"
            AND c_new."event_id"     = m."event_id"
         WHERE eos."event_id" = $1 
            AND eos."court_id" IS NOT NULL 
            AND eos."match_start_time" IS NOT NULL`
		);
	}

	get OFFICIALS_LIST_SQL () {
		return (
            'select eof.event_official_id "id", u.first, u.last, eof.schedule_name "name",  \
                    ( \
                        select coalesce(array_to_json(array_agg(row_to_json(matches_list))), \'[]\'::json) \
                        from ( \
                            select  m.display_name "match_name", m.division_id, d.short_name "division",  \
                                    (m.division_id || \'-\' || m.display_name) "match_id", \
                                    (EXTRACT(EPOCH FROM m.secs_start) * 1000)::BIGINT "date_start", \
                                    c.name "court_name",\
                                    eos.published \
                            from "event_official_schedule" "eos" \
                            inner join "matches" "m" \
                                on m.display_name = eos.match_name \
                                and m.division_id = eos.division_id \
                            inner join "division" "d" \
                                on d.division_id = m.division_id \
                            left join "courts" "c" \
                                on c.uuid = m.court_id \
                            where eos.event_id = eof.event_id \
                                and eos.event_official_id = eof.event_official_id \
                        ) "matches_list" \
                    ) "matches", \
                    o.rank, eof.additional_restrictions, eof.departure_time, eof.schedule_availability, \
                    oar.name "official_additional_role" \
                from "official" "o" \
                inner join "user" "u" \
                    on u.user_id = o.user_id \
                inner join "event_official" "eof" \
                    on eof.official_id = o.official_id \
                    and eof.event_id = $1 \
                    and eof.work_status = \'approved\' \
                    and eof.is_official = true \
                    and eof."deleted" IS NULL \
                left join "event_official_additional_role" "eoar" \
                    on eoar.event_official_id = eof.event_official_id \
                left join "official_additional_role" "oar" \
                    on oar.official_additional_role_id = eoar.official_additional_role_id \
                where o.rank is not null'
		);
	}

	get EVENT_INFO_SQL () {
		return (
            `SELECT e.event_id, e.name, e.long_name, e.reg_fee, e.has_coed_teams, e.has_female_teams, 
                (EXTRACT(EPOCH FROM e.date_start) * 1000)::BIGINT date_start, 
                (EXTRACT(EPOCH FROM e.date_end) * 1000)::BIGINT date_end, 
                e.has_male_teams, e.email, e.website, e.has_rosters, e.city, e.state, e.address, e.schedule_published, 
                e.registration_method, e.has_officials, e.has_match_barcodes, e.social_links, 
                (EXTRACT(EPOCH FROM e.date_reg_open) * 1000)::BIGINT date_reg_open, 
                (EXTRACT(EPOCH FROM e.date_reg_close) * 1000)::BIGINT date_reg_close, 
                (EXTRACT(EPOCH FROM e.roster_deadline) * 1000)::BIGINT roster_deadline, 
                TO_CHAR((NOW() AT TIME ZONE e.timezone), 'YYYY-MM-DD') cur_date, 
                (
                    SELECT ARRAY_TO_JSON(ARRAY_AGG(ranks_arr.rank)) 
                    FROM ( 
                        SELECT distinct o.rank
                        FROM event_official eof 
                        INNER JOIN official o 
                            ON o.official_id = eof.official_id 
                            AND o.rank IS NOT NULL
                        WHERE eof.event_id = e.event_id 
                            AND eof.work_status = 'approved'
                            AND eof."deleted" IS NULL
                    ) "ranks_arr" 
                ) "ranks", 
                ( 
                    SELECT
                        FORMAT(
                            '{%s}', 
                            STRING_AGG(
                                FORMAT('%s: { "matches": %s, "divisions": %s, "champdesks": %s, "waves": %s }', 
                                        TO_JSON("days_matches"."day"), 
                                        TO_JSON("days_matches"."match_hours"),
                                        TO_JSON("days_matches"."divisions"),
                                        TO_JSON("days_matches"."champdesks"),
                                        TO_JSON("days_matches"."waves")
                                        ), 
                                ','
                            )
                        )::JSON
                    FROM (
                        SELECT 
                            "days"."day", 
                            (
                                SELECT COALESCE(ARRAY_TO_JSON(ARRAY_AGG(TO_CHAR(secs.secs_start, 'HH12:MI AM'))), '[]'::JSON)
                                FROM (
                                    SELECT DISTINCT date_trunc('minutes', m.secs_start) "secs_start"
                                    FROM "matches" m
                                    WHERE m.event_id = $1
                                        AND TO_CHAR(m.secs_start, 'YYYY-MM-DD') = days.day
                                    ORDER BY date_trunc('minutes', m.secs_start)
                                ) "secs"
                            ) "match_hours", 
                            (
                                SELECT COALESCE(ARRAY_TO_JSON(ARRAY_AGG(ROW_TO_JSON("d"))), '[]'::JSON)
                                FROM (
                                    SELECT 
                                        DISTINCT ON (d.division_id) 
                                        d.division_id, d.name "division_name", d.gender
                                    FROM "matches" m 
                                    INNER JOIN "division" d 
                                        ON d.division_id = m.division_id
                                        AND d.event_id = m.event_id
                                    WHERE m.event_id = $1
                                        AND TO_CHAR(m.secs_start, 'YYYY-MM-DD') = days.day
                                ) "d"
                            ) "divisions",
                            (
                                SELECT COALESCE(ARRAY_TO_JSON(ARRAY_AGG(desks.champdesk)), '[]'::JSON)
                                FROM (
                                    SELECT DISTINCT c.champdesk
                                    FROM "matches" m
                                    INNER JOIN "courts" c ON c.uuid = m.court_id
                                    WHERE m.event_id = $1
                                        AND TO_CHAR(m.secs_start, 'YYYY-MM-DD') = days.day
                                    ORDER BY c.champdesk
                                ) "desks"
                            ) "champdesks",
                            (
                                SELECT COALESCE(ARRAY_TO_JSON(ARRAY_AGG(ww.wave)), '[]'::JSON)
                                FROM (
                                    SELECT DISTINCT m.wave
                                    FROM "matches" m
                                    WHERE m.event_id = $1
                                        AND TO_CHAR(m.secs_start, 'YYYY-MM-DD') = days.day
                                    ORDER BY m.wave
                                ) "ww"
                            ) "waves"
                            
                        FROM (
                            SELECT TO_CHAR(dd, 'YYYY-MM-DD') "day", dd
                            FROM (
                                    SELECT GENERATE_SERIES(e.date_start, e.date_end, '1 day'::INTERVAL) dd
                                    FROM "event" e
                                    WHERE e.event_id = $1
                            ) "event_days"
                        ) "days"
                        GROUP BY days.day, days.dd
                        ORDER BY days.dd
                    ) "days_matches"
                ) "event_dates", 
                ( 
                    SELECT to_char(m.secs_start, 'HH24')::INT "time" 
                    FROM matches m
                    WHERE m.event_id = e.event_id 
                    ORDER BY m.secs_start
                    LIMIT 1
                ) "first_day_hour", 
                (
                    SELECT COALESCE(MAX(eog.event_official_group_id), 0) 
                    FROM "event_official_group" eog 
                    WHERE eog.event_id = e.event_id
                ) "max_official_group_id",
                e.official_additional_role_enable
            FROM "event" e  
            WHERE e.has_officials IS TRUE 
            AND e.event_id = $1`
		);
	}

	get COURTS_2_SQL () {
		return (
            `SELECT c."court_id", c.name, c.champdesk 
            FROM (
                SELECT 
                    DISTINCT c.uuid "court_id", c.name, c.champdesk, c.sort_priority
                FROM "courts" c 
                INNER JOIN "matches" m 
                    ON m.court_id = c.uuid
                    AND TO_CHAR(m.secs_start, 'YYYY-MM-DD') = $2
                WHERE c.event_id = $1
            ) "c" ORDER BY c.sort_priority`
		);
	}

	get APPLY_PATTERN_SQL () {
		return (
            `INSERT INTO "event_official_group" ("group_name", "pattern_id", "event_id")
            VALUES( $1, $2, $3 ) RETURNING "event_official_group_id"`
		);
	}

	get __APPLY_OFFICIAL_PATTERN_2_SQL () {
		return (
            `WITH "remove_existing" AS (
                DELETE FROM "event_official_schedule" eos
                WHERE "event_id" = $1 
                    AND "division_id" = $2 
                    AND "match_name" = $3 
                    AND "ref_num" = $5 
                RETURNING 'removed'::TEXT "type", eos.event_official_id
             ), "insert_official" AS (
                INSERT INTO "event_official_schedule" ( 
                     "event_id", "division_id", "match_name", "event_official_id", "ref_num",
                     "event_official_group_id"
                 )
                 SELECT 
                      $1, $2, m.display_name, $4, $5, $6
                 FROM "matches" m 
                 WHERE m.event_id = $1 
                     AND m.division_id = $2
                     AND m.display_name = trim($3)
                 RETURNING 'inserted'::TEXT "type", "event_official_id"
             ) 
             SELECT * FROM "remove_existing" UNION ALL SELECT * FROM "insert_official"`
		);
	}

	get PUBLISH_OFFICIAL_ASSIGNMENTS_SQL () {
		return (
            `WITH available_matches AS (
                SELECT
                    m.display_name,
                    m.division_id
                FROM matches m
                WHERE m.event_id = $1
                  AND to_char(m.secs_start, 'YYYY-MM-DD') = $3
                  AND to_char(m.secs_start, 'HH24') :: INT BETWEEN $4 AND $5
            ), eos_to_update AS (
                SELECT eos.event_official_schedule_id
                FROM event_official_schedule eos
                    INNER JOIN available_matches am ON
                        am.display_name = eos.match_name AND am.division_id = eos.division_id
            ), update_eos AS (
                UPDATE event_official_schedule
                SET published = $2
                WHERE event_official_schedule_id IN (select * from eos_to_update)
                RETURNING event_official_schedule_id
            )
            SELECT (SELECT count (*) FROM available_matches) as matches_found,
                    (SELECT count (*) FROM update_eos) AS assignments_updated`
		);
	}

	get ASSIGNMENTS_LIST_SQL () {
		return (
            `SELECT 
                 e.long_name "event_name", TO_CHAR($2::DATE, 'Mon DD, YYYY') "event_day", (
                     SELECT COALESCE(ARRAY_TO_JSON(ARRAY_AGG(ROW_TO_JSON("o"))), '[]'::JSON)
                     FROM (
                         SELECT 
                             FORMAT('%s, %s', INITCAP(u.last), INITCAP(u.first)) "name", (
                                 SELECT COALESCE(ARRAY_TO_JSON(ARRAY_AGG(ROW_TO_JSON("m"))), '[]'::JSON)
                                 FROM (
                                     SELECT
                                         TO_CHAR(m.secs_start, 'HH12:MI AM') "start_hour",
                                         m.display_name "match_name",
                                         c.name "court_name"
                                     FROM "event_official_schedule" eos
                                     INNER JOIN "matches" m 
                                         ON m.display_name = eos.match_name
                                         AND m.division_id = eos.division_id
                                     INNER JOIN "courts" c 
                                        ON c.uuid = m.court_id
                                     WHERE eos.event_official_id = eof.event_official_id
                                          AND eos.published IS TRUE
                                          AND TO_CHAR(m.secs_start, 'YYYY-MM-DD') = ($2)::TEXT
                                     ORDER BY m.secs_start asc
                                 ) "m"
                             ) "assignments"
                         FROM "event_official" eof 
                         INNER JOIN "official" o 
                             ON o.official_id = eof.official_id
                         INNER JOIN "user" u
                             ON u.user_id = o.user_id
                         WHERE eof.event_id = e.event_id
                             AND eof.work_status = 'approved'
                             AND eof."deleted" IS NULL
                         ORDER BY u.last ASC
                     ) "o"
                 ) "officials"
             FROM "event" e
             WHERE e.event_id = $1`
		);
	}

	get __FIND_HOURS_SQL () {
		return (
            `select DISTINCT on (date_trunc('minutes', m.secs_start)) 
                date_trunc('minutes', m.secs_start) "secs_start", 
                array_to_json(
                    array_agg(
                        (
                            select row_to_json("match_data")
                            from (
                                select m.court_id, c.name court_name,
                                    m.display_name "match_name", (m.division_id || '-' || m.display_name) "match_id",
                                    d.division_id, d.short_name "division", 
                                    extract(epoch from m.secs_start)::BIGINT * 1000 "date_start", 
                                    COALESCE(
                                        NULLIF(m.officials_required, 0), 
                                        1
                                    ) "officials_required", 
                                    m.officials_assigned, ( 
                                        select coalesce(array_to_json(array_agg(row_to_json(o))), '[]'::json) 
                                        from ( 
                                            SELECT 
                                                eos.event_official_id "id", eos.ref_num::INT, 
                                                FORMAT('%s, %s', u.last, u.first) "name", 
                                                eos.published 
                                            FROM "event_official_schedule" eos  
                                            INNER JOIN "event_official" eo 
                                                ON eo.event_official_id = eos.event_official_id 
                                                AND eo."deleted" IS NULL 
                                                AND eo."work_status" = 'approved'
                                            left join "official" o 
                                                on o.official_id = eo.official_id 
                                            left join "user" u 
                                                on u.user_id = o.user_id 
                                            where eos.match_name = m.display_name 
                                                and eos.division_id = m.division_id 
                                            order by eos.ref_num 
                                        ) "o" 
                                    ) "officials_list"
                            ) "match_data"
                            order by c.sort_priority
                        )
                    )
                ) "matches"
            from "matches" m
            left join "courts" c 
                ON m.court_id = c.uuid
            LEFT JOIN division d  
                ON d.division_id = m.division_id 
            where m.event_id = 43
                AND TO_CHAR(m.secs_start, 'YYYY-MM-DD') = '2016-02-13'
            group by date_trunc('minutes', m.secs_start)
            ORDER BY date_trunc('minutes', m.secs_start)`
		);
	}

	get REMOVE_OFFICIAL_SQL () {
		return (
            `DELETE FROM "event_official_schedule"
            WHERE "event_id" = $1
               AND "division_id" = $2
               AND "match_name" = $3
               AND "event_official_id" = $4`
		);
	}

	get COURTS_MATCHES_DEFAULT_HOUR_SQL () {
		return (
            ' SELECT EXTRACT(HOUR FROM NOW() AT TIME ZONE e.timezone) "hour" \
            FROM "event" e  \
            WHERE e.event_id = $1  \
              AND to_char((now() AT TIME ZONE e.timezone), \'YYYY-MM-DD\') = $2'
		);
	}

	get COURTS_MATCHES_HOURS_SQL () {
		return (
            'SELECT DISTINCT to_char(m.secs_start, \'HH24\')::INT "time", \
                to_char(m.secs_start, \'FMHH12 AM\') "time12"  \
            FROM matches m  \
            LEFT JOIN event e ON m.event_id = e.event_id  \
            WHERE m.event_id = $1  \
                AND to_char(m.secs_start, \'YYYY-MM-DD\') = $2 \
            ORDER BY to_char(m.secs_start, \'HH24\')::INT'
		);
	}

	get COURTS_MATCHES_DIVISIONS_SQL () {
		return (
            'SELECT DISTINCT d.division_id, d.name division_name, d.gender \
            FROM matches m \
            LEFT JOIN event e ON m.event_id = e.event_id \
            LEFT JOIN division d ON d.division_id = m.division_id \
            WHERE m.event_id = $1  \
            ORDER BY d.name'
		);
	}

	get COURTS_MATCHES_SQL () {
		return (
            `SELECT  mt.court_id, c.name court_name, 
                (SELECT coalesce(array_to_json(array_agg(row_to_json(ctmatches))), '[]'::json) 
                        FROM ( 
                                SELECT  
                                        m.display_name "match_name", (m.division_id || '-' || m.display_name) "match_id",
                                        d.division_id, d.short_name "division", c.name court_name, 
                                        extract(epoch from m.secs_start)::BIGINT * 1000 "date_start", 
                                        COALESCE(
                                            NULLIF(m.officials_required, 0), 
                                            1
                                        ) "officials_required", ( 
                                            select coalesce(array_to_json(array_agg(row_to_json(o))), '[]'::json) 
                                            from ( 
                                                select 
                                                    eos.event_official_id "id", eos.ref_num::int, 
                                                    format('%s, %s', u.last, u.first) "name", 
                                                    eos.published, eog.group_name, eog.event_official_group_id "group_id"
                                                FROM "event_official_schedule" eos  
                                                LEFT JOIN "event_official" eo 
                                                    ON eo.event_official_id = eos.event_official_id 
                                                    AND eo."deleted" IS NULL 
                                                    AND eo."work_status" = 'approved'
                                                LEFT JOIN "event_official_group" eog 
                                                    ON eog.event_official_group_id = eos.event_official_group_id
                                                LEFT JOIN "official" o 
                                                    ON o.official_id = eo.official_id 
                                                LEFT JOIN "user" u 
                                                    ON u.user_id = o.user_id 
                                                WHERE eos.match_name = m.display_name 
                                                    AND eos.division_id = m.division_id 
                                                ORDER BY eos.ref_num 
                                            ) "o" 
                                        ) "officials_list", 
                                        m.officials_assigned 
                                FROM matches m 
                                LEFT JOIN division d  
                                    ON d.division_id = m.division_id 
                                WHERE m.event_id = mt.event_id   
                                    AND m.court_id = mt.court_id   
                                    AND to_char(m.secs_start, 'YYYY-MM-DD') = $2 
                                    AND to_char(m.secs_start, 'HH24')::INT BETWEEN $3 AND $3 + $4 - 1  
                                ORDER BY m.secs_start   
                        ) "ctmatches"  
                ) "matches" 
                FROM matches mt   
                LEFT JOIN courts c  
                    ON mt.court_id = c.uuid
                left join "event" e 
                    on e.event_id = mt.event_id 
                WHERE mt.event_id = $1 
                    AND to_char(mt.secs_start, 'YYYY-MM-DD') = $2 
                    aND to_char(mt.secs_start, 'HH24')::INT BETWEEN $3 AND $3 + $4  
                GROUP BY e.event_id, mt.event_id, mt.court_id, c.name, c.sort_priority  
                ORDER BY c.sort_priority`
		);
	}

	get MAX_ALLOWED_POSITIONS () {
		return 5;
	}
    
    get MAX_ALLOWED_POSITIONS_FOR_SOME_EVENT () {
        return 8;
    }

	get UUID_V4_PATTERN () {
		return /^[0-9A-F]{8}-[0-9A-F]{4}-4[0-9A-F]{3}-[89AB][0-9A-F]{3}-[0-9A-F]{12}$/i;
	}

    _removeAssignment (db, eventID, divisionID, matchName, refNum) {
        return db.query(this.REMOVE_ASSIGNMENT, [eventID, divisionID, matchName, refNum])
        .then(() => {});
    }

    // covered 😄👍
    _formatMatchSlots (slots) {
        if (slots.length === 0) {
            return [{ is_default: true, ref_num: 1 }];
        } else {
            let maxRefNum   = Number(slots[slots.length - 1].ref_num);
            let result      = [];

            for (let i = 0; i < maxRefNum; ++i) {
                let refNum = (i + 1);
                let slotItem = null;

                for (let official of slots) {
                    let position = Number(official.ref_num);

                    if (position === refNum) {
                        slotItem = official;
                    }
                }

                result.push(slotItem || { is_default: true, ref_num: refNum });
            }

            return result;
        }
    }

    // covered 😄👍
	_getMatchData (displayName, divisionID, eventID) {
        // TODO: get court and date start of the match
		return this._db.query(
            `SELECT 
                m."match_id" "id",
                m."court_id",
                m."secs_start" "match_start_time", (
                    SELECT COALESCE(ARRAY_TO_JSON(ARRAY_AGG(ROW_TO_JSON("assignments"))), '[]'::JSON)
                    FROM (
                        SELECT 
                            eosch."event_official_id",
                            eosch."ref_num"
                        ORDER BY eosch."ref_num" ASC
                    ) "assignments"
                ) "slots"
             FROM "matches" m 
             LEFT JOIN "event_official_schedule" eosch 
                ON eosch."match_name" = m."display_name"
                AND eosch."division_id" = m."division_id"
             WHERE m.display_name = $1
                AND m.division_id = $2
                AND m.event_id = $3`,
			[displayName, divisionID, eventID]
		).then(res => { 
            let match = res.rows[0];

            if (match) {
                match.slots = this._formatMatchSlots(match.slots);

                return match;
            } else {
                return null;
            }
        });
	}

    // covered 😄👍
	_manageOfficialAssignment (eventID, matchName, divisionID, officialID, position, SQLRunner, courtID = null, startTime = null) {
		let SQLParams 		= [eventID, divisionID, matchName,  position],
			officialIDInt 	= parseInt(officialID, 10),
			SQLQuery;
        if(courtID !== null && !swUtils.isUUID(courtID)) {
            return Promise.reject({ validation: `Invalid court ID` });
        }

		if (officialID === null) {
			SQLQuery = this.REPLACE_ASSIGNMENT_WITH_DEFAULT_SQL;
            SQLParams.push(this.DEFAULT_EVENT_OFFICIAL_ID, courtID, startTime);
		} else if (officialIDInt) {
			SQLQuery = this.OFFICIAL_ASSIGNMENT_SQL;
			SQLParams.push(officialID, courtID, startTime);
		} else {
			return Promise.reject({ validation: `Invalid Event Official ID #${officialID}` });
		}

		return SQLRunner.query(SQLQuery, SQLParams)
		.then(result => {
			if (officialID !== null && result.rowCount === 0) {
				return Promise.reject({
	                validation: 
	                    `Official #${officialID} does not match current event or does not approved` 
	            });
			}

			loggers.debug_log.verbose('Officials Assignment Change', result.rows);
		});
	}

    // covered 😄👍
	_convertListToPGArr (list) {
		let stringifiedItems = list.reduce((resultStr, uuid, index) => {
			
			/* "matches" table uses v4 uuid generator */
			if (!this.UUID_V4_PATTERN.test(uuid)) {
				throw new Error(`"${uuid}"" is not a valid v4 UUID`)
			}

			return (index > 0)
					?`${resultStr}, ${uuid}`
					:uuid
		}, '');

		return `{${stringifiedItems}}`;
	}

    // covered 😄👍
	manageAssignment (matches, eventID, tr) {
        /*
        * Example matchExternalID: "123-R3D1GP1M3"
        */

        const SQL_RUNNER = (tr || this._db);

        return Object.keys(matches).reduce((prevStep, matchExternalID) => {

            let officialsList = matches[matchExternalID];

          	if (officialsList.length === 0) {
                return prevStep;
            }

          	return prevStep.then(processedMatches => {
                let _split = matchExternalID.split('-');

              	let divisionID  = parseInt(_split[0], 10),
                    matchName   = _split[1];

                return this._getMatchData(matchName, divisionID, eventID)
                .then(match => {
                    if (!match) {
                        return Promise.reject({ validation: `Match "${matchName}" not found` });
                    }

                	let {
                        id                  : matchID, 
                        court_id            : courtID, 
                        match_start_time    : matchStartTime
                    } = match;

                	let manage = this._manageOfficialAssignment.bind(this, eventID, matchName, divisionID);
                    
                    const checkedEventId = sails.config.environment === 'development' ? 23017 : 24725;
                    let maxAllowedPositions = eventID == checkedEventId ? this.MAX_ALLOWED_POSITIONS_FOR_SOME_EVENT : this.MAX_ALLOWED_POSITIONS;
                    if (officialsList.length > maxAllowedPositions) {
                		return Promise.reject({
                			validation: `Too many positions (${officialsList.length}) for match "${matchName}"`
                		});
                	}

                    let _tasks = [];
                    /**
                     * 
                     */
                    let requiredMatchOfficials = officialsList.length;

                    /*
                    * All officials, that are not in the list, should be removed.
                    * So we make the maximum number of iterations and pass NULL if there is no 
                    * element in current position
                     */
                    for (let i = 0; i < maxAllowedPositions; ++i) {
                        let officialPosition    = (i + 1);
                        let officialID          = officialsList[i];
                        let positionElement     = (officialID === undefined) ? null : officialID;

                        if (officialPosition <= requiredMatchOfficials) {
                            let task = 
                                manage(positionElement, officialPosition, SQL_RUNNER, courtID, matchStartTime);

                            _tasks.push(task);
                        } else {
                            _tasks.push(
                                this._removeAssignment(SQL_RUNNER, eventID, divisionID, matchName, officialPosition)
                            );
                        }
                    }

                    return Promise.all(_tasks)
                    .then(() => {
                        processedMatches.push(matchID);
                        return processedMatches;
                    });
                });
            });
        }, Promise.resolve([]));
    }

    // covered 😄👍
    updateMatchesOfficialsQty (matches, eventID, tr) {
    	if (!Array.isArray(matches)) {
    		return Promise.reject({ validation: 'Expected First Argument (matches list) to be an array'});
    	}

    	if (matches.length === 0) {
    		return Promise.reject({ validation: 'Matches List should not be empty' });
    	}

    	if (!eventID) {
    		return Promise.reject({ validation: 'Event ID required' });
    	}

    	let matchesPGArr = this._convertListToPGArr(matches);

    	return (tr || this._db)
    	.query(this.UPDATE_OFFICIALS_QTY_SQL, [matchesPGArr, eventID])
    	.then(result => result.rowCount);
    }

    _generateChangesMsg (matches) {
        return matches.map(m => {

            let msg = [
                `<b>${m.schedule_name}</b> has changes on match `,
                `<b>${m.division_name} ${m.match_name}</b>`,
                '<br/>'
            ];

            if (m.new_court_id !== m.old_court_id) {
                msg.push(
                    `Court <b>${m.old_court_name}</b> changed to <b>${m.new_court_name}</b>`,
                    '<br/>'
                );
            }

            if (m.new_start_time !== m.old_start_time) {
                msg.push(`Time <b>${m.old_start_time}</b> changed to <b>${m.new_start_time}</b>`);
            }

            m.message = msg.join('');

            return m
        })
    }

    async __findHours () {
        const result = await Db.query(this.__FIND_HOURS_SQL);

        return result.rows || [];
    }

    __findCourts () {
        return [];
    }

    __findDivisions () {
        return [];
    }

    __formatMatches (courts) {
        for (const court of courts) {
            const { original_court_index: courtIndex } = court;
    
            for (const match of court.matches) {
                const {
                    officials_list      : list,
                    officials_required  : offRequired,
                    original_match_index: matchIndex
                } = match;
                match.officials = this.formatOfficialsSlots(list, offRequired, matchIndex, courtIndex);
                match.officials_list        = undefined;
                match.original_court_index  = courtIndex;
            }
        }
    
        return courts;
    }

    async __applyOfficialPattern (tr, officialId, matchesList, eventId, groupId, eventDay, hoursFrom, hoursTo) {
        
        if (!officialId) {
            throw { validation: 'Invalid Official Identifier' };
        }

        const queryParams = [officialId, eventId, eventDay];
        let hoursCheck = '';

        // in case of wave, we should add wave to the query (AM, PM) to be able to assign officials to some match in different waves
        if(hoursFrom) {
            queryParams.push(hoursFrom);
            hoursCheck += ` AND TO_CHAR(m.secs_start, 'HH24')::INT >= $${queryParams.length}`;
        }

        if(hoursTo) {
            queryParams.push(hoursTo);
            hoursCheck += ` AND TO_CHAR(m.secs_start, 'HH24')::INT <= $${queryParams.length}`;
        }

        // remove from event_official_schedule all matches that are in the same day and hours for the official
        const removeSQL =
            `DELETE FROM "event_official_schedule" eos
             WHERE "event_official_id" = $1
                AND "event_id" = $2
                AND EXISTS (
                  SELECT 1
                  FROM "matches" m
                  WHERE m.event_id = eos.event_id
                  AND m."display_name" = eos."match_name"
                  AND m."division_id" = eos."division_id"
                  AND TO_CHAR(m.secs_start, 'YYYY-MM-DD') = $3
                  ${hoursCheck}
                );`;

        await tr.query(removeSQL, queryParams);

        return Promise.all(
            matchesList.map((matchNameFormatted) => {
                const matchNameSplitted   = matchNameFormatted.split('-');
                const divisionId          = Number(matchNameSplitted[0]);
                const matchName           = matchNameSplitted[1];
                    
                return tr.query(
                    this.__APPLY_OFFICIAL_PATTERN_2_SQL,
                    [eventId, divisionId, matchName, officialId, 1, groupId]
                )
            }, this)
        );
    }

    async _getCourtsMatchesHours($event_id, $day, $hour) {
        
        if (!$day) {
            return null;
        }

        const defaultHourResult = await Db.query(
            this.COURTS_MATCHES_DEFAULT_HOUR_SQL,
            [$event_id, $day]
        );
        const defaultHourRow = _.first(defaultHourResult.rows);
        const defaultHour = defaultHourRow && defaultHourRow.hour || null;

        const hoursResult = await Db.query(
            this.COURTS_MATCHES_HOURS_SQL,
            [$event_id, $day]
        );
        const hourResultArr = hoursResult.rows || [];
        const defaultTime = {};

        if ($hour) {
            defaultTime.default = $hour;
        } else if (defaultHour) {
            defaultTime.default = defaultHour;
        } else {
            const row = _.first(resultArr);
            defaultTime.default = row && row.time;
        }

        hourResultArr.push(defaultTime);

        return hourResultArr;
    }

    async _getCourtsMatchesDivisions($event_id, $day) {

        if(!$day) {
            return null;
        }

        const result = await Db.query(
            this.COURTS_MATCHES_DIVISIONS_SQL, 
            [$event_id]
        );

        return result.rows;  
    }

    async _getCourtsMatchesCourts($event_id, $day, $hour, $hoursCount) {
        const result = await Db.query(
            this.COURTS_MATCHES_SQL, 
            [$event_id, $day, ($hour || 8), ($hoursCount || 3)]
        );
        const courts = (result.rows.length) ? this.__formatMatches(result.rows) : [];
        
        return courts;
    }

    async getOfficialsOnMovedMatches (eventID) {
        const result = await Db.query(
            this.OFFICIALS_ON_MOVED_MATCHES_SQL, 
            [eventID]
        );

        return this._generateChangesMsg(result.rows);
    }

    // covered 😄👍
    /**
     * Officials list's length should always equal the quantity of officials allowed for the match.
     * For empty slots, list should contain {} (empty object)
     *
     * @param { Number } requiredCount Required officials quantity. If it is less than passed 
     * officialsList, it means the list is filled with default values, so we should use the list's 
     * length as the required officials quanity instead of requiredCount value.
     */
    formatOfficialsSlots (officialsList, requiredCount, matchIndex, courtIndex) {
        if (officialsList.length === 0) {
            return new Array(requiredCount).fill({});
        }

        let result      = [];
        let slotsQty    = (officialsList.length > requiredCount) ? officialsList.length : requiredCount;

        for (let i = 0; i < slotsQty; ++i) {
            let refNum      = (i + 1);
            let official    = null;

            /**
             * TODO:
             * If item.event_official_id = -1 -> that is a default string, should be represented as
             * an empty object
             */
            for (let item of officialsList) {
                if (Number(item.ref_num) === refNum) {
                    if (Number(item.id) === this.DEFAULT_EVENT_OFFICIAL_ID) {
                        official = {}
                    } else {
                        item.original_match_index = matchIndex;
                        item.original_court_index = courtIndex;

                        official = item;
                    }

                    break;
                }
            }

            result.push(official || {});
        }

        return result;
    }

    async getOfficialsList($event_id, $cert) { 
        const sqlParams = [$event_id];
        let sqlQuery = this.OFFICIALS_LIST_SQL;

        if($cert) {
            sqlQuery += ' and o.rank = $2';

            sqlParams.push($cert);
        }

        return Db.query(sqlQuery, sqlParams);
    }

    async getEventInfo($event_id) {
        return Db.query(
            this.EVENT_INFO_SQL, [$event_id]
        ).then(result => _.first(result.rows) || {});
    }

    async getCourts($eventId, $courts, $day) {
        const noCourts = _.isEmpty($courts);
        const courtsSql = 
            `SELECT mt.court_id, c.name court_name, c.champdesk, (
                SELECT COALESCE(ARRAY_TO_JSON(ARRAY_AGG(ROW_TO_JSON("ctmatches"))), '[]'::JSON) 
                FROM ( 
                    SELECT  
                        m.display_name "match_name", FORMAT('%s-%s', m.division_id, m.display_name) "match_id",
                        m.wave,
                        d.division_id, d.short_name "division", c.name court_name, 
                        EXTRACT(EPOCH FROM m.secs_start)::BIGINT * 1000 "date_start", 
                        COALESCE(
                            NULLIF(m.officials_required, 0), 
                            1
                        ) "officials_required", ( 
                            SELECT COALESCE(ARRAY_TO_JSON(ARRAY_AGG(ROW_TO_JSON("o"))), '[]'::JSON) 
                            FROM ( 
                                SELECT 
                                    eos.event_official_id "id", eos.ref_num::INT, 
                                    eo.schedule_name "name", eos.published, 
                                    eog.group_name, eog.event_official_group_id "group_id"
                                FROM "event_official_schedule" eos  
                                RIGHT JOIN "event_official" eo
                                    ON eo.event_official_id = eos.event_official_id 
                                    AND eo."deleted" IS NULL 
                                    AND eo."work_status" = 'approved'
                                LEFT JOIN "event_official_group" eog 
                                    ON eog.event_official_group_id = eos.event_official_group_id
                                LEFT JOIN "official" o 
                                    ON o.official_id = eo.official_id 
                                LEFT JOIN "user" u 
                                    ON u.user_id = o.user_id 
                                WHERE eos.match_name = m.display_name 
                                    AND eos.division_id = m.division_id 
                                ORDER BY eos.ref_num 
                            ) "o" 
                        ) "officials_list", 
                        m.officials_assigned,
                        (ROW_NUMBER() OVER(ORDER BY m.secs_start) - 1) "original_match_index"
                    FROM matches m 
                    LEFT JOIN division d  
                        ON d.division_id = m.division_id 
                    WHERE m.event_id = mt.event_id   
                        AND m.court_id = mt.court_id  
                        AND TO_CHAR(m.secs_start, 'YYYY-MM-DD') = $2  
                    ORDER BY m.secs_start   
                ) "ctmatches"  
             ) "matches",
             (ROW_NUMBER() OVER(ORDER BY c.sort_priority) - 1)::INTEGER "original_court_index"
            FROM matches mt   
            LEFT JOIN courts c  
                ON mt.court_id = c.uuid
            LEFT JOIN "event" e 
                ON e.event_id = mt.event_id 
            WHERE mt.event_id = $1  
                ${noCourts?'':`AND c.uuid IN ('${$courts.join(`', '`)}')`}
                AND TO_CHAR(mt.secs_start, 'YYYY-MM-DD') = $2 
            GROUP BY e.event_id, mt.event_id, mt.court_id, c.name, c.sort_priority, c.champdesk
            ORDER BY c.sort_priority
            ${noCourts?'LIMIT 10':''}`;

        const results = await Promise.all([
            Db.query(courtsSql, [$eventId, $day]).then(result => result.rows || []),
            (noCourts) 
                ? Db.query(this.COURTS_2_SQL, [$eventId, $day]).then(result => result.rows || [])
                : Promise.resolve([])
        ]);
        const courts = _.first(results);

        return {
            courts: (courts.length) ? this.__formatMatches(courts) : [],
            courts_list: results[1]
        };
    }

    async assignOfficial($eventId, $matchesObj) {
        let tr;

        try {
            tr = await Db.begin();
            const processedMatches = await this.manageAssignment($matchesObj, $eventId, tr);

            await this.updateMatchesOfficialsQty(processedMatches, $eventId, tr);
            
            return tr.commit();
        } catch (err) {

            if (tr && !tr.isCommited) {
               await tr.rollback();
            }

            throw err;
        }
    }

    async applyPattern($event_id, $reqBodyValidated) {
        let tr;

        try {
            tr = await Db.begin({skipErrAboutCommittedTr: true});
            const result = await tr.query(
                this.APPLY_PATTERN_SQL,
                [$reqBodyValidated.group_name, $reqBodyValidated.pattern, $event_id]
            );
            const group = _.first(result.rows);

            if (_.isEmpty(group)) {
                throw { validation: 'Error while creating a group' };
            }

            const groupId = group && group.event_official_group_id;
            const officialIdentifiers = Object.keys($reqBodyValidated.assignments);
            const updatedOfficialsList = await Promise.all(
                officialIdentifiers.map((officialId) => {

                    return this.__applyOfficialPattern(
                        tr, 
                        Number(officialId), 
                        $reqBodyValidated.assignments[officialId], 
                        $event_id, 
                        groupId, 
                        $reqBodyValidated.event_day, 
                        $reqBodyValidated.hours_from, 
                        $reqBodyValidated.hours_to
                    )
                }, this)
            );

            loggers.debug_log.verbose('Officials group, inserted', updatedOfficialsList.length, 'rows');
            await tr.commit();

            return groupId;
        } catch (err) {

            if (err.validation && tr && !tr.isCommited) {
                tr.rollback()
            }

            throw err;
        }
    }

    async publishSchedule($event_id, $publish, $day, hour_from, hour_to) {
        return Db.query(
            this.PUBLISH_OFFICIAL_ASSIGNMENTS_SQL, 
            [$event_id, $publish, $day, hour_from, hour_to]
        );
    }

    async assignmentsList($eventId, $eventDate) {
        return Db.query(this.ASSIGNMENTS_LIST_SQL, [$eventId, $eventDate]);
    }

    async scheduleExport($eventId, includeMatchCodes) {
        const dbConn = Buffer.from(JSON.stringify(SAILS_CONNECTION)).toString('base64');
        
        return new Promise((resolve, reject) => {
            loggers.debug_log.verbose('Spawn child process');

            let _outputFilePath;
            let _procError;
            let _killTimer;
            const exportProc = spawn(
                'node', 
                [
                    'officials-schedule-export.js',
                    `--event=${$eventId}`,
                    `--connection=${dbConn}`,
                    '--filename="Schedule_Export"',
                    `--includeMatchCodes=${includeMatchCodes}`,
                ], 
                {
                    detached: true,                  
                    cwd: path.resolve(__dirname, '..', '..', '..', '..', '..'),
                    stdio: 'pipe'
                }
            ).on('error', (err) => { 

                if(_killTimer) {
                    clearTimeout(_killTimer);
                }     

                return reject(err);
            }).on('close', (code) => { 
                loggers.debug_log.verbose('Process exited with code', code);

                if(_killTimer) {
                    clearTimeout(_killTimer);
                } 

                if(code === 0) {
                    resolve(_outputFilePath);
                } else {
                    reject(_procError);
                }               
            });

            exportProc.unref();

            exportProc.stdout.on('data', (message) => {
                _outputFilePath = message.toString();
            });

            exportProc.stderr.on('data', (error) => {
                _procError += error.toString();
            });

            _killTimer = setTimeout(() => {
                exportProc.kill();
            }, 5 * 60 * 1000);
        });
    }

    async courtsMatchesReversed() {
        const [ hours, courts, divisions ] = await Promise.all([
            this.__findHours(),
            this.__findCourts(),
            this.__findDivisions()
        ]);

        return { hours, courts, divisions };
    }

    async removeOfficial($eventId, divisionId, matchName, $eventOfficialId) {
        return Db.query(
            this.REMOVE_OFFICIAL_SQL,
            [$eventId, divisionId, matchName, $eventOfficialId]
        );
    }

    async courtsMatches($event_id, $day, $hour, $hoursCount) {
        const [ hours, divisions, courts ] = await Promise.all([
            this._getCourtsMatchesHours($event_id, $day, $hour),
            this._getCourtsMatchesDivisions($event_id, $day),
            this._getCourtsMatchesCourts($event_id, $day, $hour, $hoursCount)
        ]);

        return { hours, divisions, courts };
    }
}

module.exports = new ScheduleService(Db);
