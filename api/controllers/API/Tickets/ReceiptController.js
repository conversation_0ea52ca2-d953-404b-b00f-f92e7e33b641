'use strict';

let renderReceipt = function (isAppleDevice, res, data) {
    if(data) {
        data.isAppleDevice = isAppleDevice;
    }

    const template = SWTReceiptService.getReceiptTemplate(data);

    res.renderMin(template, data);
}

let errorHandler = function (res, err) {
    if(err.validation) {
        loggers.debug_log.debug(err);
        return res.render('204', {
            error: err.validation,
            title: 'Invalid Ticket URL'
        })
    } else {
        loggers.errors_log.error(err);
        res.serverError();
    }
}

let isAppleDevice = function (req) {
    return /iPad|iPhone|iPod|Macintosh/gm.test(req.get('User-Agent'));
}

module.exports = {
    // GET /tickets/receipt/:code
    viewReceipt: function (req, res) {
        let $code = req.params.code;

        if(!$code) {
            return res.render('500', { error: 'No barcode passed' });
        }

        let ticket_barcode;

        try {
            ticket_barcode = SWTReceiptService.convertHashToBarcode($code);
        } catch(e) {
            return res.validation(e.message)
        }

        SWTReceiptService.retrieveData(ticket_barcode, $code)
        .then(renderReceipt.bind(null, isAppleDevice(req), res))
        .catch(errorHandler.bind(null, res))
    },
    // GET /event/:event/ticket/:barcode/receipt
    viewBarcodeReceipt: function (req, res) {
        let $barcode = +req.params.barcode;

        if(!$barcode) return res.render('500', { error: 'No barcode passed' });

        SWTReceiptService.retrieveData($barcode)
        .then(renderReceipt.bind(null, isAppleDevice(req), res))
        .catch(errorHandler.bind(null, res))
    },
    // GET /r/:barcode
    shortLinkReceipt: function (req, res) {
        let $barcode = req.params.barcode;

        if(!$barcode) return res.render('500', { error: 'No barcode passed' });

        SWTReceiptService.shortHashData($barcode)
        .then(renderReceipt.bind(null, isAppleDevice(req), res))
        .catch(errorHandler.bind(null, res))
    },
    //GET /pt/:hash
    viewPurchaseTickets: async function (req, res) {
        const hash = req.params.hash;

        if(!hash) return res.render('500', { error: 'No barcode passed' });

        try {
            const receiptData = await SWTReceiptService.getPurchaseTicketsData(hash);

            if(_.isEmpty(receiptData)) {
                return res.render('404', { error: 'Purchase not found' });
            }

            res.render('tickets/assigned/tickets-list', receiptData);

        } catch (err) {
            res.customRespError(err);
        }
    }
};

