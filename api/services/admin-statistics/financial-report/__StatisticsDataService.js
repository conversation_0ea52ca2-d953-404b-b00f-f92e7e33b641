'use strict';

const { FEE_PAYER } = require('../../../constants/payments');

class StatisticsDataService {

    get STATISTIC_MODE () {
        return {
            EXPORT: 'export',
            DEFAULT: 'default'
        }
    }

    get EXPORT_FIELDS_LABELS () {
        return {
            EVENT_ID: 'Event ID',
            DATE_PAID: 'Date Paid',
            PURCHASE_ID: 'Purchase ID',
            EVENT_NAME: 'Event Name',
            CHARGE_ID: 'Charge ID',
            PAYMENT_FOR: 'Payment For',
            SW_FEE: 'SW Fee',
            AMOUNT_REFUNDED: 'Amount Refunded',
            APPLICATION_FEE_REFUNDED: 'Application Fee Refunded',
            STRIPE_PERCENT: 'Stripe Percent',
            ADDITIONAL_FEE: 'Additional Fee',
            APPLICATION_FEE: 'Application Fee',
            CHARGE_AMOUNT: 'Charge Amount'
        }
    }

    getFields (params) {
        return {
            EVENT_ID            : {
                value: 'e.event_id',
                alias: {
                    default: 'event_id',
                    export : this.EXPORT_FIELDS_LABELS.EVENT_ID
                }
            },
            DATE_PAID           : {
                value: knex.raw(`TO_CHAR(p.date_paid, 'DD/MM/YYYY HH12:MI AM')`),
                alias: {
                    export: this.EXPORT_FIELDS_LABELS.DATE_PAID
                }
            },
            DATE_CREATED        : {
                value: knex.raw(`TO_CHAR(p.created, 'YYYY-MM-DD')`),
                alias: {
                    default: 'created',
                    export: 'created'
                }
            },
            PURCHASE_ID         : {
                value: 'p.purchase_id',
                alias: {
                    default: 'purchase_id',
                    export : this.EXPORT_FIELDS_LABELS.PURCHASE_ID
                }
            },
            DATE_START          : {
                value: knex.raw(`TO_CHAR(e.date_start, 'Mon DD, YYYY')`),
                alias: {
                    default: 'date_start',
                }
            },
            DATE_START_RAW      : {
                value: 'e.date_start',
                alias: {
                    default: 'date_start_raw',
                }
            },
            LONG_NAME           : {
                value: 'e.long_name',
                alias: {
                    default: 'long_name',
                    export : this.EXPORT_FIELDS_LABELS.EVENT_NAME
                }
            },
            STRIPE_CHARGE_ID    : {
                value: 'p.stripe_charge_id',
                alias: {
                    default: 'stripe_charge_id',
                    export : this.EXPORT_FIELDS_LABELS.CHARGE_ID
                }
            },
            PAYMENT_FOR         : {
                value: 'p.payment_for',
                alias: {
                    default: 'payment_for',
                    export : this.EXPORT_FIELDS_LABELS.PAYMENT_FOR
                }
            },
            STRIPE_TICKETS_FEE_PAYER: {
                value: knex.raw(`COALESCE(e.stripe_tickets_fee_payer, ?)`, [FEE_PAYER.BUYER]),
                alias: {
                    default: 'stripe_tickets_fee_payer',
                    export: 'stripe_tickets_fee_payer'
                }
            },
            SW_FEE              : {
                value: knex.raw(`(p.collected_sw_fee + p.additional_fee_amount)::NUMERIC`),
                alias: {
                    default: 'sw_fee',
                    export : this.EXPORT_FIELDS_LABELS.SW_FEE
                }
            },
            TICKETS_SW_FEE      : {
                value: knex.raw(`e.tickets_sw_fee ::NUMERIC`),
                alias: {
                    default: 'tickets_sw_fee'
                }
            },
            REFUNDS_TOTAL       : {
                value: knex.raw(`COALESCE(SUM(ref.amount::NUMERIC / 100), 0)`),
                alias: {
                    default: 'refunds_total',
                    export : this.EXPORT_FIELDS_LABELS.AMOUNT_REFUNDED
                }
            },
            APP_FEE_REFUND_TOTAL: {
                value: knex.raw(`COALESCE(SUM(app_fee_ref.amount::NUMERIC / 100), 0)`),
                alias: {
                    default: 'app_fee_refunds_total',
                    export : this.EXPORT_FIELDS_LABELS.APPLICATION_FEE_REFUNDED
                }
            },
            STRIPE_PERCENT      : {
                value: knex.raw(`
                    ROUND(((CASE
                           WHEN p.stripe_percent IS NOT NULL THEN p.stripe_percent
                           WHEN p.payment_for = 'teams' THEN e.stripe_teams_percent
                           WHEN p.payment_for = 'tickets' THEN e.stripe_tickets_percent
                           WHEN p.payment_for = 'booths' 
                            THEN COALESCE(e.stripe_exhibitors_percent, e.stripe_teams_percent)
                        END)::NUMERIC / 100), 3)
                `),
                alias: {
                    default: 'stripe_percent',
                    export : this.EXPORT_FIELDS_LABELS.STRIPE_PERCENT
                }
            },
            INIT_CHARGE_AMOUNT  : {
                value: knex.raw(`MAX(COALESCE(ch_ref.amount, 0)::NUMERIC / 100)`),
                alias: {
                    default: 'initial_charge_amount',
                    export: 'initial_charge_amount'
                }
            },
            ADDITIONAL_FEE      : {
                value: 'p.additional_fee_amount',
                alias: {
                    export: this.EXPORT_FIELDS_LABELS.ADDITIONAL_FEE
                }
            },
            APP_FEE             : {
                value: knex.raw('SUM(ROUND(COALESCE(app_fee.amount, 0)::NUMERIC / 100, 2))'),
                alias: {
                    default: 'app_fee_bt',
                    export : this.EXPORT_FIELDS_LABELS.APPLICATION_FEE
                }
            },
            CHARGE_AMOUNT       : {
                value: knex.raw(`ROUND(SUM(COALESCE(ch.amount, 0)::NUMERIC / 100), 4)`),
                alias: {
                    default: 'charge_amount',
                    export : this.EXPORT_FIELDS_LABELS.CHARGE_AMOUNT
                }
            },
            TEAMS_COUNT         : {
                value: knex.raw(`
                    (SELECT COALESCE((array_agg(row_to_json(t)))[1], '{"paid":0,"canceled":0}'::JSON)
                    FROM (
                             SELECT COALESCE(COUNT(pt.*)
                                             FILTER ( WHERE
                                                 p.date_paid >= :after AND p.date_paid <= :before ),
                                             0) :: INTEGER "paid",
                                    COALESCE(COUNT(pt.*)
                                             FILTER ( WHERE
                                                 (pt.canceled >= :after AND pt.canceled <= :before) OR 
                                                 (  
                                                    p.dispute_created >= :after AND 
                                                    p.dispute_created <= :before AND 
                                                    p.dispute_status = 'lost'
                                                 ) OR 
                                                 (p.canceled_date >= :after AND p.canceled_date <= :before)  
                                             ), 0) :: INTEGER "canceled"
                             FROM purchase_team pt
                             WHERE pt.purchase_id = p.purchase_id
                               AND p.payment_for = 'teams'
                             GROUP BY p.stripe_charge_id
                        ) t
                    )
                `, { after: params.dateStart, before: params.dateEnd }),
                alias: {
                    default: 'teams_count',
                }
            },
            TICKET_RATIOS: {
                value: knex.raw(`(
                    SELECT (array_to_json(array_agg(row_to_json(p))))
                        FROM (SELECT * FROM ticket_purchase_number_in_season pn WHERE pn.payment_id = p.purchase_id) p
                )`),
                alias: {
                    default: 'ratios'
                },
            },
            TICKETS_COUNT       : {
                value: knex.raw(`
                    (SELECT COALESCE((array_agg(row_to_json(t)))[1], '{"paid":0,"canceled":0}'::JSON)
                        FROM (
                               SELECT 
                                    COALESCE(COUNT(ticket.*)
                                         FILTER (
                                             WHERE (p.date_paid >= :after AND p.date_paid <= :before)
                                             ), 0) :: INTEGER "paid",
                                    COALESCE(COUNT(ticket.*)
                                         FILTER (
                                             WHERE (
                                                 (  
                                                    p.dispute_created >= :after AND 
                                                    p.dispute_created <= :before AND 
                                                    p.dispute_status = 'lost'
                                                 ) OR 
                                                 (ticket.date_refunded >= :after AND ticket.date_refunded <= :before) OR
                                                 (p.canceled_date >= :after AND p.canceled_date <= :before)
                                             )
                                         ), 0) :: INTEGER "canceled"                                       
                             FROM purchase ticket
                             WHERE ticket.is_ticket IS TRUE
                               AND ticket.is_payment IS FALSE
                               AND ticket.linked_purchase_id = p.purchase_id
                               AND p.payment_for = 'tickets'
                               AND (e."tickets_settings" ->> 'require_recipient_name_for_each_ticket')::BOOLEAN IS TRUE
                             GROUP BY p.stripe_charge_id
                        ) t
                    )
                `, { after: params.dateStart, before: params.dateEnd }),
                alias: {
                    default: 'tickets_count',
                }
            },
            BOOTHS_COUNT       : {
                value: knex.raw(`
                    (SELECT COALESCE((array_agg(row_to_json(t)))[1], '{"paid":0,"canceled":0}'::JSON)
                    FROM (
                             SELECT COALESCE(SUM(pb.quantity)
                                     FILTER (
                                         WHERE (p.date_paid >= :after AND p.date_paid <= :before)
                                         ), 0) :: INTEGER "paid",
                                 COALESCE(SUM(pb.quantity)
                                     FILTER (
                                         WHERE (
                                             (p.canceled_date >= :after AND p.canceled_date <= :before) OR 
                                             (p.date_refunded >= :after AND p.date_refunded <= :before) OR                                            
                                             (
                                                 p.dispute_created >= :after AND
                                                 p.dispute_created <= :before AND
                                                 p.dispute_status = 'lost'
                                             )
                                         )
                                         ), 0) :: INTEGER "canceled"
                             FROM purchase_booth pb
                             WHERE p.is_ticket IS FALSE
                               AND pb.purchase_id = p.purchase_id
                               AND p.payment_for = 'booths'
                             GROUP BY p.stripe_charge_id
                         ) t
                   )
                `, { after: params.dateStart, before: params.dateEnd }),
                alias: {
                    default: 'booths_count',
                }
            }
        }
    }

    /**
     *
     * @param {String} dateStart min date balance transaction created in format YYYY-MM-DD
     * @param {String} dateEnd max date balance transaction created in format YYYY-MM-DD
     * @param {Array} paymentFor allowed values - teams, tickets, booths
     * @param {Number} eventID
     * @param {string} mode allowed values = export
     * @returns {Promise<array>}
     */
    getData (dateStart, dateEnd, paymentFor, eventID = null, mode= this.STATISTIC_MODE.DEFAULT) {

        if(![this.STATISTIC_MODE.DEFAULT, this.STATISTIC_MODE.EXPORT].includes(mode)) {
            return Promise.reject({ validation: 'Statistics mode invalid' });
        }

        let query = this.getStatsQuery(dateStart, dateEnd, paymentFor, eventID, mode);

        return Db.query(query).then(result => result.rows);
    }

    getStatsQuery (dateStart, dateEnd, paymentFor, eventID, mode) {
        let select = {};

        let FIELDS = this.getFields({ dateEnd, dateStart });

        Object.keys(FIELDS).forEach(field => {
            if(FIELDS[field].alias[mode]) {
                select[FIELDS[field].alias[mode]] = FIELDS[field].value;
            }
        })

        let query = knex('stripe.balance_transaction AS bt')
            .select(select)
            .leftJoin('stripe.application_fee AS app_fee', function () {
                this.on('bt.id', '=', 'app_fee.balance_transaction_id')
                    .on('bt.type', '=', knex.raw('?', ['application_fee']))
            })
            .leftJoin('stripe.refund AS ref', function () {
                this.on('bt.id', '=', 'ref.balance_transaction_id')
                    .on('bt.type', '=', knex.raw('?', ['refund']))
            })
            .leftJoin('stripe.application_fee_refund AS app_fee_ref', function () {
                this.on('bt.id', '=', 'app_fee_ref.balance_transaction_id')
                    .on('bt.type', '=', knex.raw('?', ['application_fee_refund']))
            })
            .leftJoin('stripe.charge AS ch', function () {
                this.on('bt.id', '=', 'ch.balance_transaction_id')
                    .on('bt.type', '=', knex.raw('?', ['charge']))
            })
            .leftJoin('stripe.application_fee AS app_fee_for_refund', function () {
                this.on('app_fee_for_refund.id', '=', 'app_fee_ref.fee_id')
                    .on('bt.type', '=', knex.raw('?', ['application_fee_refund']))
            })
            .join('public.purchase AS p', function () {
                this.on('p.stripe_charge_id', '=', knex.raw(`
                    COALESCE(
                        app_fee.originating_transaction_id, 
                        ch.id, 
                        app_fee_for_refund.originating_transaction_id,
                        ref.charge_id
                     )`))
                    .onIn('p.payment_for', paymentFor)
                    .onNotNull('p.stripe_charge_id')
                    .onNotNull('p.payment_for')
                    .on(knex.raw('p.type = ?', ['card']))
            })
            .leftJoin('stripe.charge AS ch_ref', function () {
                this.on('p.stripe_charge_id', '=', 'ch_ref.id')
                    .on('bt.type', '=', knex.raw('?', ['application_fee_refund']))
            })
            .join('public.event AS e', 'e.event_id', 'p.event_id')
            .whereRaw(`to_timestamp(bt.created) >= ?`, [dateStart])
            .whereRaw(`to_timestamp(bt.created) <= ?`, [dateEnd])
            .groupBy('e.event_id', 'p.purchase_id');

        if(eventID) {
            query.where('e.event_id', eventID);
        }

        return query;
    }
}

module.exports = new StatisticsDataService();
