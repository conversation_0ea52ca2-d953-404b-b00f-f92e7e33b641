'use strict';

const swUtils = require('../../../lib/swUtils');
const MinStripePercentService = require('./__MinStripePercentService');

const FIXED = 0.3;

class TeamsService {
    constructor () {}

    async getDeltas (payment) {
        this.STRIPE_PERCENT_MIN = MinStripePercentService.getMinStripePercent(payment);

        let refundDeltas = this.getRefundDeltas(payment);
        let incomeDeltas = this.getIncomeDeltas(payment);

        return Object.assign({}, refundDeltas, incomeDeltas);
    }

    getRefundDeltas (payment) {
        let refundAmount            = Number(payment.refunds_total);
        let initialChargeAmount     = Number(payment.initial_charge_amount);
        let refundedAppFeeAmount    = Number(payment.app_fee_refunds_total);
        let refundCount             = this.__getCounts('canceled', payment);

        let { swFeeDelta: swFeeRefundDelta, stripeDelta: stripeRefundDelta } = this.__getDeltas(
            refundAmount, payment.stripe_percent, refundedAppFeeAmount, initialChargeAmount
        );

        return { swFeeRefundDelta, stripeRefundDelta, refundCount };
    }

    getIncomeDeltas (payment) {
        let chargeAmount = Number(payment.charge_amount);
        let appFeeAmount = Number(payment.app_fee_bt);
        let incomeCount  = this.__getCounts('paid', payment);

        let { swFeeDelta, stripeDelta } = this.__getDeltas(chargeAmount, payment.stripe_percent, appFeeAmount);

        return { swFeeDelta, stripeDelta, incomeCount };
    }

    __getCounts (countsType, payment) {
        let purchaseTypeLabel = payment.payment_for === 'booths'
            ? 'booths_count'
            : 'teams_count';

        return Number(payment[purchaseTypeLabel][countsType]);
    }

    __getDeltas (amount, stripePercent, appFee, initialChargeAmount = 0) {
        let swFeeDelta  = 0;

        let { stripeFeeSw, stripeDelta } = this.__getStripeFeeDelta(amount, stripePercent, initialChargeAmount);

        if(appFee > 0 && appFee !== stripeFeeSw) {
            swFeeDelta += swUtils.normalizeNumber(appFee - stripeFeeSw);
        }

        return { swFeeDelta, stripeDelta };
    }

    __getStripeFeeDelta (amount, stripePercent, initialChargeAmount) {
        let stripeFeeSw  = this.__getStripeFee(amount, stripePercent, initialChargeAmount);

        let stripeFeeMin = 0;
        let stripeDelta = 0;

        if(stripePercent > this.STRIPE_PERCENT_MIN) {
            stripeFeeMin = this.__getStripeFee(amount, this.STRIPE_PERCENT_MIN, initialChargeAmount);

            stripeDelta += swUtils.normalizeNumber(stripeFeeSw - stripeFeeMin);
        }

        return { stripeFeeSw, stripeDelta };
    }

    __getStripeFee (amount, stripePercent, initialChargeAmount) {
        if(initialChargeAmount && initialChargeAmount !== amount) {
            return this.__getPartialRefundStripeFee(amount, initialChargeAmount, stripePercent);
        }

        return StripeService.defaultStripeFee(amount, stripePercent, FIXED);
    }

    __getPartialRefundStripeFee (refundAmount, initialChargeAmount, stripePercent) {
        let stripeFeeInitial    = StripeService.defaultStripeFee(initialChargeAmount, stripePercent, FIXED);
        let newAmount           = swUtils.normalizeNumber(initialChargeAmount - refundAmount);
        let stripeFeeNew        = StripeService.defaultStripeFee(newAmount, stripePercent, FIXED);

        return swUtils.normalizeNumber(stripeFeeInitial - stripeFeeNew);
    }

    async getOtherData (event, after, before) {
        let counts = await this.__getTeamsCounts(event, after, before);
        let disputesFee = await this.__getDisputeFee(event, after, before);

        if(!_.isEmpty(counts) && disputesFee > 0) {
            counts.expected_sw_fee
                = swUtils.normalizeNumber(Number(counts.expected_sw_fee) + disputesFee);
        }

        return counts;
    }

    async __getTeamsCounts (event, after, before) {
        let query = `WITH teams_paid_by_check AS (
            SELECT pt.roster_team_id
            FROM purchase_team pt
                     JOIN purchase p on pt.purchase_id = p.purchase_id
            WHERE pt.event_id = :event
              AND COALESCE(p.date_paid, p.received_date) BETWEEN :after AND :before
              AND p.type = 'check'
              AND (pt.canceled NOT BETWEEN :after AND :before OR pt.canceled IS NULL)
        ),
             teams_paid_by_card AS (
                 SELECT pt.roster_team_id
                 FROM purchase_team pt
                          JOIN purchase p on pt.purchase_id = p.purchase_id
                 WHERE pt.event_id = :event
                   AND p.date_paid BETWEEN :after AND :before
                   AND p.type = 'card'
                   AND (p.dispute_status NOT IN ('pending', 'lost') OR p.dispute_status IS NULL)
                   AND (pt.canceled NOT BETWEEN :after AND :before OR pt.canceled IS NULL)
             ),
             already_paid_teams AS (
                 SELECT pt.roster_team_id,
                        COALESCE(
                                    COALESCE(MAX(pt.created)
                                             FILTER ( WHERE pt.canceled IS NOT NULL OR pt.canceled <= :before),
                                             e.created) <
                                    COALESCE(MAX(pt.created) FILTER ( WHERE pt.canceled IS NULL), e.created),
                                    FALSE) "paid"
                 FROM purchase_team pt
                          JOIN purchase p
                               ON pt.purchase_id = p.purchase_id 
                               AND COALESCE(p.date_paid, p.received_date) <= :before
                               AND (p.dispute_status NOT IN ('pending', 'lost') OR p.dispute_status IS NULL)                            
                          JOIN event e on pt.event_id = e.event_id
                 WHERE pt.event_id = :event         
                 GROUP BY pt.roster_team_id, e.created
             ),
             accepted AS (
                 SELECT ec.roster_team_id,
                        (COALESCE(MAX(ec.created) FILTER ( WHERE ec.action = 'team.entry.accepted' ), :after) >
                         COALESCE(MAX(ec.created)
                                  FILTER ( WHERE ec.action <> 'team.entry.accepted' AND ec.action IS NOT NULL ),
                                  :after)) "accepted"
                 FROM event_change ec
                 WHERE ec.event_id = :event
                   AND ec.action ILIKE 'team.entry.%'
                   AND ec.created BETWEEN :after AND :before
                 GROUP BY ec.roster_team_id
             ),
             accepted_not_paid AS (
                 SELECT *
                 FROM (
                          SELECT a.roster_team_id
                          FROM accepted a
                          WHERE a.accepted IS TRUE
                      ) data
                 WHERE data.roster_team_id NOT IN (
                                                    SELECT apt.roster_team_id 
                                                    FROM already_paid_teams apt 
                                                    WHERE apt.paid IS TRUE
                                                  )
             )
        SELECT COUNT(1) FILTER ( WHERE tpbch.roster_team_id IS NOT NULL ) teams_paid_by_check,
               COUNT(1) FILTER ( WHERE tpbc.roster_team_id IS NOT NULL )  teams_paid_by_card,
               COUNT(1) FILTER ( WHERE anp.roster_team_id IS NOT NULL )   accepted_not_paid,
               e.teams_entry_sw_fee                                       sw_fee
        FROM roster_team rt
                 LEFT JOIN teams_paid_by_check tpbch ON tpbch.roster_team_id = rt.roster_team_id
                 LEFT JOIN teams_paid_by_card tpbc ON tpbc.roster_team_id = rt.roster_team_id
                 LEFT JOIN accepted_not_paid anp ON anp.roster_team_id = rt.roster_team_id
                 JOIN event e ON e.event_id = rt.event_id
        WHERE rt.event_id = :event         
        GROUP BY e.teams_entry_sw_fee`;

        let data = await Db.query(
            knex.raw(query, { event, after, before }).toString()
        ).then(result => result.rows && result.rows[0] || null)

        if(!data) {
            return {};
        }

        let totalTeamsCountInPurchases = swUtils.normalizeNumber(
            Number(data.teams_paid_by_check) +
            Number(data.teams_paid_by_card) +
            Number(data.accepted_not_paid)
        )

        let expected_sw_fee = swUtils.normalizeNumber(Number(totalTeamsCountInPurchases) * Number(data.sw_fee));

        return _.extend(data, { totalTeamsCountInPurchases, expected_sw_fee });
    }

    async __getDisputeFee (event, after, before) {
        let query = `SELECT COALESCE(COUNT(1), 0) disputes_count
                    FROM purchase p
                    WHERE p.event_id = :event
                      AND p.dispute_status = 'lost'
                      AND p.dispute_created BETWEEN :after AND :before
                      AND p.payment_for = 'teams'
                      AND p.type = 'card'`

        let { disputes_count } = await Db.query(
            knex.raw(query, { event, after, before }).toString()
        ).then(result => result && result.rows[0] || {});

        if(!disputes_count) {
            return 0;
        }

        let disputesPenalty = swUtils.normalizeNumber(disputes_count * StripeService.DISPUTE_FEE);

        return Number(disputesPenalty);
    }
}

module.exports = new TeamsService();
