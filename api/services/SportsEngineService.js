const fetch = require('node-fetch');

const toBase64 = (input) => Buffer.from(input).toString('base64');

const FiltersValidationService = require('./sport-engine/_FiltersValidationService');
const UnresolvedMembershipsService = require("./sport-engine/UnresolvedMembershipsService");

class SportsEngineService {
    constructor() {
        this._lastToken = null;
    }

    get OAUTH_URL() {
        return sails.config.sportsEngine.oauth_url;
    }

    get API_BASE_URL() {
        return sails.config.sportsEngine.api_base_url;
    }

    get CREDENTIAL_DEFINITION_ID() {
        return sails.config.sportsEngine.credential_definition_id;
    }

    get ORG_ID() {
        return sails.config.sportsEngine.org_id;
    }

    get CLIENT_ID() {
        return sails.config.sportsEngine.client_id;
    }

    get CLIENT_SECRET() {
        return sails.config.sportsEngine.client_secret;
    }

    get RESULTS_PER_PAGE() {
        return 100;
    }

    get unresolvedMemberships () {
        return UnresolvedMembershipsService;
    }

    async* getEligibility(filters) {
        this._validateFilters(filters);

        yield* this._getResult(`/eligibility/${this.CREDENTIAL_DEFINITION_ID}/${this.ORG_ID}`, filters);
    }

    _prepareTokenRequestOptions() {
        const headers = new fetch.Headers();
        headers.set('Authorization', `Basic ${toBase64(`${this.CLIENT_ID}:${this.CLIENT_SECRET}`)}`);
        headers.set('Accept', 'application/json');
        headers.set('Content-Type', 'application/x-www-form-urlencoded');

        return {
            headers,
            method: 'POST',
            body: 'grant_type=client_credentials',
        };
    }

    async _getToken() {
        let lastToken = await this._lastToken;
        if(!lastToken || lastToken.expires_at <= Date.now() / 1000) {
            this._lastToken = (async () => {
                const response = await fetch(this.OAUTH_URL, this._prepareTokenRequestOptions());
                const body = await response.json();
                if(!response.ok) {
                    loggers.errors_log.error(body);
                    throw Error(body.error_description || 'Error obtaining se token');
                }
                return body;
            })();
            lastToken = await this._lastToken;
        }
        return lastToken.access_token;
    }

    async _prepareDataRequestOptions() {
        const headers = new fetch.Headers();
        headers.set('Authorization', `Bearer ${await this._getToken()}`);
        headers.set('Accept', 'application/json');

        return { headers };
    }

    async* _getResult(path, params) {
        let page = 1;
        let url = this._generateURL(path, params, page);

        while(url) {
            const response = await fetch(url, await this._prepareDataRequestOptions());
            const body = await response.json();
            if(!response.ok) {
                if(this._isValidationError(response)) {
                    throw {
                        validation: body.error.messages.join('\n'),
                    };
                }
                loggers.errors_log.error(body);
                throw Error(
                    (body.error && body.error.messages || ['Error sending se api request']).join('\n')
                );
            }
            yield* body.result;

            // SportEngine response next_page_href doesn't contain all initial query params so we need to
            // generate url for each page
            if(body.metadata?.pagination?.next_page_href) {
                url = this._generateURL(path, params, ++page);
            } else {
                url = null;
            }
        }
    }

    _generateURL(path, params, page) {
        const queryParams = new URLSearchParams({
            per_page: this.RESULTS_PER_PAGE,
            page,
            ...params,
        });
        return `${this.API_BASE_URL}${path}?${queryParams}`;
    }

    _isValidationError(response) {
        const VALIDATION_ERROR_STATUS_CODES = [406, 422];
        return VALIDATION_ERROR_STATUS_CODES.includes(response.status);
    }

    _validateFilters (filters) {
        FiltersValidationService.validate(filters);
    }
}

module.exports = new SportsEngineService();
