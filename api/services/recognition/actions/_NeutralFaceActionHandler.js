'use strict';

const RekognitionService = require('../_RekognitionService');
const AbstractActionHandler = require('./_AbstractActionHandler');

class NeutralFaceActionHandler extends AbstractActionHandler {
    getAction() {
        return RekognitionService.VERIFICATION_ACTIONS.NEUTRAL_FACE;
    }

    __validateAction() {
        super.__validateAction();

        if (
            this.recognitionVerification.last_action !==
            RekognitionService.VERIFICATION_ACTIONS.ID
        ) {
            throw {
                validation:
                    'Neutral face verification can run only after ID verification',
            };
        }
    }
}

module.exports = NeutralFaceActionHandler;
