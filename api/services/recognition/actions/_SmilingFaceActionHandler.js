'use strict';

const RekognitionService = require('../_RekognitionService');
const AbstractActionHandler = require('./_AbstractActionHandler');

class SmilingFaceActionHandler extends AbstractActionHandler {
    getAction() {
        return RekognitionService.VERIFICATION_ACTIONS.SMILING_FACE;
    }

    __validateAction() {
        super.__validateAction();

        if (
            this.recognitionVerification.last_action !==
            RekognitionService.VERIFICATION_ACTIONS.NEUTRAL_FACE
        ) {
            throw {
                validation:
                    'Smile face verification can run only after neutral face verification',
            };
        }
    }

    __getUpdateData() {
        const data = super.__getUpdateData();

        return {
            ...data,
            is_verified: true,
        };
    }
}

module.exports = SmilingFaceActionHandler;
