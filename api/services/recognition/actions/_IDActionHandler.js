'use strict';

const RekognitionService = require('../_RekognitionService');
const AbstractActionHandler = require('./_AbstractActionHandler');

class IDActionHandler extends AbstractActionHandler {
    getAction() {
        return RekognitionService.VERIFICATION_ACTIONS.ID;
    }

    __validateRekognitionResponse(response) {
        super.__validateRekognitionResponse(response);

        const { data: IDUserInfo } = response;

        const { user } = this.recognitionVerification;

        const isSameUser =
            IDUserInfo.first_name.toLowerCase() === user.first.toLowerCase() &&
            IDUserInfo.last_name.toLowerCase() === user.last.toLowerCase();

        if (!isSameUser) {
            throw { validation: "The name on the ID and account didn't match" };
        }
    }
}

module.exports = IDActionHandler;
