'use strict';

const RekognitionService = require('../_RekognitionService');

class AbstractActionHandler {
    constructor(recognitionVerification) {
        this.recognitionVerification = recognitionVerification;
    }

    getAction() {
        throw new Error('getAction is not implemented');
    }

    async handleAction(image) {
        this.__validateAction();

        const actionData = {
            session_id: this.recognitionVerification.session_id,
            action: this.getAction(),
            image,
        };

        const response = await RekognitionService.handleAction(actionData);

        this.__validateRekognitionResponse(response);

        return this.__updateRecognitionVerification();
    }

    __validateRekognitionResponse({ error }) {
        if (error) {
            throw { validation: error };
        }
    }

    __getUpdateData() {
        return {
            last_action: this.getAction(),
        };
    }

    __updateRecognitionVerification() {
        const updateData = this.__getUpdateData();

        return {
            ...this.recognitionVerification,
            ...updateData,
        };
    }

    __validateAction() {
        if (this.recognitionVerification.is_verified) {
            throw {
                validation: 'Verification session already verified',
            };
        }
    }
}

module.exports = AbstractActionHandler;
