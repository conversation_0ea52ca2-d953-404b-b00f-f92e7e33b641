'use strict';

const ActionHandlerService = require('./_ActionHandlerService');
const RekognitionService = require('./_RekognitionService');

class RecognitionVerificationService {
    async init({ userId }) {
        const { error, data: session_id } = await RekognitionService.init();

        if (error) {
            throw { validation: error };
        }

        await this.__create({
            session_id,
            user_id: userId,
            last_action: null,
        });

        return { session_id };
    }

    async handleVerificationAction({ action, sessionId, image }) {
        const recognitionVerification = await this.__findBySessionId(sessionId);

        if (!recognitionVerification) {
            throw { validation: 'Invalid session' };
        }

        const updatedRecognitionVerification =
            await ActionHandlerService.handleAction({
                action,
                recognitionVerification,
                image,
            });

        let tr;

        try {
            tr = await Db.begin();

            await this.__update(updatedRecognitionVerification, tr);

            await tr.commit();
        } catch (err) {
            if (tr && !tr.isCommited) {
                tr.rollback();
            }

            throw err;
        }
    }

    async __findBySessionId(session_id, tr = null) {
        const _db = tr || Db;

        const query = knex('recognition_verification as rv')
            .select(
                knex.raw(
                    `rv.*, JSON_BUILD_OBJECT(
                'user_id', u.user_id,
                'first', u.first,
                'last', u.last,
                'recognition_verification_status', u.recognition_verification_status
            ) AS user`
                )
            )
            .innerJoin('user AS u', 'u.user_id', 'rv.user_id')
            .where('session_id', session_id);

        const recognitionVerification = await _db
            .query(query)
            .then(({ rows }) => rows[0] || null);

        if (!recognitionVerification) {
            throw { validation: 'Invalid session id' };
        }

        return recognitionVerification;
    }

    __create(data) {
        const query = knex('recognition_verification').insert(data);

        return Db.query(query);
    }

    async __update({ user, ...data }, tr) {
        const query = knex('recognition_verification')
            .update(data)
            .where('session_id', data.session_id);

        if (data.is_verified && user.recognition_verification_status !== 'verified') {
            await this.__setUserVerified(user.user_id, tr);
        }

        const { rowCount, rows } = await tr.query(query);

        if (rowCount === 0) {
            throw { validation: 'Invalid session provided' };
        }

        return rows[0];
    }

    async __setUserVerified(user_id, tr) {
        const query = knex('user')
            .update({ recognition_verification_status: 'verified' })
            .where('user_id', user_id);

        const { rowCount } = await tr.query(query);

        if (rowCount === 0) {
            throw new Error('User for verification not found');
        }
    }
}

module.exports = new RecognitionVerificationService();
