'use strict';

const IDActionHandler = require('./actions/_IDActionHandler');
const NeutralFaceActionHandler = require('./actions/_NeutralFaceActionHandler');
const SmilingFaceActionHandler = require('./actions/_SmilingFaceActionHandler');

class ActionHandlerService {
    get __actionHandlers() {
        return [
            IDActionHand<PERSON>,
            NeutralFaceActionHandler,
            SmilingFaceActionHandler,
        ];
    }

    handleAction({ action, recognitionVerification, image }) {
        const actionHandler = this.__getActionHandler(
            action,
            recognitionVerification
        );

        return actionHandler.handleAction(image);
    }

    __getActionHandler(action, recognitionVerification) {
        const actionHandlers = this.__actionHandlers.map(
            (ActionHandler) => new ActionHandler(recognitionVerification)
        );

        const actionHandler = actionHandlers.find(
            (handler) => handler.getAction() === action
        );

        if (!actionHandler) {
            throw { validation: 'Invalid action provided' };
        }

        return actionHandler;
    }
}

module.exports = new ActionHandlerService();
