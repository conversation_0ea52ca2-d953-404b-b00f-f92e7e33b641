
const EventsListService = require('./event/_EventsListService');
const TicketsAdditionalFieldsService = require('./event/_TicketsAdditionalService');
const EventCustomFormService = require('./event/_EventCustomFormService');
const TicketsAppUserVerificationService = require('./event/_TicketsAppUserVerificationService');
const EventDuplicationService = require('./event/_EventDuplicationService');
const EventHistoryService = require('./event/_EventHistoryService');

class EventService {

    constructor() {
        this.ticketsAppUserVerification = TicketsAppUserVerificationService;
    }

    get list () {
        return EventsListService;
    }

    get ticketsAdditionalFields () {
        return TicketsAdditionalFieldsService;
    }

    get eventCustomForm () {
        return EventCustomFormService;
    }

    get history () {
        return EventHistoryService;
    }

    get duplication () {
        return EventDuplicationService;
    }

    async getPaymentProvider(eventID) {
        const query = `
            SELECT e.tickets_payment_provider AS payment_provider
            FROM event AS e
            WHERE e.event_id = $1
            LIMIT 1
        `;

        const result = await Db.query(query, [eventID]).then(result => result.rows[0]);

        return result.payment_provider || PaymentService.__PAYMENT_PROVIDERS__.STRIPE;
    }
}

module.exports = new EventService();
