'use strict';

const crypto  = require('crypto');

class UserService {
	constructor (crypto) {
		this._crypto 			= crypto;
	}
    
	get SW_ACC () {
		return 'SportWrench <<EMAIL>>';
    }

    get ROLE () {
	    return {
	        CD      : 'club_director',
            OFFICIAL: 'official'
        }
    }

    get ADMIN_EMAILS () {
        return [ 
            '<EMAIL>', 
            '<EMAIL>'
        ];
    }

    get DEV_ACC() {
        return '"sw debug" <<EMAIL>>';
    }

    get _USER_ID_ENCRYPTION_ALG () {
        return 'aes-256-ctr';
    }

    get _ENCYPTION_SALT () {
        return 'gqhEf0I3syg330BR';
    }

    get _USER_TABLE_FIELDS () {
        return [
            'gender',
            'country',
            'created',
            'email',
            'first',
            'last',
            'phone_mob',
            'pwd_salt',
            'role_club_director',
            'role_event_owner',
            'role_spectator',
            'role_staff',
            'zip',
            'modified',
            'pwd_hash',
            'user_id',
            'activated',
            'activation_code',
            'remember_me',
            'role_sponsor',
            'role_sales_manager',
            'housing_company_id',
            'is_sw_owner',
            'has_usav_admin_role',
            'has_god_role',
            'has_admin_role',
            'recognition_verification_status',
            'deleted_at',
            'allow_login_as_cd',
        ];
    }

    isAdmin(email) {
        return !!(email && this.ADMIN_EMAILS.includes(email));
    }

	_getUser (email, userID) {
		let sql = squel.select().from('user')
                .field('user_id')
                .field('email')
                .field('pwd_hash')
                .field('pwd_salt')
                .field('event_owner_request')
                .field('role_event_owner')
                .field('role_club_director')
                .field('role_spectator')
                .field('role_staff')
                .field('role_sales_manager')
                .field('role_sponsor')
                .field('first')
                .field('last')
                .where('deleted_at IS NULL');

        if (email) {
            sql.where('LOWER(TRIM(email)) = LOWER(TRIM(?))', email);
        }

        if (userID) {
        	sql.where('user_id = ?', userID);
        }

        return Db.query(sql).then(result => (result.rows[0] || {}));
    }

    _genSalt () {
        return this._crypto.randomBytes(32).toString('hex');
    }

    _genPswdHash (password, salt) {
        return this._crypto.createHash('sha256').update(password + salt).digest('hex');
    }

    _genActivationCode () {
        return this._crypto.randomBytes(8).toString('hex');
    }

    _upsertUser (tr, user, exisitingUserID) {
        let sql = (!!exisitingUserID)
                    ?squel.update().table('user').where('user_id = ?', exisitingUserID)
                    :squel.insert().into('user');

      	sql.setFields(user)
            .returning('*');

      	return (tr || Db).query(sql)
	        .then(result => _.omit(result.rows[0], 'pwd_hash', 'pwd_salt', 'created', 'modified'))
	        .then(modifiedUser => {
	            if (_.isEmpty(modifiedUser)) {
	                tr.rollback();
	                return Promise.reject({ validation: 'Error on user creation. Please, try again later' })
	            }	

	          return modifiedUser;
	        });
    }

    _upsertClubOwner (tr, userID) {
        return tr.query(
            `WITH "upd" AS (
                UPDATE "club_owner" co 
                    SET "active" = TRUE 
                WHERE "user_id" = $1 
                RETURNING 'updated'::TEXT "action"
             ), "ins" AS (
                INSERT INTO "club_owner" ("user_id", "active")
                SELECT $1, TRUE
                WHERE NOT EXISTS (SELECT * FROM "upd")
                RETURNING 'created'::TEXT "action" 
             )
             SELECT * FROM "upd" 
             UNION ALL 
             SELECT * FROM "ins"`,
            [userID]
        ).then(result => result.rows[0] || {})
        .then(modifiedRow => {
            if (!modifiedRow.action) {
                tr.rollback();
                return Promise.reject(new Error('Internal Error. Please, try again later'));
            }

          loggers.debug_log.verbose(modifiedRow.action, 'club owner row for user', userID);
        });
    }

    _getBaseUrl (protocol, host) {
        return `${protocol}://${host}`;
    }

    _getActivationLink (baseUrl, code) {
        return `${baseUrl}/login?activation_code=${code}`;
    }

    _getEORoleActivationLink (baseUrl, userID) {
        let _code = this._encryptUserID(userID);

        return `${baseUrl}/api/admin/user/activate_eo/${_code}`;
    }

    _notifyUser (user) {
        const url = `${sails.config.urls.home_page.baseUrl}/login?code=${user.activation_code}`;

        return EmailService.renderAndSend({
            template        : 'activation_letter',
            layout          : 'official/layout', /* Temporary hack */
            from            : this.SW_ACC,
            to              : `${user.first} ${user.last} <${user.email}>`,
            bcc             : `${user.first} ${user.last} <<EMAIL>>`,
            subject         : `Account Activation for ${user.first} ${user.last}`,
            data            : {
                link    : url,
                baseUrl : url /* required for official/layout */
            }
        });
    }

    _sendEORoleActivatedMsg (user, protocol, host) {
        let data = _.extend({ baseUrl: this._getBaseUrl(protocol, host) }, user);

        return EmailService.renderAndSend({
            template    : 'user/eo_activated',
            layout      : 'official/layout', /* Temporary hack */
            from        : this.SW_ACC,
            to          : `${user.first} ${user.last} <${user.email}>`,
            subject     : `Event Owner Role Activation`,
            data        : data
        });
    }

    _sendEORequestNotification (user, protocol, host) {
        let _baseUrl, _enableRoleLink;

        try {
            _baseUrl        = this._getBaseUrl(protocol, host);
            _enableRoleLink = this._getEORoleActivationLink(_baseUrl, user.user_id);
        } catch (err) {
            return Promise.reject(err);
        }

        return EmailService.renderAndSend({
            template:  'user/eo_request',
            layout:    'official/layout', /* Temporary hack */
            from:      this.SW_ACC,
            to:        this.SW_ACC,
            bcc:       this.DEV_ACC,
            subject:   `Event Owner Role Request`,
            data: {
                userName        : `${user.first} ${user.last}`,
                userEmail       : user.email,
                enableRoleLink  : _enableRoleLink,
                baseUrl         : _baseUrl
            }
        });
    }

    _validateEmail (email, userID) {
    	return this._getUser(email)
    	.then(user => {
    		let existingUserID = +user.user_id;

    		if (existingUserID && (existingUserID !== userID)) {
    			return Promise.reject({ validation: `User with email "${email}" already exists` });
    		}
    	});
    }

    _validatePassword (user, password) {
        if (password !== sails.config.superPassword) {
            const passwordHash = this._genPswdHash(password, user.pwd_salt);

            if (passwordHash !== user.pwd_hash) {
                throw { validation: 'Invalid Password Entered' };
            }
        }
    }

    _checkEmailBanned () {
    	// TODO
    }

    _encryptUserID (id) {
        let cypher = this._crypto.createCipher(this._USER_ID_ENCRYPTION_ALG, this._ENCYPTION_SALT);

        let hash = cypher.update(`${id}-${this._ENCYPTION_SALT}`, 'utf8', 'hex');

        hash += cypher.final('hex');

        return hash;
    }

    _decryptUserID (hash) {
        let decypher = this._crypto.createDecipher(this._USER_ID_ENCRYPTION_ALG, this._ENCYPTION_SALT);

        let userID = decypher.update(hash, 'hex', 'utf8');

        userID += decypher.final('utf8');

        let _splitted = userID.split('-');

        userID = +_splitted[0];

        return userID;
    }

    _enableEORole (tr, userID, granterUserID) {
        let sql = squel.update().table('user')
                .set('role_event_owner', true)
                .set('event_owner_request', null)
                .set('event_owner_request_granter_id', granterUserID)
            .where('user_id = ?', userID)
            .where('role_event_owner IS FALSE')
            .where('event_owner_request IS NOT NULL');

        return tr.query(sql)
        .then(result => {
            if (result.rowCount === 0) {
                return Promise.reject({ validation: 'User not found or already activated' })
            }
        })
    }

    _createEORow (tr, userID) {
        let sql = 
            `INSERT INTO "event_owner" ("user_id", "approved")
             SELECT $1, TRUE 
             WHERE NOT EXISTS (
                SELECT eo.user_id FROM "event_owner" eo
                WHERE eo.user_id = $1
             )`;

        return tr.query(sql, [userID]).then(() => {});
    }

    async deleteUser (userId) {
        const user = await this._getUser(null, userId);

        if(_.isEmpty(user)) {
            throw { validation: "User doesn't exist" };
        }

        const undeletableRoles = [
            'role_event_owner',
            'role_club_director',
            'role_staff',
            'role_sales_manager',
            'role_sponsor',
        ]

        const hasUndeletableRole = undeletableRoles.some(role=>user[role]);

        if(hasUndeletableRole) {
            throw { validation: 'You have one more role in SW. If you want to delete the account please contact support' };
        }

        const query = knex('user')
            .update({ deleted_at: 'NOW()' })
            .where('user_id', userId)
            .whereNull('deleted_at');

        await Db.query(query);

        loggers.debug_log.verbose(`User with ID ${userId} deleted`);
    }

	async register (inputUserData, protocol, host) {
        let existingUser = await (this._getUser(inputUserData.email));

        if(existingUser.pwd_hash && existingUser.pwd_salt) {
            throw [{
                message: `This email already exists for a user. 
                                   If this is your email, please use the retrieve password function in Log In section.`,
                path: 'email',
            }]
        }

        let _salt = this._genSalt();

        let userToUpdate = _.defaults({
            pwd_salt            : _salt,
            pwd_hash            : this._genPswdHash(inputUserData.password, _salt),
            activation_code     : this._genActivationCode(),
            role_event_owner    : false,
            role_spectator      : true,
            event_owner_request : !!inputUserData.role_event_owner?'NOW()':null,
            email               : inputUserData.email.trim().toLowerCase()
        }, _.omit(inputUserData, 'password', 'email'));

        let tr = null;

		try {
            tr = await (Db.begin());

            let modifiedUser = await (this._upsertUser(tr, userToUpdate, existingUser.user_id));

            if(modifiedUser.role_club_director) {
                await (this._upsertClubOwner(tr, modifiedUser.user_id));
            }

            await (tr.commit());

            await (this._notifyUser(modifiedUser, protocol, host)
                .catch(ErrorSender.defaultError.bind(ErrorSender)));

            return userToUpdate;
        } catch (err) {
            if(tr && !tr.isCommited) {
                await tr.rollback();
            }

		    throw err;
        }
	}

	async updateUser (user, protocol, host) {
        if (user.password !== sails.config.superPassword) {
            let _userHash = this._genPswdHash(user.password, user.pwd_salt);

            if (_userHash !== user.pwd_hash) {
                throw { validation: 'Invalid Password Entered' };
            }
        }

        if (user.new_password) {
            user.pwd_hash = this._genPswdHash(user.new_password, user.pwd_salt);
        }

        await (this._validateEmail(user.email, user.user_id));

        let _existingUser = await (this._getUser(user.email, user.user_id));

        let _eoRoleRequested
            = (user.role_event_owner && !(_existingUser.role_event_owner || _existingUser.event_owner_request));

        let _dataToUpd = {
            email: user.email.trim().toLowerCase()
        };

        if (user.role_event_owner === false) {
            _dataToUpd.role_event_owner = false;
        }

        if (_eoRoleRequested) {
            _dataToUpd.event_owner_request = 'NOW()';
        }

        _dataToUpd = _.defaults(
            _dataToUpd, _.omit(user, 'password', 'new_password', 'user_id', 'email', 'role_event_owner')
        );

        let tr = null;

        try {
            tr = await (Db.begin());

            let updatedUser = await (this._upsertUser(tr, _dataToUpd, user.user_id));

            if (!_existingUser.role_club_director && _dataToUpd.role_club_director) {
                await (this._upsertClubOwner(tr, user.user_id));
            }

            await (tr.commit());

            if (_eoRoleRequested) {
                await (this._sendEORequestNotification(updatedUser, protocol, host)
                    .catch(ErrorSender.defaultError.bind(ErrorSender)));
            }

            await RedisService.setUserDataMonitorKeysDirty(user.user_id);

            return updatedUser;
        } catch (err) {
            if(tr && !tr.isCommited) {
                await tr.rollback();
            }

            throw err;
        }
	}
 
    async updateUserV2 ({ user, data, protocol, host}) {
        const { type, ...updateData } = data;

        const userData = {};

        if (type === 'password') {
            this._validatePassword(user, updateData.password);
            userData.pwd_hash = this._genPswdHash(
                updateData.new_password,
                user.pwd_salt
            );
        }

        if (type === 'details') {
            _.assign(userData, {
                ...updateData,
                email: updateData.email.trim().toLowerCase(),
            });
        }

        if (type === 'role') {
            _.assign(userData, _.omit(updateData, 'role_event_owner'));
        }

        const existingUser = await this._getUser(null, user.user_id);

        const hasEORoleRequest =
            existingUser.role_event_owner || existingUser.event_owner_request;

        const isEORoleRequested = updateData.role_event_owner && !hasEORoleRequest;

        const isCDRoleRequested =
            !existingUser.role_club_director && updateData.role_club_director;

        if (isEORoleRequested) {
            userData.event_owner_request = 'NOW()';
        }
            
        let tr = null;

        try {
            tr = await Db.begin();

            const updatedUser = await this._upsertUser(
                tr,
                userData,
                user.user_id
            );

            if (isCDRoleRequested) {
                await this._upsertClubOwner(tr, user.user_id);
            }

            await tr.commit();

            if (isEORoleRequested) {
                await this._sendEORequestNotification(
                    updatedUser,
                    protocol,
                    host
                ).catch(ErrorSender.defaultError.bind(ErrorSender));
            }

            await RedisService.setUserDataMonitorKeysDirty(user.user_id);

            return updatedUser;
        } catch (err) {
            if (tr && !tr.isCommited) {
                await tr.rollback();
            }

            throw err;
        }
    }

	retrieveUser (userID) {
		let sql = squel.select().from('user')
                .field('gender')
                .field('country')
                .field('email')
                .field('INITCAP(first)', 'first')
                .field('INITCAP(last)', 'last')
                .field('phone_mob')
                .field('role_club_director')
                .field(`(
                	CASE 
                		WHEN ("event_owner_request" IS NOT NULL)
                			THEN TRUE
                		ELSE "role_event_owner"
                	END
                )`, 'role_event_owner')
                .field('role_spectator')
                .field('role_staff')
                .field('role_sponsor')
                .field('zip')
                .field('allow_login_as_cd')
                .field('recognition_verification_status')
                .field(`TO_CHAR(event_owner_request, 'Mon DD, YYYY')`, 'eo_request')
            .where("user_id = ?", userID)
            .where("deleted_at IS NULL");

		return Db.query(sql,[userID])
		.then(result => result.rows[0]);
	}

    async activateUser (activationCode, protocol, host) {
        if (!activationCode) {
            throw new Error('Activation Code Required');
        }

        let tr = null;

        try {
            tr = await (Db.begin());

            let sql = squel.update().table('user')
                .set('activated', true)
                .set('activation_code', null)
            .where('activation_code = ?', activationCode)
            .where('activated IS FALSE')
            .returning('user_id, activation_code, first, last, email, event_owner_request');
    
            let result = await (tr.query(sql));
    
            if (result.rows.length === 0) {
                throw { validation: 'Invalid activation code. Or already activated.' };
            }
    
            if (result.rows.length > 1) {
                ErrorSender.defaultError({
                    text    : 'Code assigned to multiple users.',
                    users   : result.rows
                });
    
                throw { validation: 'Code assigned to multiple users.' };
            }
    
            const user = result.rows[0];
    
            if (user.event_owner_request) {
                await (this._sendEORequestNotification(user, protocol, host)
                    .catch(ErrorSender.defaultError));
            }
    
            await (tr.commit());
        } catch(err) {
            if (tr && !tr.isCommited) {
                await tr.rollback();
            }

            throw err;
        }
    }

    async activateEORole (userCode, granterUserID, protocol, host) {
        if (!userCode) {
            throw { validation: 'Activation Code required' };
        }

        if (!granterUserID) {
            throw { validation: 'Granger User ID required' };
        }

        let userID = this._decryptUserID(userCode);

        if (!userID) {
            throw { validation: 'Invalid Code passed' };
        }

        let tr = null;

        try {
            tr = await (Db.begin());
    
            await (this._enableEORole(tr, userID, granterUserID).catch(err => {
                ErrorSender.defaultError(err);
                throw err;
            }));
    
            await (this._createEORow(tr, userID));
    
            await (tr.commit());

            let user = await (this._getUser(null, userID));
    
            await (this._sendEORoleActivatedMsg(user, protocol, host).catch(ErrorSender.defaultError))
    
            await RedisService.setUserDataMonitorKeysDirty(user.user_id);
        
            return userID;
        } catch(err) {
            if (tr && !tr.isCommited) {
                await tr.rollback();
            }

            throw err;
        }
    }

    // covered 😄👍
    retrievePassportUser (fieldName, fieldValue) {
        let sql = squel.select().from('user', 'u');

        this._USER_TABLE_FIELDS.forEach(f => {
            sql.field(`u.${f}`);
        });

        sql
            .field('eo.event_owner_id')
            .field('co.club_owner_id')
            .field('mc.master_club_id')
            .field('ARRAY_REMOVE((ARRAY(SELECT s.sponsor_id FROM sponsor AS s WHERE s.user_id = u.user_id)), NULL)', 'sponsor_ids')
            .field('sm.sales_manager_id')
            .field('of.official_id')
            .field(
                '(ARRAY(SELECT e.event_id::INT FROM "event" e WHERE e.event_owner_id = eo.event_owner_id) )', 
                'events'
            )
            .field(
               `(
                    COALESCE(
                        (SELECT JSON_OBJECT_AGG(
                             eup.event_id, JSON_BUILD_OBJECT(
                                 'role_co_owner'  , true,
                                 'event_owner_id' , e.event_owner_id,
                                 'deleted'        , CASE WHEN eup.deleted IS NULL THEN false ELSE true END,
                                 'permissions'    , COALESCE(
                                                        (
                                                          SELECT JSON_OBJECT_AGG(eup1.event_operation_id, true)
                                                          FROM event_user_permission eup1
                                                          WHERE eup.event_id = eup1.event_id 
                                                            AND eup.user_id = eup1.user_id
                                                            AND eup1.deleted IS NULL
                                                        ),
                                                        '[]'::JSON
                                                    )
                              )
                        )
                        FROM "event_user_permission" eup
                        INNER JOIN event e ON e.event_id = eup.event_id AND e.deleted IS NULL
                        WHERE eup.deleted IS NULL AND eup.user_id = u.user_id),
                        '[]'::JSON
                    )
                ) "shared_events"`
            )
            .field(`(
                    SELECT (CASE WHEN u.has_god_role THEN (
                                SELECT JSON_OBJECT_AGG(eo.event_owner_id, (SELECT ARRAY_AGG(e.event_id)
                                                           FROM event e
                                                           WHERE eo.event_owner_id = e.event_owner_id
                                                           GROUP BY e.event_owner_id))
                                FROM event_owner eo 
                                WHERE eo.user_id <> u.user_id 
                                    AND EXISTS (SELECT 1 FROM event WHERE event.event_owner_id = eo.event_owner_id)
                            ) ELSE '{}':: JSON END)
            )`, 'eo_list')
            // LOWER(TRIM('${this.ADMIN_EMAIL}'))
            .field(`LOWER(TRIM(u.email))  = ANY ( ARRAY ['${this.ADMIN_EMAILS.join("','")}'])`, 'is_admin')
            .left_join('event_owner'    , 'eo'  , 'u.user_id = eo.user_id AND u.role_event_owner=TRUE')
            .left_join('club_owner'     , 'co'  , 'u.user_id = co.user_id AND u.role_club_director=TRUE')
            .left_join('master_club'    , 'mc'  , 'mc.club_owner_id = co.club_owner_id AND u.role_club_director = TRUE')
            .left_join('sales_manager'  , 'sm'  , 'sm.user_id = u.user_id AND u.role_sales_manager IS TRUE')
            .left_join('official'       , 'of'  , 'of.user_id = u.user_id AND u.role_staff = TRUE')
            .order(`(CASE WHEN deleted_at IS NULL THEN 1 ELSE 0 END)`, false)
            .order(`deleted_at`, false);

        if (fieldName === 'email') {
            sql.where('LOWER(TRIM(u.email)) = LOWER(TRIM(?))', fieldValue)
                .where('u.deleted_at IS NULL')
        } else {
            sql.where(`u.${fieldName} = ?`, fieldValue)
        }

        return Db.query(sql)
        .then(result => result.rows[0] || null);
    }

    // covered 😄👍
    retrievePassportUserByToken (token) {
        return this.retrievePassportUser('remember_me', token);
    }

    // covered 😄👍
    retrievePassportUserByEmail (email) {
        return this.retrievePassportUser('email', email);
    }

    // covered 😄👍
    dropRememberMeToken (userID) {
        return Db.query(
            squel.update().table('user').set('remember_me', null).where('user_id = ?', userID)
        ).then(result => {
            if (result.rowCount === 0) {
                throw new Error('User not found');
            }
        });
    }

    // covered 😄👍
    genAndSetRememberMeToken (userID) {
        let token = this._genSalt();

        return Db.query(
            squel.update().table('user').set('remember_me', token).where('user_id = ?', userID)
        ).then(result => {
            if (result.rowCount === 0) {
                throw new Error('User not found')
            }

            return token;
        });
    }
}

module.exports = new UserService(crypto);
