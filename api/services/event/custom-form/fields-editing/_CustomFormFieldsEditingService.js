
const {
    customFormField: fieldValidationSchema,
    fieldsOrderData: fieldsOrderValidationSchema
} = require('../../../../validation-schemas/custom-form');

class CustomFormFieldsEditingService {

    async create (formID, fieldData) {
        if(!formID) {
            throw { validation: 'Form ID required' };
        }

        this.#validateFieldData(fieldData);

        const dataForCreate = await this.#prepareData(formID, fieldData);

        return this.#createFieldRow(dataForCreate);
    }

    async update (formID, fieldID, fieldData) {
        if(!formID) {
            throw { validation: 'Form ID required' };
        }

        if(!fieldID) {
            throw { validation: 'Field ID required' };
        }

        this.#validateFieldData(fieldData);

        const dataForUpdate = await this.#prepareData(formID, fieldData);

        await this.#updateFieldRow(Db, fieldID, dataForUpdate);
    }

    delete (formID, fieldID) {
        if(!formID) {
            throw { validation: 'Form ID required' };
        }

        if(!fieldID) {
            throw { validation: 'Field ID required' };
        }

        return this.#removeFieldRow(formID, fieldID);
    }

    async changeOrder (formID, fieldsOrderData) {
        let validationResult = fieldsOrderValidationSchema.validate(fieldsOrderData);

        if (validationResult.error) {
            throw { validationErrors: validationResult.error.details };
        }

        let tr;

        try {
            tr = await Db.begin();

            for(const field of fieldsOrderData) {
                await this.#updateFieldRow(tr, field.field_id, { sort_order: field.sort_order });
            }

            await tr.commit();
        } catch (err) {
            if(tr && !tr.isCommited) {
                await tr.rollback();
            }

            throw err;
        }
    }

    async #removeFieldRow (formID, fieldID) {
        const query = knex('custom_form_field')
            .delete()
            .where('custom_form_field_id', fieldID)
            .where('custom_form_event_id', formID);

        const { rowCount } = await Db.query(query);

        if(!rowCount) {
            throw { validation: 'Field not found!' };
        }
    }

    #validateFieldData (formID, fieldData) {
        let validationResult = fieldValidationSchema.validate(fieldData);

        if (validationResult.error) {
            throw { validationErrors: validationResult.error.details };
        }
    }

    async #prepareData (formID, fieldData) {
        fieldData.custom_form_field_type_id = await this.#getFieldTypeByID(fieldData.type);
        fieldData.custom_form_event_id = formID;

        delete fieldData.type;

        if(!_.isEmpty(fieldData.options)) {
            fieldData.options = JSON.stringify(fieldData.options);
        } else {
            fieldData.options = null;
        }

        return fieldData;
    }

    async #getFieldTypeByID (fieldType) {
        const query = knex('custom_form_field_type')
            .select('custom_form_field_type_id as id')
            .where('type', fieldType);

        const { rows: [type] } = await Db.query(query);

        if(!type?.id) {
            throw { validation: 'Identified for selected type not found' };
        }

        return type.id;
    }

    async #createFieldRow (fieldData) {
        const query = knex('custom_form_field')
            .insert(fieldData)
            .returning('custom_form_field_id AS id')

        const { rows: [field] } = await Db.query(query);

        if(_.isEmpty(field)) {
            throw { validation: 'Field not created' };
        }

        return field.id;
    }

    async #updateFieldRow (tr, fieldID, fieldData) {
        const query = knex('custom_form_field')
            .update(fieldData)
            .where('custom_form_field_id', fieldID);

        const { rowCount } = await tr.query(query);

        if(rowCount < 1) {
            throw { validation: 'Field not updated' };
        }
    }
}

module.exports = new CustomFormFieldsEditingService();
