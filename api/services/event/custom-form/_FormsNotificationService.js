const { DEFAULT_SENDER } = require('../../../constants/emails');


class FormsNotificationService {
    #TEMPLATE = 'custom-form/submitted';

    #EVENT_EMAIL_REASON_TYPE = 'email';

    #EVENT_EMAIL_RECIPIENT_TYPE = 'custom_form';

    formatFormFieldsForEmail (row, form) {
        const rowFields = row?.fields ?? [];

        if (!Array.isArray(form?.fields) || form.fields.length === 0) {
            return rowFields;
        }

        const valueByLabel = Object.fromEntries(
            rowFields.map(({ label, value }) => [
                label,
                value ?? '',
            ])
        );

        return form.fields
            .map(({ label, type }) => ({ label, value: valueByLabel[label] ?? '', type }));
    }

    async sendForm({ to, data, log = {} }) {
        if (!to) {
            throw { validation: 'Recipient email required' };
        }
        const subject = this.#getSubject(data?.eventName);

        const email = await EmailService.renderAndSend({
            template: this.#TEMPLATE,
            data,
            from: DEFAULT_SENDER,
            to,
            subject,
        });

        await this.#saveEventEmail(log, to, subject, email);
    }

    #getSubject(eventName) {
        return `Completed custom form for ${eventName}`;
    }

    async #saveEventEmail(log, to, subject, email) {
        const insert = knex('event_email').insert({
            event_id: log.event_id || null,
            purchase_id: log.purchase_id || null,
            recipient_type: this.#EVENT_EMAIL_RECIPIENT_TYPE,
            email_from: DEFAULT_SENDER,
            email_to: to,
            email_subject: subject,
            email_text: email.text || null,
            email_html: email.html || null,
            reason_type: this.#EVENT_EMAIL_REASON_TYPE,
            email_id: email.email_id || null,
        });

        await Db.query(insert);
    }
}

module.exports = new FormsNotificationService();

