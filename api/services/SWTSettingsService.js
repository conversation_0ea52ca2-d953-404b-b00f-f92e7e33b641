'use strict';

const { POINT_OF_SALES_TYPE } = require("../constants/sales-hub");
const PaymentService = require("./PaymentService");

const TICKET_TYPES_SQL =
`SELECT 
     COALESCE(e.tickets_options, '{}'::JSON) "changes", 
     e.tickets_sw_fee, 
     TO_CHAR(e.tickets_purchase_date_start, 'MM/DD/YYYY HH24:MI') purchase_date_start, 
     TO_CHAR(e.tickets_purchase_date_end, 'MM/DD/YYYY HH24:MI') purchase_date_end, 
     COALESCE(e.tickets_published, FALSE) tickets_published, 
     COALESCE(e.tickets_visible, FALSE) tickets_visible, 
     e.event_tickets_code, 
     'MM/DD/YYYY HH:mm' date_format,
     e.stripe_tickets_fee_payer "stripe_fee_payer", 
     e.tilled_tickets_fee_payer "tilled_fee_payer", 
     e.tickets_sw_fee_payer "sw_fee_payer",
     e.tickets_sw_fee "sw_fee",
     e.stripe_tickets_percent,
     e.stripe_tickets_fixed,
     (e.tickets_settings->>'allow_point_of_sales')::BOOLEAN IS TRUE "allow_point_of_sales",
     (select epos.point_of_sales_id 
        from event_point_of_sales epos 
        where epos.sw_event_id = e.event_id 
        and epos.point_of_sales_type = '${POINT_OF_SALES_TYPE.TICKETS}') as sales_hub_point_of_sale_id,
     COALESCE((e.tickets_settings->>'require_recipient_name_for_each_ticket'), 'false')::BOOLEAN "ticketing_mode",
     COALESCE((e.tickets_settings ->> 'enable_free_tickets')::BOOLEAN, false) "enable_free_tickets",
     COALESCE((e.tickets_settings ->> 'use_merchandise_sales')::BOOLEAN, false) "use_merchandise_sales", 
     COALESCE((e.tickets_settings ->> 'use_vertical_insurance')::BOOLEAN, false) "use_vertical_insurance", 
     COALESCE((e.tickets_settings ->> 'require_coupon')::BOOLEAN, false) "require_coupon",
     COALESCE((
        e.tickets_settings ->> 'require_covid_test_for_each_ticket'
     )::BOOLEAN, false) "require_covid_test_for_each_ticket",
     e.tickets_purchase_passcode "purchase_passcode",
     e.tickets_refund_passcode "refund_passcode",
     e.tickets_has_barcodes "use_tg",
     e.tickets_stripe_statement "tickets_stripe_statement",
     e.tickets_tilled_statement "tickets_tilled_statement",
     COALESCE((e.tickets_payment_provider), '${PaymentService.__PAYMENT_PROVIDERS__.STRIPE}') "payment_provider",
     e.ticket_camps_registration "camps_sales",
     sa.stripe_account_id,
     e.tilled_tickets_account_id "tilled_account_id",
     sa.id "account_id",
     (e.date_end AT TIME ZONE 'UTC') AS date_end,
     (
        (NOW() AT TIME ZONE e.timezone) <= e.tickets_purchase_date_end
        AND (NOW() AT TIME ZONE e.timezone) >= e.tickets_purchase_date_start
      ) AS is_tickets_purchase_open,
      ARRAY( 
        SELECT TO_CHAR(dd, 'YYYY-MM-DD') 
        FROM GENERATE_SERIES ( 
            e.date_start, 
            e.date_end, 
            '1 day'::INTERVAL 
        ) "dd"
     ) "days_dates",
     (
       SELECT ROW_TO_JSON(coupons_settings)
       FROM (
                SELECT cs.is_active, cs.team_code_source_enabled, cs.team_code_settings, cs.custom_code_source_enabled
                FROM event_ticket_buy_entry_code_settings cs
                WHERE cs.event_id = e.event_id
            ) coupons_settings
     ) coupons_settings,
     (COALESCE(
        COUNT(p.purchase_id) FILTER (WHERE p.payment_for = 'tickets' AND p.type IN ('card', 'ach')
     ), 0) > 0) "block_tickets_keys_edit",(
        SELECT COALESCE(ARRAY_TO_JSON(ARRAY_AGG(ROW_TO_JSON("tickets"))), '[]'::JSON)
        FROM (
            SELECT  
                 et.event_ticket_id, 
                 et.label, 
                 et.initial_price::NUMERIC, 
                 et.published, 
                 COALESCE(et.prices, '{}')::JSON prices, 
                 et.short_label, 
                 et.description, 
                 et.background_color, 
                 et.foreground_color, 
                 et.current_price,
                 et.waitlisted,
                 et.kiosk_surcharge,
                 et.event_camp_id,
                 et.border_color,
                 et.is_free,
                 et.merchandise_type,
                 (
                    SELECT ARRAY_AGG(vd ORDER BY (TO_TIMESTAMP(vd.*::TEXT, 'YYYY-MM-DD')) ASC
                    ) FROM JSONB_OBJECT_KEYS(et.valid_dates) AS vd
                 ) "valid_dates",
                 COALESCE(NULLIF(et."application_fee", 0), e."tickets_sw_fee", 0) "sw_fee",
                 et.waitlist_switching_count, (
                    SELECT COUNT(p.purchase_id)
                    FROM "purchase" p 
                    INNER JOIN "purchase_ticket" pt 
                        ON pt.purchase_id = p.purchase_id
                    WHERE pt.event_ticket_id = et.event_ticket_id 
                        AND p.event_id = et.event_id 
                        AND p.status IS NOT NULL AND p.status <> 'canceled'
                        AND p.type IN('card', 'check', 'free')
                        AND pt.quantity > 0
                        AND p.canceled_date IS NULL
                 ) "bought",
                 et.ticket_type,
                 et.sub_type,
                 et.allow_reentry,
                 (
                   EXISTS (
                     SELECT 1 FROM purchase p
                     LEFT JOIN purchase_ticket pt 
                        ON pt.event_ticket_id = et.event_ticket_id
                     WHERE p.purchase_id = pt.purchase_id
                     AND p.status IN('paid', 'pending')
                     AND p.is_ticket IS TRUE
                     AND p.payment_for ='tickets'
                     AND pt.canceled IS NULL
                   )
                 ) is_ticket_purchased
             FROM event_ticket et 
             WHERE et.event_id = e."event_id"
             ORDER BY et.sort_order, et.event_ticket_id
         ) "tickets"     
     ) "tickets"    
FROM "event" e  
LEFT JOIN "purchase" p 
    ON p.event_id = e.event_id
    AND p.canceled_date IS NULL 
    AND p.status <> 'canceled'
LEFT JOIN "stripe_account" sa ON sa.secret_key = e.stripe_tickets_private_key
WHERE e.event_id = $1
GROUP BY e.event_id, sa.stripe_account_id, sa.id`;


module.exports.__findCamps__ = function  (eventID) {
	return Db.query(
        `SELECT 
            ec.event_camp_id "id", 
            ec."name" 
         FROM "event_camp" ec 
         WHERE ec."event_id" = $1
            AND ec."deleted" IS NULL
         ORDER BY ec.sort_order ASC`,
        [eventID]
    ).then(res => res.rows);
};

module.exports.__filterTicketsOfRemovedCamps__ = function (camps, tickets) {
    return tickets.filter(ticket => {
        let ticketsCamp = camps.filter(camp => {
            return Number(camp.id) === Number(ticket.event_camp_id);
        });

        let exists = (ticketsCamp.length > 0);

        return exists;
    });
}

module.exports.getAllTicketTypes = function (eventID) {
	if (!Number.isInteger(eventID) || (eventID < 0)) {
		return Promise.reject(new Error('Invalid Event ID'));
	}

	return Db.query(TICKET_TYPES_SQL, [eventID])
	.then(result => {
		let event 	         = result.rows[0] || { changes: {}, tickets: [] };
		let tickets          = event.tickets;
			event.tickets    = undefined;

		if (!!event.camps_sales) {
			return this.__findCamps__(eventID)
			.then(camps => ({ 
                event, 
                tickets: this.__filterTicketsOfRemovedCamps__(camps, tickets), 
                camps 
            }))
		} else {
			return {event, tickets};
		}
	})
};

module.exports.findEventPaymentDiscounts = function (eventCode, eventID) {
    let whereIDField, identifier;

    if (eventCode) {
        whereIDField = 'event_tickets_code';
        identifier = eventCode;
    } else if (eventID) {
        whereIDField = 'event_id';
        identifier = eventID;
    } else {
        return Promise.reject(new Error('Invalid Parameters: event code or event id required'))
    }
        
    return Db.query(
        `SELECT (
             CASE 
                 WHEN (e."tickets_discount" IS NULL)
                     THEN NULL 
                 WHEN (
                     SELECT COUNT(*) = 0
                     FROM (
                         SELECT JSONB_OBJECT_KEYS(e."tickets_discount"->'amount')
                     ) "d"
                 ) AND (
                     SELECT COUNT(*) = 0
                     FROM (
                         SELECT JSONB_OBJECT_KEYS(e."tickets_discount"->'quantity')
                     ) "d"
                 )
                     THEN NULL
                 ELSE e."tickets_discount"
             END
         ) "tickets_discount"
         FROM "event" e 
         WHERE e."${whereIDField}" = $1`,
        [identifier]
    ).then(res => {
        if (res.rowCount === 0) {
            return Promise.reject({ validation: 'Event not found' });
        } else {
            return res.rows[0].tickets_discount;
        }
    })
}
