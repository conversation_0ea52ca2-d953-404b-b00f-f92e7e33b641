const { uniq } = require('lodash');
const { PAYMENT_FOR, PAYMENT_METHOD, PAYMENT_PROVIDER, FEE_PAYER } = require('../../../constants/payments');
const { WEBHOOK_EVENT_TYPES } = require('../../../constants/sales-hub');
const crypto = require('crypto');
const { COUNTRY_NAMES } = require('../../../constants/common');

class SalesHubOrderWebhookHandler {
    get __PAYMENT_PROVIDER_TYPE() {
        return {
            PAYMENT_HUB: 'payment-hub'
        }
    }

    get __COUNTRY_CODES() {
        return {
            US: 'US',
            AS: 'AS',
            BS: 'BS',
            BM: 'BM',
            DO: 'DO',
            GU: 'GU',
            MX: 'MX',
            PR: 'PR',
            VI: 'VI',
            UA: 'UA',
            CA: 'CA',
        }
    }

    get __CENT() {
        return 0.01;
    }

    async handle(webhookEvent) {
        switch (webhookEvent.type) {
            case WEBHOOK_EVENT_TYPES.ORDER_PAID:
                return this.handleOrderPaid(webhookEvent);
        }
    }

    async handleOrderPaid(webhookEvent) {
        const { data: order } = webhookEvent;

        const event = await this._getEventByPointOfSalesId(order.pointOfSaleId);

        const eventTickets = await this._getTicketsByProductIds(
            order.orderItems.map(({ productId }) => productId)
        );

        const additionalFields =
            await this._getAdditionalFieldsByCustomFieldIds(
                event.sw_event_id,
                order.customFields.map(({ customFieldId }) => customFieldId)
            );

        const [payment] = order.payments;

        let purchaseData = {
            sales_hub_order_id: order.id,
            method: payment ? PAYMENT_METHOD.CARD : PAYMENT_METHOD.FREE,
            user: this._mapToPaymentUser(order.customer),
            receipt: this._mapToReceiptsWithNames(
                order.orderItems,
                eventTickets
            ),
            total: order.amount / 100,
            event_id: event.sw_event_id,
            type: PAYMENT_FOR.TICKETS,
            additional: this._mapToAdditionalFields(
                order.customFields,
                additionalFields
            ),
            ticket_buy_entry_code: order.entryCode,
            // we use seller mode always, since we pass item total with fees
            stripe_fee_payer: FEE_PAYER.SELLER, 
            sw_fee_payer: FEE_PAYER.SELLER,
        };

        let isAppleDevice = false;

        if(payment) {
            const { clientInfo } = payment;

            purchaseData = Object.assign(purchaseData, {
                fingerprint: payment.paymentMethod.fingerprintAtProvider,
                card_last_4: payment.paymentMethod.cardLast4,
                payment_provider: payment.paymentProviderType,
                ip: clientInfo?.ip,
                stripe_payment_intent_id: payment.paymentIntentIdAtGateway,
                sales_hub_payment_id: payment.id,
            });

            isAppleDevice = this._isAppleDevice(clientInfo);
        }

        await buyTicketsService.buyWithSalesHub(
            purchaseData,
            isAppleDevice,
        );
    }

    _mapToPaymentUser(salesHubCustomer) {
        return {
            first: salesHubCustomer.firstName,
            last: salesHubCustomer.lastName,
            phone: salesHubCustomer.phone,
            country: this._mapCountry(salesHubCustomer.country),
            email: salesHubCustomer.email,
            zip: salesHubCustomer.zip,
            password: crypto.randomBytes(8).toString('hex'),
        }
    }

    _mapCountry(country) {
        switch (country.toUpperCase()) {
            case this.__COUNTRY_CODES.US:
                return COUNTRY_NAMES.US;
            case this.__COUNTRY_CODES.AS:
                return COUNTRY_NAMES.AS;
            case this.__COUNTRY_CODES.BS:
                return COUNTRY_NAMES.BS;
            case this.__COUNTRY_CODES.BM:
                return COUNTRY_NAMES.BM;
            case this.__COUNTRY_CODES.DO:
                return COUNTRY_NAMES.DO;
            case this.__COUNTRY_CODES.GU:
                return COUNTRY_NAMES.GU;
            case this.__COUNTRY_CODES.MX:
                return COUNTRY_NAMES.MX;
            case this.__COUNTRY_CODES.PR:
                return COUNTRY_NAMES.PR;
            case this.__COUNTRY_CODES.VI:
                return COUNTRY_NAMES.VI;
            case this.__COUNTRY_CODES.UA:
                return COUNTRY_NAMES.UA;
            case this.__COUNTRY_CODES.CA:
                return COUNTRY_NAMES.CA;
            default:
                throw new Error(`Unsupported country code ${country}`);
        }

    }

    _isAppleDevice(clientInfo) {
        return clientInfo.device.vendor === 'Apple';
    }

    _mapToReceiptsWithNames(orderItems, eventTickets) {
        const eventTicketsByProductId = _.keyBy(
            eventTickets,
            (ticket) => ticket.sales_hub_product_id
        );

        return orderItems.flatMap((orderItem) => {
            const ticket = eventTicketsByProductId[orderItem.productId];
    
            const totalInDollars = orderItem.totalAmount / 100;
            const quantity = orderItem.quantity;

            const unitPrices = this._splitTotalIntoUnitPrices(totalInDollars, quantity);

            return unitPrices.map((price) => ({
                id: ticket.event_ticket_id,
                price,
                quantity: 1,
                sales_hub_order_item_id: orderItem.id,
                first: orderItem.holder?.firstName,
                last: orderItem.holder?.lastName,
              }));
        });
    }

    /**
     * Splits a total dollar amount into `quantity` number of items, rounding fairly.
     * 
     * @param totalAmountInDollars Total amount in dollars (e.g. 10.03)
     * @param quantity Number of items to divide into
     * @returns number[] of length `quantity`, where each entry is a price in dollars (floats with 2 decimals)
     */
    _splitTotalIntoUnitPrices(totalAmountInDollars, quantity) {
        // 10.03 / 3 = 3.34
        const basePrice = Math.floor((totalAmountInDollars / quantity) * 100) / 100;
        // 3.34 * 3 = 10.02
        const baseTotal = basePrice * quantity;

        // 10.03 - 10.02 = 0.01, 1 cent
        let remainingCents = Math.round((totalAmountInDollars - baseTotal) * 100);

        const result = Array.from({ length: quantity }, () => basePrice);

        for (let i = 0; i < result.length && remainingCents > 0; i++) {
            result[i] += this.__CENT;
            remainingCents--;
        }

        return result;
    }

    _mapToAdditionalFields(salesHubCustomFields, additionalFields) {
        const additionalFieldsBySalesHubCustomFieldId = _.keyBy(
            additionalFields,
            (field) => field.sales_hub_custom_field_id
        );

        return salesHubCustomFields.reduce(
            (mappedAdditionalFields, salesHubCustomField) => {
                const additionalField =
                    additionalFieldsBySalesHubCustomFieldId[
                        salesHubCustomField.customFieldId
                    ];
                
                mappedAdditionalFields[additionalField.field_id] =
                    salesHubCustomField.value;

                return mappedAdditionalFields;
            },
            {}
        );
    }

    async _getAdditionalFieldsByCustomFieldIds(eventId, customFieldIds) {
        const uniqueCustomFieldIds = uniq(customFieldIds);

        const query = knex('event as e')
            .select(
                knex.raw(`field ->> 'field' as field_id`),
                knex.raw(
                    `field ->> 'sales_hub_custom_field_id' as sales_hub_custom_field_id`
                )
            )
            .leftJoin(
                knex.raw(
                    'lateral JSONB_ARRAY_ELEMENTS(e.tickets_purchase_additional_fields) as field'
                ),
                knex.raw('true')
            )
            .join(
                'event_point_of_sales as epos',
                'epos.sw_event_id',
                'e.event_id'
            )
            .where('e.event_id', eventId)
            .andWhereRaw(
                `(field ->> 'sales_hub_custom_field_id')::TEXT = ANY(?)`,
                [uniqueCustomFieldIds]
            );

        const additionalFields = await Db.query(query).then(({ rows }) => rows);

        if (uniqueCustomFieldIds.length !== additionalFields.length) {
            const nonExistingProductIds = uniqueCustomFieldIds.filter(
                (customFieldId) =>
                    !additionalFields.some(
                        (field) =>
                            field.sales_hub_custom_field_id === customFieldId
                    )
            );
            throw new Error(
                `Additional Field not found by custom field IDs '${nonExistingProductIds}'`
            );
        }

        return additionalFields;
    }

    async _getTicketsByProductIds(productIds) {
        const uniqueProductIds = uniq(productIds);

        const query = knex('sales_hub_product_event_ticket')
            .select('event_ticket_id', 'sales_hub_product_id')
            .whereIn('sales_hub_product_id', uniqueProductIds);

        const eventTickets = await Db.query(query).then(({ rows }) => rows);

        if (uniqueProductIds.length !== eventTickets.length) {
            const nonExistingProductIds = uniqueProductIds.filter(
                (productId) =>
                    !eventTickets.some(
                        (ticket) => ticket.sales_hub_product_id === productId
                    )
            );
            throw new Error(
                `Event ticket not found by product IDs '${nonExistingProductIds}'`
            );
        }

        return eventTickets;
    }

    async _getEventByPointOfSalesId(posId) {
        const query = knex('event_point_of_sales')
            .select('event_point_of_sales_id', 'sw_event_id')
            .where('point_of_sales_id', posId);

        const event = await Db.query(query).then(({ rows }) => rows[0]);

        if (!event) {
            throw new Error(`Event not found for point of sale '${posId}'`);
        }

        return event;
    }
}

module.exports = new SalesHubOrderWebhookHandler();
