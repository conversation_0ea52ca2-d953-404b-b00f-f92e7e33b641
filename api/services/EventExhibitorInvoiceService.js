const {PAYMENT_TYPE, PAYMENT_STATUS, PAYMENT_FOR} = require("../constants/payments");
const {OTHER_BOOTH} = require("../constants/event-exhibitor");
const argv = require('optimist').argv;
const settingsRow = argv.prod?'stripe_connect':'stripe_connect_dev';

class EventExhibitorInvoiceService {
    /**
     * This method returns a list of exhibitor invoices for a given event
     *
     * @param eventId
     *
     * @param filters
     * @return {Promise<Array>} - invoice list
     */
    async getExhibitorInvoices(eventId, filters) {
        let query = `
            SELECT
                sp.first,
                sp.last,
                sp.company_name,
                sp.sponsor_id AS "exhibitor_id",
                TO_CHAR(p.date_paid, 'Mon DD, YYYY, HH12:MI AM') AS "date_paid",
                p.type AS "payment_type",
                p.status AS "payment_status",
                p.amount AS "amount",
                p.purchase_id,
                exi.event_exhibitor_invoice_id,
                CONCAT_WS(
                    ', ',
                    NULLIF((
                        SELECT ARRAY_TO_STRING(ARRAY_AGG(eb.title), ', ')
                        FROM unnest(exi.booths) AS bid
                        JOIN event_booth AS eb ON eb.event_booth_id = bid
                    ), ''),
                    NULLIF((
                        SELECT ARRAY_TO_STRING(ARRAY_AGG(b.title), ', ')
                        FROM (
                            SELECT 'Custom Booth' AS title
                            FROM purchase_booth AS pb
                            WHERE pb.purchase_id = p.purchase_id
                                AND pb.event_booth_id IS NULL
                        ) AS b
                    ), '')
                ) AS booth_title
            FROM "purchase" p
            INNER JOIN "sponsor" sp ON sp.sponsor_id = p.sponsor_id
            INNER JOIN "event_exhibitor_invoice" exi 
                ON exi.purchase_id = p.purchase_id
                AND exi.deleted_at IS NULL
            WHERE p.event_id = $1
            AND p.payment_for = $2
        `;

        const params = [eventId, PAYMENT_FOR.BOOTHS];
        let paramIndex = 3;

        if (filters?.sponsor) {
            query += ` AND p.sponsor_id = $${paramIndex}`;
            params.push(filters.sponsor);
            paramIndex++;
        }

        query += ` ORDER BY sp.company_name, p.date_paid DESC`;

        const { rows: invoices = [] } = await Db.query(query, params);

        return invoices;
    }

    /**
     * This method returns initial data for exhibitor invoice creation
     *
     * @param eventId
     *
     * @returns {Promise<Object>} - exhibitor invoice data
     */
    async getExhibitorInvoiceInitData(eventId) {
        const query = knex
            .select(
                {
                    event_dates: knex.raw(`(
                        SELECT json_object_agg(d.date, false)
                        FROM (
                            SELECT generate_series(e.date_start, e.date_end, '1 day'::interval) as date
                        ) as d
                    )`)
                },
                {
                    event_booths: knex.raw(`(
                        SELECT COALESCE(ARRAY_TO_JSON(ARRAY_AGG(ROW_TO_JSON(b))), '[]')
                        FROM (
                            SELECT 
                                FORMAT('%s, $%s', eb.title, eb.fee) as label, 
                                eb.event_booth_id AS id, 
                                eb.fee AS amount,
                                eb.title,
                                eb.description
                            FROM event_booth AS eb
                            WHERE eb.event_id = e.event_id
                            AND eb.is_enabled IS TRUE
                        ) AS b
                    )`)
                }
            )
            .from('event as e')
            .where('e.event_id', eventId);

        const {rows: [exhibitorInvoiceFormData]} = await Db.query(query);

        if (_.isEmpty(exhibitorInvoiceFormData)) {
            throw { validation: 'Event not found' };
        }

        exhibitorInvoiceFormData.event_booths.unshift(OTHER_BOOTH);

        return exhibitorInvoiceFormData;
    }

    /**
     * This method returns exhibitor invoice information for a given exhibitor
     *
     * @param eventId
     * @param exhibitorId
     * @param exhibitorInvoiceId
     *
     * @returns {Promise<Object>} - exhibitor invoice information
     */
    async getExhibitorInvoiceInfo(eventId, exhibitorId, exhibitorInvoiceId) {
        const exhibitorInvoiceDetails = await getExhibitorInvoiceDetails(eventId, exhibitorId, exhibitorInvoiceId);

        return formatExhibitorInvoiceDetails(exhibitorInvoiceDetails);
    }

    /**
     * This method creates an exhibitor invoice
     *
     * @param eventId
     * @param exhibitorId
     * @param userAccessIds
     * @param exhibitorInvoiceData
     *
     * @returns {Promise<void>}
     */
    async createExhibitorInvoice(eventId, exhibitorId, userAccessIds, exhibitorInvoiceData) {
        let tr;

        try {
            tr = await Db.begin();

            const {status: applicationStatus, event_exhibitor_id: eventExhibitorId} =
                await EventExhibitorService.getApplicationData(tr, eventId, exhibitorId);

            if (applicationStatus !== EventExhibitorService.APPLICATION_STATUS.APPROVED) {
                throw { validation: `Operation Denied: Application status is not "Approved"` };
            }

            const {purchaseId, purchaseBooths} = await this.createPurchase(tr, eventId, exhibitorId, userAccessIds, exhibitorInvoiceData);

            exhibitorInvoiceData.booths = this.preparePurchaseBoothsData(purchaseBooths);

            await this.createEventExhibitorInvoice(tr, eventExhibitorId, purchaseId, exhibitorInvoiceData);

            await tr.commit();
        } catch (err) {
            await rollbackTransaction(tr);

            throw err;
        }
    }

    /**
     * This method creates a purchase for the exhibitor invoice
     *
     * @param tr
     * @param eventId
     * @param exhibitorId
     * @param userAccessIds
     * @param invoiceData
     *
     * @returns {Promise<Object>} - purchase data
     */
    createPurchase(tr, eventId, exhibitorId, userAccessIds, invoiceData) {
        const purchaseData = preparePurchaseData(eventId, exhibitorId, invoiceData);

        return BoothsService.payment.pay(
            purchaseData,
            PAYMENT_TYPE.PENDING_PAYMENT,
            userAccessIds,
            tr
        );
    }

    /**
     * This method prepares the purchase booths data
     *
     * @param purchaseBooths
     * @returns {Array} - booths data
     */
    preparePurchaseBoothsData(purchaseBooths) {
        const booths = separateOtherBoothsByBoothQuantity(purchaseBooths);

        return booths.map(({event_booth_id, purchase_booth_id}) => event_booth_id || purchase_booth_id);
    }

    /**
     * This method creates an event exhibitor invoice
     *
     * @param tr
     * @param eventExhibitorId
     * @param purchaseId
     * @param invoiceData
     *
     * @returns {Promise<Object>} - event exhibitor invoice data
     */
    createEventExhibitorInvoice(tr, eventExhibitorId, purchaseId, invoiceData) {
        const eventExhibitorInvoiceData = {
            event_exhibitor_id: eventExhibitorId,
            purchase_id: purchaseId,
            event_dates: invoiceData.event_dates,
            booths: invoiceData.booths,
            comment: invoiceData.comment
        };

        return createEventExhibitorInvoiceRow(tr, eventExhibitorInvoiceData);
    }

    /**
     * This method updates an exhibitor invoice
     *
     * @param eventId
     * @param exhibitorId
     * @param exhibitorInvoiceId
     * @param exhibitorInvoiceData
     *
     * @returns {Promise<void>}
     */
    async updateExhibitorInvoice(eventId, exhibitorId, exhibitorInvoiceId, exhibitorInvoiceData) {
        let tr;

        try {
            tr = await Db.begin();

            const {purchase_id: purchaseId, ...oldExhibitorInvoiceBoothsData} =
                await getExhibitorInvoiceBoothsData(tr, exhibitorInvoiceId);

            const eventBoothsChanged = isEventBoothsChanged(oldExhibitorInvoiceBoothsData, exhibitorInvoiceData);

            if(eventBoothsChanged) {
                const purchaseData = await this.updatePurchaseData(tr, eventId, exhibitorId, purchaseId, exhibitorInvoiceData);

                exhibitorInvoiceData.booths = this.preparePurchaseBoothsData(purchaseData);
            }

            await this.updateEventExhibitorInvoice(
                tr, eventId, exhibitorId, exhibitorInvoiceId, exhibitorInvoiceData
            );

            await tr.commit();
        } catch (err) {
            await rollbackTransaction(tr);

            throw err;
        }
    }

    async deleteExhibitorInvoice(eventId, exhibitorId, exhibitorInvoiceId) {
        let tr;

        try {
            tr = await Db.begin();

            const {
                purchase_id: purchaseId
            } = await deleteEventExhibitorInvoiceRow(eventId, exhibitorId, exhibitorInvoiceId, tr);

            await cancelPurchase(eventId, exhibitorId, purchaseId, tr)

            await tr.commit();
        } catch (err) {
            await rollbackTransaction(tr);

            throw err;
        }
    }

    async updatePurchaseData(tr, eventId, exhibitorId, purchaseId, exhibitorInvoiceData) {
        const purchaseData = preparePurchaseData(eventId, exhibitorId, exhibitorInvoiceData);

        await validatePurchaseAmount(purchaseData);

        return BoothsService.payment.updatePurchaseData(purchaseId, purchaseData, tr);
    }

    updateEventExhibitorInvoice(tr, eventId, exhibitorId, exhibitorInvoiceId, exhibitorInvoiceData) {
        const eventExhibitorInvoiceData = {
            booths: exhibitorInvoiceData.booths,
            comment: exhibitorInvoiceData.comment,
            event_dates: exhibitorInvoiceData.event_dates
        };

        return updateEventExhibitorInvoiceRow(
            tr, eventId, exhibitorId, exhibitorInvoiceId, eventExhibitorInvoiceData
        );
    }
}

async function getExhibitorInvoiceDetails(eventId, exhibitorId, exhibitorInvoiceId) {
    const query = knex
        .select(
            'p.purchase_id',
            'p.status AS purchase_status',
            'p.type AS purchase_type',
            'p.check_num',
            'p.date_paid',
            'ex.status AS application_status',
            {
                purchase_booths: knex.raw(`(
                    SELECT COALESCE(array_to_json(array_agg(b)), '[]')
                    FROM(
                        SELECT
                            FORMAT('Custom Booth, $%s', pb.fee) AS label,
                            pb.purchase_booth_id as id,
                            pb.fee AS amount,
                            pb.title,
                            pb.description,
                            pb.quantity,
                            pb.fee,
                            pb.event_booth_id,
                            pb.notes,
                            pb.booth_label
                        FROM purchase_booth AS pb
                        WHERE pb.purchase_id = exi.purchase_id
                            AND pb.event_booth_id IS NULL
                    ) as b
                )`)
            },
            {
                comment: knex.raw(`COALESCE(exi.comment, '')`)
            },
            {
                event_dates: knex.raw(`(
                    SELECT json_object_agg(d.date, false)
                    FROM (
                        SELECT generate_series(e.date_start, e.date_end, '1 day'::interval) as date
                    ) as d
                )`)
            },
            {
                chosen_event_dates: knex.raw(`COALESCE(exi.event_dates, '{}'::JSONB)`)
            },
            {
                event_booths: knex.raw(`(
                    SELECT COALESCE(ARRAY_TO_JSON(ARRAY_AGG(ROW_TO_JSON(b))), '[]')
                    FROM (
                        SELECT 
                            FORMAT('%s, $%s', eb.title, eb.fee) as label, 
                            eb.event_booth_id AS id, 
                            eb.fee AS amount,
                            eb.title,
                            eb.description
                        FROM event_booth AS eb
                        WHERE eb.event_id = e.event_id
                            AND eb.is_enabled IS TRUE
                    ) AS b
                )`)
            },
            {
                chosen_event_booths: knex.raw(`(
                    SELECT COALESCE(array_to_json(array_agg(b)), '[]')
                    FROM(
                        SELECT
                            FORMAT('%s, $%s', eb.title, eb.fee) AS label,
                            bth.id,
                            eb.fee AS amount,
                            eb.title,
                            eb.description
                        FROM (
                            SELECT unnest(exi.booths) AS id
                        ) as bth
                        JOIN event_booth AS eb ON eb.event_booth_id = bth.id
                    ) as b
                )`)
            },
            {
                publishable_key: knex.raw(`(
                    CASE 
                        WHEN (e.teams_use_connect IS TRUE AND sa.stripe_account_id IS NOT NULL)
                        THEN (
                            SELECT "value"->>'public_key' 
                            FROM settings 
                            WHERE "key" = ?
                        )
                        ELSE sa.public_key
                    END
                )`, [settingsRow])
            }
        )
        .from('event_exhibitor_invoice AS exi')
        .join('purchase AS p', (join) => {
            join.on('p.purchase_id', 'exi.purchase_id')
        })
        .join('event_exhibitor AS ex', 'ex.event_exhibitor_id', 'exi.event_exhibitor_id')
        .join('event as e', 'e.event_id', 'ex.event_id')
        .leftJoin('stripe_account AS sa', 'sa.secret_key', 'e.stripe_exhibitors_private_key')
        .where('e.event_id', eventId)
        .where('ex.sponsor_id', exhibitorId)
        .where('exi.event_exhibitor_invoice_id', exhibitorInvoiceId)
        .whereNull('exi.deleted_at');

    const {rows: [exhibitorInvoiceDetails]} = await Db.query(query);

    if (_.isEmpty(exhibitorInvoiceDetails)) {
        throw { validation: 'Payment not found' };
    }

    return exhibitorInvoiceDetails;
}

function formatExhibitorInvoiceDetails(exhibitorInvoiceDetails) {
    if (_.isEmpty(exhibitorInvoiceDetails)) {
        return {};
    }

    exhibitorInvoiceDetails.event_dates = Object.assign({}, exhibitorInvoiceDetails.event_dates, exhibitorInvoiceDetails.chosen_event_dates);
    exhibitorInvoiceDetails.event_booths.unshift(OTHER_BOOTH);

    if(!_.isEmpty(exhibitorInvoiceDetails.purchase_booths)) {
        exhibitorInvoiceDetails.purchase_booths = separateOtherBoothsByBoothQuantity(exhibitorInvoiceDetails.purchase_booths);
        exhibitorInvoiceDetails.chosen_event_booths = exhibitorInvoiceDetails.chosen_event_booths.concat(exhibitorInvoiceDetails.purchase_booths);
    }

    return _.omit(exhibitorInvoiceDetails, ['chosen_event_dates', 'purchase_booths']);
}

function separateOtherBoothsByBoothQuantity(booths) {
    const _booths = [].concat(booths);

    const boothsWithQuantityOne = _booths.filter(filterByQuantityEqualOne);

    _booths.forEach(booth => {
        if (booth.quantity > 1) {
            for (let i = 0; i < booth.quantity; i++) {
                boothsWithQuantityOne.push(Object.assign({}, booth, { quantity: 1 }));
            }
        }
    });

    return boothsWithQuantityOne;
}

function filterByQuantityEqualOne(booth) {
    return booth.quantity === 1;
}

function preparePurchaseData(eventId, exhibitorId, exhibitorInvoiceData) {
    return {
        amount: exhibitorInvoiceData.amount,
        total: exhibitorInvoiceData.amount,
        sponsor_id: exhibitorId,
        event_id: eventId,
        booth: exhibitorInvoiceData.otherBooths,
    };
}

async function createEventExhibitorInvoiceRow(tr, eventExhibitorInvoiceData) {
    const query = knex('event_exhibitor_invoice')
        .insert(eventExhibitorInvoiceData);

    const { rowCount, rows: [eventExhibitorInvoice] } = await tr.query(query);

    if (rowCount !== 1) {
        throw { validation: 'Failed to create exhibitor invoice' };
    }

    return eventExhibitorInvoice;
}

async function rollbackTransaction(tr) {
    if (tr && !tr.isCommited) {
        await tr.rollback();
    }
}

async function validatePurchaseAmount(purchaseData) {
    const eventSettings = await BoothsService.paymentUtils.getEventStripeSettings(purchaseData.event_id);

    if(_.isEmpty(eventSettings)) {
        throw { validation: 'Event Stripe settings not found' };
    }

    BoothsService.paymentUtils.recountTotals(eventSettings, purchaseData);
}

async function getExhibitorInvoiceBoothsData(tr, exhibitorInvoiceId) {
    const query = knex
        .select(
            'exi.purchase_id',
            'exi.booths',
            {
                purchase_booths: knex.raw(`(
                    array_to_json(
                        array_agg(
                            json_build_object(
                                'quantity',       pb.quantity,
                                'event_booth_id', pb.event_booth_id,
                                'fee',            pb.fee,
                                'amount',         pb.quantity * pb.fee,
                                'title',          pb.title,
                                'description',    pb.description,
                                'booth_label',    pb.booth_label,
                                'notes',          pb.notes
                            )
                        )
                    )
                )`)
            },
        )
        .from('event_exhibitor_invoice AS exi')
        .join('purchase AS p', (join) => {
            join.on('p.purchase_id', 'exi.purchase_id')
        })
        .join('purchase_booth AS pb', 'pb.purchase_id', 'p.purchase_id')
        .where('exi.event_exhibitor_invoice_id', exhibitorInvoiceId)
        .whereNull('exi.deleted_at')
        .groupBy('exi.booths', 'exi.purchase_id');

    const {rows: [exhibitorInvoiceData = {}]} = await tr.query(query);

    if (_.isEmpty(exhibitorInvoiceData)) {
        throw { validation: 'Payment cannot be updated. Please try again later.' };
    }

    return exhibitorInvoiceData;
}

function isEventBoothsChanged(oldExhibitorInvoiceBoothsData, exhibitorInvoiceData) {
    const {booths, otherBooths} = exhibitorInvoiceData;

    const areEventBoothsChanged = !_.isEqual(
        _.sortBy(oldExhibitorInvoiceBoothsData.booths),
        _.sortBy(booths)
    );

    const areOtherBoothsChanged = !_.isEqual(
        _.sortBy(oldExhibitorInvoiceBoothsData.purchase_booths, ['title', 'fee']),
        _.sortBy(otherBooths, ['title', 'fee'])
    );

    return areEventBoothsChanged || areOtherBoothsChanged;
}

async function updateEventExhibitorInvoiceRow(tr, eventId, exhibitorId, exhibitorInvoiceId, eventExhibitorInvoiceData) {
    const query = knex('event_exhibitor_invoice as eei')
        .update(eventExhibitorInvoiceData)
        .where(function() {
            this.whereExists(function() {
                this.select(1)
                    .from('event_exhibitor as ee')
                    .whereRaw('ee.event_exhibitor_id = eei.event_exhibitor_id')
                    .where(`ee.status`, '<>', EventExhibitorService.APPLICATION_STATUS.DECLINED)
                    .where('ee.event_id', eventId)
                    .where('ee.sponsor_id', exhibitorId);
            });
        })
        .whereExists(function() {
            this.select(1)
                .from('purchase as p')
                .whereRaw('p.purchase_id = eei.purchase_id')
                .where('p.status', PAYMENT_STATUS.PENDING)
                .where('p.type', PAYMENT_TYPE.PENDING_PAYMENT);
        })
        .where('eei.event_exhibitor_invoice_id', exhibitorInvoiceId)
        .returning('eei.purchase_id');

    const {rows: [eventExhibitorInvoice]} = await tr.query(query);

    if(_.isEmpty(eventExhibitorInvoice)) {
        throw { validation: 'Payment cannot be updated. Please try again later.' };
    }

    return eventExhibitorInvoice;
}

async function deleteEventExhibitorInvoiceRow(eventId, exhibitorId, exhibitorInvoiceId, tr) {
    const query = knex('event_exhibitor_invoice as exi')
        .update({
            deleted_at: knex.fn.now()
        })
        .where(function() {
            this.whereExists(function() {
                this.select(1)
                    .from('event_exhibitor as ex')
                    .whereRaw('ex.event_exhibitor_id = exi.event_exhibitor_id')
                    .where('ex.event_id', eventId)
                    .where('ex.sponsor_id', exhibitorId);
            });
        })
        .where('exi.event_exhibitor_invoice_id', exhibitorInvoiceId)
        .whereNull('exi.deleted_at')
        .returning('exi.purchase_id');

    const {rows: [eventExhibitorInvoice]} = await tr.query(query);

    if(_.isEmpty(eventExhibitorInvoice)) {
        throw { validation: 'Payment cannot be deleted. Please try again later.' };
    }

    return eventExhibitorInvoice;
}

async function cancelPurchase(eventId, exhibitorId, purchaseId, tr) {
    const query = knex('purchase')
        .update({
            status: PAYMENT_STATUS.CANCELED
        })
        .where('status', PAYMENT_STATUS.PENDING)
        .where('type', PAYMENT_TYPE.PENDING_PAYMENT)
        .where('purchase_id', purchaseId)
        .where('event_id', eventId)
        .where('sponsor_id', exhibitorId)
        .returning('purchase_id');

    const {rows: [purchase]} = await tr.query(query);

    if(_.isEmpty(purchase)) {
        throw { validation: 'Payment cannot be deleted. Please try again later.' };
    }

    return purchase;
}

module.exports = new EventExhibitorInvoiceService();
