'use strict';

var ejs             = require('ejs'),
    path            = require('path'),
    fetch           = require('node-fetch');

const BASE_URL = sails.config.urls.home_page.baseUrl;

const Queue = require('bull');
const Deferred = require('../lib/deferred');
const emailQueue = new Queue('send-email', sails.config.redis_queue.send_email);
const UnsubscribeTokenService = require('./email-service/_UnsubscribeToken');
const EmailCategoryService = require('./email-service/_EmailCategory');
const RabbitMQClient = require('./RabbitMQClient');
const { PRIORITY } = require('../constants/emails');

var TMPL_STR =
    '<!DOCTYPE html><html lang="en"><head><meta charset="UTF-8"><title>{1}</title>' +
    '</head><body>{0}</body></html>';

const
    viewsPath                       = path.resolve(__dirname, '..', '..', 'views'),
    LEAVE_ONE_BREAK_REG_EXP         = /(\s)(?:\s)/gm,
    REMOVE_BREAKS_REG_EXP           = /\s\s+/g,

    // https://sendgrid.com/docs/API_Reference/Web_API_v3/Mail/index.html#-Limitations
    REMOVE_FORBIDDEN_CHARS_EXP                    = /,|;/g,
    FIND_MULTIPLE_FORMATTED_RECEIVERS_REGEXP      = />\s/g,
    ADD_MULTIPLE__FORMATTED_RECEIVERS_STRING      = '>, ',
    FIND_MULTIPLE_NOT_FORMATTED_RECEIVERS_REGEXP  = /\s/g,
    ADD_MULTIPLE__NOT_FORMATTED_RECEIVERS_STRING  = ', ',
    FORMATTED_RECEIVER_REGEXP                     = /\s</;

function prepareCC (cc) {
    return (!cc || (cc.indexOf('undefined') >= 0))?'':cc;
}

const EmailService = {
    unsubscribeToken: UnsubscribeTokenService,
    emailCategory: EmailCategoryService,

    beginEmailTransaction(commonFields, options = {}) {
        return {
            _email: commonFields,
            _options: options,
            _personalizations: [],
            isCommited: false,
            isRollbacked: false,

            sendEmail(email) {
                const result = new Deferred();
                this._personalizations.push(
                    {
                        email,
                        result,
                    }
                );

                return result.promise;
            },

            async commit() {
                try {
                    if(this.isCommited) {
                        throw new Error('Transaction is already committed')
                    }
                    if(this.isRollbacked) {
                        throw new Error('Transaction is already rollbacked')
                    }
                    const jobData = {
                        ...this._email,
                        personalizations: this._personalizations.map(({ email }) => email),
                    };
                    const result = await EmailService.sendEmail(jobData, this._options);
                    for(const [index, personalization] of Object.entries(result.personalizations)) {
                        this._personalizations[index].result.resolve({
                            email_id: personalization.metadata.email_id,
                            job_id: result.job_id
                        });
                    }
                    this._personalizations = [];
                    this.isCommited = true;
                }
                catch(err) {
                    this._personalizations.forEach(({result}) => result.reject(err));
                    this.isRollbacked = true;
                }
            },

            async rollback() {
                if(this.isCommited) {
                    throw new Error('Transaction is already committed')
                }
                if(!this.isRollbacked) {
                    this._personalizations.forEach(({ result }) => result.reject(new Error('Transaction rollbacked')));
                    this.isRollbacked = true;
                }
            }
        };
    },

    endQueueClient() {
        return RabbitMQClient.close()
    },

    async _formatEmailObject(email, { wrap = false, unsubscribeLinkMetadata = null }) {
        email = {...email};
        const RECIPIENT_FIELDS = ['to', 'cc', 'bcc', 'copy', 'variables'];

        if(!email.templateId) {
            if(!( email.html || email.text )) {
                throw new Error('Cannot send an empty letter. At least body text should be provided');
            }
            if(!email.subject) {
                throw new Error('Subject required');
            }
        }

        if (!email.from) {
            throw new Error('Sender required');
        }

        if(email.replyto) {
            email.replyTo = email.replyto;

            delete email.replyto;
        }

        if(!email.personalizations) {
            email.personalizations = [
                _.pick(email, RECIPIENT_FIELDS)
            ];
            for(const field of RECIPIENT_FIELDS) {
                delete email[field];
            }
        }
        const emailIds = await this._getEmailIds(email.personalizations.length);
        const unsubscribeTokens = unsubscribeLinkMetadata === null
            ? null
            : await this.unsubscribeToken.batchCreate(
                email.personalizations.map(p => p.to),
                unsubscribeLinkMetadata
            );
        email.personalizations = email.personalizations.map(
            (p, index) => this._formatPersonalizationObject(p, emailIds[index], unsubscribeTokens?.[index])
        );

        if (email.html && wrap) {
            email.html = TMPL_STR.format(email.html, email.subject);
        } else if (!email.html && email.text) {
            email.html = TMPL_STR.format(
                email.text.replace(/(?:\r\n|\r|\n)/g, '<br/>'),
                email.subject
            );
        }

        return email;
    },

    async _getEmailIds(count) {
        const { rows } = await Db.query(
            `SELECT nextval('sent_email_id_seq'::regclass) "id"
            FROM generate_series(1, $1);`, [count]
        );

        return rows.map(row => Number(row.id));
    },

    _formatPersonalizationObject(personalization, email_id, unsubscribeToken) {
        personalization = {...personalization};
        if (!personalization.to) {
            throw new Error('Destination required');
        }

        //NOTE: need to remove "," and ";" from "to" param.
        //https://sendgrid.com/docs/API_Reference/Web_API_v3/Mail/index.html#-Limitations
        personalization.to = removeForbiddenCharsFromReceiverField(personalization.to);

        if(personalization.cc) {
            personalization.cc = removeForbiddenCharsFromReceiverField(personalization.cc);
        }

        if(personalization.bcc) {
            personalization.bcc = removeForbiddenCharsFromReceiverField(personalization.bcc);
        }

        if(personalization.cc || personalization.bcc) {
            personalization.copy = _.pickBy(personalization, (v, k) => ['cc', 'bcc'].includes(k) && v);
        }
        delete personalization.cc;
        delete personalization.bcc;

        personalization.metadata = {
            ...(personalization.metadata || {}),
            email_id,
        };

        if(unsubscribeToken) {
            personalization.unsubscribeLink = `${BASE_URL}/unsubscribe/${unsubscribeToken}`;
        }

        return personalization;
    },

    async __sendViaRabbitMQ__(email, options) {
        await RabbitMQClient.publish(
            sails.config.rabbitmq.emailExchange,
            this.__mapPriorityToRoutingKey(options.priority),
            email,
        );

        return email;
    },

    // map transactional marketing email to low priority queue
    __mapPriorityToRoutingKey(priority) {
        switch (priority) {
            case PRIORITY.MARKETING:
                return sails.config.rabbitmq.emailRoutingKey;
            default:
                return sails.config.rabbitmq.highPriorityEmailRoutingKey
        }
    },
    // Test
    async __sendViaBull__(email, queueOptions = {}) {
        let { id: jobID } = await emailQueue.add(email, queueOptions);
        email.job_id = jobID;
        return email;
    },

    // covered 😄👍
    async sendEmail(email, options = {}) {
        const formattedEmail = await this._formatEmailObject(email, options);
        const queueOptions = this._getQueueOptions(options);

        return this.__sendViaRabbitMQ__(formattedEmail, queueOptions);
    },

    async renderAndSend(emailParams, options = {}) {
        let emailData = _.omit(emailParams, 'data', 'template');

        try {
            let [html, text]  = await __render(emailParams.template, emailParams.layout, emailParams.data);

            if (!(html || text)) {
                throw new Error('Renderer returned nothing');
            }

            if (html) {
                emailData.html = html;
            }

            if (text) {
                emailData.text = text;
            }

            const { personalizations: [personalization] } = await this.sendEmail(emailData, options);

            return {
                ...emailData,
                email_id: personalization?.metadata?.email_id || null
            };
        } catch (err) {
            loggers.errors_log.error(err);

            throw err;
        }
    },

    async getEmail(emailId) {
        const params = {
            email_ids: [emailId], 
        }

        const headers = {
            fields: ['html', 'subject']
        }

        const [email] = await this._makeRequest('/email', { method: 'GET', params, headers});

        return email || null;
    },

    async getEmailEvents(emailId) {
        const params = {
            email_id: emailId, 
        }

        const emailEvents = await this._makeRequest('/email/events', { method: 'GET', params });

        return emailEvents;
    },

    async _makeRequest(url, {headers = {}, params = {}, method} = {}) {
        if(!method) {
            throw new Error('Method is not passed');
        }

        const headersOptions = new fetch.Headers({
            'x-api-key':  sails.config.emailService.apiKey,
            'Content-Type': 'application/json',
            ...headers
        });

        const options = { method, headers: headersOptions };
        
        const urlObject = new URL(`${sails.config.emailService.baseUrl}${url}`);

        _.forEach(params, (value, key)=>urlObject.searchParams.append(key, value));

        const response = await fetch(urlObject.toString(), options);

        if(!response.ok) {
            loggers.errors_log.error(`Error making request to Email Service`, {
                response: {
                    ..._.pick(response, 'url', 'status', 'statusText'),
                },
                body: options.body,
            });
            throw Error('Email Service unavailable');
        }

        return response.json()
    },

    getQueue() {
        // !! no queue instance for rabbit
        return null;
    },

    _getQueueOptions(options) {
        const queueOptionFields = ['priority'];
        return _.pick(options, queueOptionFields);
    },

    MULTIPLE__NOT_FORMATTED_RECEIVERS_STRING: ADD_MULTIPLE__NOT_FORMATTED_RECEIVERS_STRING
};

function __render (template, layout, content) {
    let tmplPath = path.resolve(viewsPath, template)

    return Promise.all([
        ejsRender(tmplPath + '.ejs', content),
        ejsRender(tmplPath + '.txt.ejs', content)
    ]).then(rendered => {
        if (layout) {
            let layoutPath = path.resolve(viewsPath, layout);

            return Promise.all([
                ejsRender(layoutPath + '.ejs', _.defaults({ body: rendered[0] }, content)),
                ejsRender(layoutPath + '.txt.ejs', _.defaults({ body: rendered[1] }, content))
            ]);
        } else {
            return rendered;
        }
    }).then(rendered => {
        rendered[0] = rendered[0].replace(REMOVE_BREAKS_REG_EXP, ' ');
        rendered[1] = rendered[1].replace(LEAVE_ONE_BREAK_REG_EXP, '$1');

        return rendered;
    });
}

function ejsRender (path, content) {
    return new Promise((resolve, reject) => {
        ejs.renderFile(path, content, (err, result) => {
            if(err) {
                reject(err);
            } else {
                resolve(result);
            }
        });
    });
}

function removeForbiddenCharsFromReceiverField (receiverField) {
    receiverField = receiverField.replace(REMOVE_FORBIDDEN_CHARS_EXP, '');

    let isFormattedReceiver    = FIND_MULTIPLE_FORMATTED_RECEIVERS_REGEXP.test(receiverField);
    let isNotFormattedReceiver = FIND_MULTIPLE_NOT_FORMATTED_RECEIVERS_REGEXP.test(receiverField) &&
                                    !FORMATTED_RECEIVER_REGEXP.test(receiverField);

    if(isFormattedReceiver) {
        receiverField = receiverField.replace(
            FIND_MULTIPLE_FORMATTED_RECEIVERS_REGEXP, ADD_MULTIPLE__FORMATTED_RECEIVERS_STRING
        );
    } else if(isNotFormattedReceiver){
        receiverField = receiverField.replace(
                FIND_MULTIPLE_NOT_FORMATTED_RECEIVERS_REGEXP, ADD_MULTIPLE__NOT_FORMATTED_RECEIVERS_STRING
        );
    }

    return receiverField;
}

module.exports = EmailService;
