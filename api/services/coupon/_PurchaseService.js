class PurchaseService {

    get MAX_QUANTITY_ALLOWED_TO_BUY_IN_ONE_PURCHASE () {
        return 1;
    }

    async getCouponsTickets (eventID, ticketCoupons) {
        let tickets = await this.getAllowedTicketsToBuy(eventID, ticketCoupons);

        if(_.isEmpty(tickets)) {
            return tickets;
        }

        this.__validateTicketsExpirationDate(tickets);

        tickets = tickets.map(ticket => _.omit(ticket, ['event_end']));

        return tickets;
    }

    async getAllowedTicketsToBuy(eventID, ticketCoupons) {
        let formattedCodes = ticketCoupons.map(coupon => coupon && coupon.trim().toLowerCase());

        let query = knex('ticket_coupon AS tc')
            .select({
                max_quantity: 'tc.quantity',
                bought_qty: knex.raw(`COUNT(pt.*)::INTEGER`),
                event_ticket_id: 'et.event_ticket_id',
                active: 'tc.active',
                event_email: 'e.email',
                team_name: 'rt.team_name',
                code: knex.raw(`LOWER(tc.code)`),
                valid_dates: knex.raw(`(
                    SELECT ARRAY_AGG(TO_CHAR(TO_TIMESTAMP(vd, 'YYYY-MM-DD'), 'YYYY Dy, Mon DD'))
                    FROM JSONB_OBJECT_KEYS(et.valid_dates) vd
                )`),
                ticket_coupon_id: 'tc.ticket_coupon_id',
                event_end: knex.raw(`e.date_end AT TIME ZONE e.timezone`),
                timezone: 'e.timezone'
            })
            .leftJoin('purchase_ticket AS pt', function () {
                this.on('pt.ticket_coupon_id', 'tc.ticket_coupon_id')
                    .andOnNull('pt.canceled')
            })
            .join('event_ticket AS et', 'et.event_ticket_id', 'tc.event_ticket_id')
            .join('event AS e', 'e.event_id', 'et.event_id')
            .join('ticket_coupon_receiver AS tcr', 'tcr.ticket_coupon_id', 'tc.ticket_coupon_id')
            .leftJoin('roster_team AS rt', function () {
                return this.on('rt.roster_team_id', 'tcr.roster_team_id')
                    .andOn('rt.event_id', 'e.event_id')
            })
            .whereRaw(`TRIM(LOWER(tc.code)) = ANY (?)`, [formattedCodes])
            .where('e.event_id', eventID)
            .whereRaw(`COALESCE((e.tickets_settings ->> 'require_coupon')::BOOLEAN, false) IS TRUE`)
            .groupBy('tc.ticket_coupon_id', 'rt.roster_team_id', 'et.valid_dates', 'et.event_ticket_id', 'e.event_id');



        let counts = await Db.query(query).then(({rows}) => rows);

        if(_.isEmpty(counts)) {
            throw { validation: `Coupons not found` };
        }

        if(formattedCodes.length !== counts.length) {
            throw { validation: `Some coupons not found` };
        }

        counts = counts.map(couponCounts => this.__getCouponCounts(couponCounts));

        return counts;
    }

    __validateTicketsExpirationDate (tickets) {
        let notExpiredTickets = TicketsService.filterExpiredTickets(tickets[0].timezone, tickets);

        if(notExpiredTickets.length !== tickets.length) {
            let notExpiredIDs = notExpiredTickets.map(t => t.code);
            let expiredCoupons = tickets.reduce((all, ticket) => {
                if(!notExpiredIDs.includes(ticket.code)) {
                    all.push(ticket.code.toUpperCase());
                }
                return all;
            }, []);

            throw { validation: `Coupon(s) ${expiredCoupons.join(', ')} valid dates passed` };
        }
    }

    __getCouponCounts (coupon) {
        if(_.isEmpty(coupon)) {
            throw { validation: `Some coupons not found` };
        }

        if(!coupon.active) {
            throw {
                validation: `${
                    coupon.code
                } coupon code has been deactivated by the Tournament Director. Please email “${
                    coupon.event_email
                }” for further assistance`
            };
        }
        delete coupon.active;
        delete coupon.event_email;

        const intFields = ['max_quantity', 'bought_qty'];

        for(const key of intFields) {
            coupon[key] = Number(coupon[key]);
        }

        const availableCouponTickets = coupon.max_quantity - coupon.bought_qty;
        const maxTicketsInPurchase = Math.min(this.MAX_QUANTITY_ALLOWED_TO_BUY_IN_ONE_PURCHASE, availableCouponTickets);
        if(maxTicketsInPurchase <= 0) {
            throw { validation: `All tickets for coupon ${coupon.code} already bought` };
        }
        coupon.max_tickets_in_purchase = maxTicketsInPurchase;

        // Front-end is calculating maxTicketsInPurchase as quantity - bought_qty
        // Sending fake value of quantity so that quantity - bought_qty = maxTicketsInPurchase
        coupon.quantity = maxTicketsInPurchase + coupon.bought_qty;

        return coupon;
    }

    async getBoughtTicketsList(eventID, code) {
        let query = knex('ticket_coupon AS tc')
            .select({
                holder_name: knex.raw(`(p.first || ' ' || p.last)`),
                barcode: 'p.ticket_barcode',
                date_paid:
                    knex.raw(`TO_CHAR((p.created::TIMESTAMPTZ AT TIME ZONE e.timezone), 'Mon DD, YYYY HH12:MI AM')`),
                status: 'p.status'
            })
            .join('purchase_ticket AS pt', 'pt.ticket_coupon_id', 'tc.ticket_coupon_id')
            .join('purchase AS p', 'p.purchase_id', 'pt.purchase_id')
            .join('event AS e', 'e.event_id', 'p.event_id')
            .where('p.event_id', eventID)
            .whereRaw(`TRIM(LOWER(tc.code)) = TRIM(LOWER(?))`, [code]);

        let tickets = await Db.query(query).then(result => result && result.rows);

        if(!tickets || !tickets.length) {
            throw { validation: `Coupon ${code} not found` };
        }

        return tickets;
    }
}

module.exports = new PurchaseService();
