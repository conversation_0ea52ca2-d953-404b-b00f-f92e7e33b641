'use strict';

const 
    stripeLib                    = require('stripe'),
    swUtils                      = require('../lib/swUtils'),
    _AccountService              = require('./stripe/_AccountService'),
    StripeConnect                = require('../lib/StripeConnect'),
    _WebhookService              = require('./stripe/_WebhookService'),
    _PayoutsService              = require('./stripe/_PayoutsService'),
    _NotificationsService        = require('./stripe/_NotificationsService'),
    _PaymentCardService          = require('./stripe/_PaymentCardService'),
    _ChargeUpdateService         = require('./stripe/_ChargeUpdateService'),
    _FileService                 = require('./stripe/_FileService'),
    _DisputeEvidenceService      = require('./stripe/_DisputeEvidenceService');


const
    MAX_FIELD_LENGH             = 499,
    DEFAULT_ACH_PERCENT         = 0.008, // 0.8%
    DEFAULT_ACH_FEE_CAP         = 5, // 5$ maximum for ACH Stripe Fee
    DEFAULT_STRIPE_PERCENT      = 0.029,
    DEFAULT_STRIPE_FIXED        = 0.3,
    DISPUTE_FEE                 = 15, // All disputed payments are charged a $15 fee by the bank to process each dispute
    FAILED_ACH_PAYMENT_FEE      = 4,
    DESC_FORBIDDEN_LETTERS      = _.escapeRegExp(`<>'"\\*`),
    NUMBER_REG_EXP              = '^[0-9]+$',
    DESC_ALLOWED_LETTERS        = '^[a-zA-Z0-9()_\\-!#$%^&,.|\\][]+$',
    STRIPE_PERCENT_MIN_DEFAULT  = 0.026,
    STRIPE_PERCENT_MIN_BEFORE_2021_01_01 = 0.027,
    STRIPE_PERCENT_CHANGE_DATE_2020_12_31 = '2020-12-31',
    DISPUTE_FEE_COLLECTION_CHANGE_DATE = '2023-06-01',
    DISPUTE_FEE_COLLECTION_EXCLUDE_WON_DATE = '2024-05-17';

const DISPUTE_STATUS = {
    WARNING_NEEDS_RESPONSE: 'warning_needs_response',
    WARNING_UNDER_REVIEW: 'warning_under_review',
    WARNING_CLOSED: 'warning_closed',
    NEEDS_RESPONSE: 'needs_response',
    UNDER_REVIEW: 'under_review',
    CHARGE_REFUNDED: 'charge_refunded',
    WON: 'won',
    LOST: 'lost',
    PENDING: 'pending',
};

Object.freeze(DISPUTE_STATUS)

let stripeService = {
    pay: function (data, cb) {
        let onError = err => {
            if (err && err.type === 'StripeCardError' && err.rawType === 'card_error'){
                const validationError = {validation: err.message};
                if(cb) cb(validationError);
                else return Promise.reject(validationError)
            } else if(cb) {
                cb({ text: err.message });
            } else {
                return Promise.reject(err);
            }
        }

        var $private_key    = data.private_key,
            $amount         = data.amount,
            $token          = data.token,
            $descr          = data.descr,
            $statement      = this.filterForbiddenLettersStatementDescription(data.statement),
            $api_version    = (data.api_version || sails.config.stripe_api.version),
            $metadata       = data.metadata,
            stripe          = this.initStripe($private_key, $api_version),
            amountInCents;

        if(!stripe) {
            return onError('Invalid Stripe Secret Key passed');
        }

        amountInCents = swUtils.normalizeNumber($amount * 100);

        loggers.debug_log.verbose($amount, amountInCents)

        return stripe.charges.create({
            amount                  : amountInCents,
            currency                : 'usd',
            source                  : $token,
            description             : $descr,
            statement_descriptor    : ($statement || undefined),
            receipt_email           : (data.receipt_email || undefined),
            metadata                : _.isEmpty($metadata)
                                        ?undefined
                                        :swUtils.cropStringProps($metadata, MAX_FIELD_LENGH)
        }).then(function (charge) {
            if(cb) {
                cb(null, charge);
            } else {
                return charge;
            }
        }, function (err) {
            loggers.errors_log.error('Stripe Payment Error occured:', err);

            return onError(err);
        });
    },
    refund: function (data, cb) {
        let validationErrorHandler = err => {
            if(cb) {
                cb({ text: err });
            } else {
                return Promise.reject(new Error(err));
            }
        }

        if(_.isEmpty(data)) {
            return validationErrorHandler('Empty data passed');
        }
        if(!data.charge_id) {
            return validationErrorHandler('Invalid charge identifier passed');
        }
        if(!data.stripe_secret) {
            return validationErrorHandler('Invalid stripe secret key passed');
        }

        let stripe = this.initStripe(data.stripe_secret, sails.config.stripe_api.version),
            params = (data.amount)?({ amount: (data.amount * 100) }):{};

        if(!stripe) {
            return validationErrorHandler('Stripe not iniitialized: Invalid stripe secret key')
        }

        params.charge = data.charge_id;

        return stripe.refunds.create(params)
        .then(refund => {
            if(cb) {
                cb(null, refund);
            } else {
                return refund;
            }
        }).catch(validationErrorHandler);
    },
    /*
    * covered 😄👍
    *
    * This formula is explained here:
    * https://support.stripe.com/questions/can-i-charge-my-stripe-fees-to-my-customers
    */
    customerStripeFee: function (amount, feePercent, fixedTax) {
        if(amount < 0) {
            throw new Error('Amount should be greater than 0');
        }
        if(!_.isNumber(amount)) {
            throw new Error('Amount should be a number');
        }
        if(amount === 0) {
            return 0;
        }

        let __percent = (parseFloat(feePercent, 10) || DEFAULT_STRIPE_PERCENT);
        let __fixed = (parseFloat(fixedTax, 10) || DEFAULT_STRIPE_FIXED);

        return swUtils.normalizeNumber(
            (amount + __fixed) / (1 - __percent) - amount
        );
    },
    // covered 😄👍
    customerACHStripeFee: function (amount, percent) {
        if(amount < 0) {
            throw new Error('Amount should be greater than 0');
        }
        if(!_.isNumber(amount)) {
            throw new Error('Amount should be a number');
        }
        if(amount === 0) {
            return 0;
        }

        return swUtils.normalizeNumber(amount / (1 - (parseFloat(percent, 10) || DEFAULT_ACH_PERCENT)) -  amount);
    },
    // covered 😄👍
    defaultACHStripeFee: function (amount, percent) {
        if(amount < 0) {
            throw new Error('Amount should be greater than 0');
        }
        if(!_.isNumber(amount)) {
            throw new Error('Amount should be a number');
        }
        if(amount === 0) {
            return 0;
        }

        return swUtils.normalizeNumber(amount * (parseFloat(percent) || DEFAULT_ACH_PERCENT));
    },
    // covered 😄👍
    defaultStripeFee: function (amount, percent, fixed) {
        if(amount < 0) {
            throw new Error('Amount should be greater than 0');
        }
        if(!_.isNumber(amount)) {
            throw new Error('Amount should be a number');
        }
        if(amount === 0) {
            return 0;
        }

        let __percent   = parseFloat(percent, 10) || DEFAULT_STRIPE_PERCENT;
        let __fixed     = parseFloat(fixed, 10) || DEFAULT_STRIPE_FIXED;

        return swUtils.normalizeNumber(amount * (__percent) + __fixed);
    },
    // covered 😄👍
    getSWClientID: function () {
        let sql =
            `SELECT JSON_BUILD_OBJECT(
                'live', (
                SELECT "value"->'client_id' "client_id"
                                 FROM settings
                                 WHERE "key" = 'stripe_connect'
            ), 'test', (
                SELECT "value"->'client_id' "client_id"
                                 FROM settings
                                 WHERE "key" = 'stripe_connect_dev'
            )) "data"
                         `;

        return Db.query(sql)
        .then(result => result.rows[0] && result.rows[0].data || {})
    },
    retrieveFingerPrint: function (sk, token, apiVer) {
        let stripe = this.initStripe(sk, apiVer || sails.config.stripe_api.version);

        if (stripe === null) {
            return Promise.reject({ validation: 'Invalid Stripe Secret Key passed' });
        }

        return StripeConnect.__retrieveFingerprint__(stripe, token);
    },



    /**
    * Find latest paid bank account transfers
    **/
    findTransfers: function (stripeAccountID) {
        return this.account.getStripeAccountRowByID(stripeAccountID)
        .then(account => {
            if (account === null) {
                return Promise.reject(new Error('Account not found'));
            }

            let stripeInstance = this.initStripe(account.secret_key);

            if (stripeInstance === null) {
                return Promise.reject(new Error('Stripe Initialization failed'));
            }

            return this.__findPaidBankTransfers__([], stripeInstance);
        });
    },

    __findPaidBankTransfers__: function (res, stripeInstance, startingAfter) {
        return stripeInstance.transfers.list({
            created: {
                // First SW Payment was on 2014-10-02, ********** - 2014-10-01
                gte: **********
            },
            status          : 'paid',
            limit           : 100, // maximum available limit
            starting_after  : startingAfter
        })
        .then(resp => resp.data)
        .then(transfers => {
            if (transfers.length === 0) {
                return res;
            } else {
                res = res.concat(transfers.filter(tr => (tr.type === 'bank_account')));

                let lastItem = transfers[transfers.length - 1];

                return this.__findPaidBankTransfers__(res, stripeInstance, lastItem.id)
            }
        })
    },

    account: _AccountService,

    filterForbiddenLettersStatementDescription(desc) {
        if(typeof desc === 'string') {
            let reg = new RegExp('['+ this.DESC_FORBIDDEN_LETTERS +']', 'g');
            return desc.replace( reg , '');
        }
    },

    statementDescriptorIsNotValid(desc) {
        if(typeof desc !== 'string') {
            return;
        }

        let doesNotContainsForbiddenLetters = new RegExp(this.DESC_ALLOWED_LETTERS).test(desc);
        let containsOnlyNumbers = new RegExp(this.NUMBER_REG_EXP).test(desc);

        return !doesNotContainsForbiddenLetters || containsOnlyNumbers;
    },

    async updateChargeMetadataAfterRefund (paymentData = {}, totals = {}) {
        let {
            stripe_payment_id: paymentID,
            charge_id: chargeID,
            stripe_account_id: stripeAccountID
        } = paymentData;

        let metadata = this.paymentCommonMetadataFields(
            _.pick(totals, ['stripeFee', 'netProfit', 'extraFee', 'swFee'])
        );

        try {
            if(!paymentID) {
                throw new Error('Stripe Payment ID not found');
            }

            if(!chargeID) {
                throw new Error('Stripe Charge ID not found');
            }

            if(!stripeAccountID) {
                throw new Error('Stripe Account ID not found');
            }
        } catch (err) {
            loggers.errors_log.error('Metadata update error\n', `${err}\n`, paymentData);
            return;
        }

        return Promise.all([
            this.chargeUpdate.updateStripeCharge({ metadata }, chargeID),
            this.chargeUpdate.updateConnectedAccountStripeCharge({ metadata }, paymentID, stripeAccountID)
        ]);
    },

    paymentCommonMetadataFields ({ netProfit, stripeFee, extraFee, swFee }) {
        let format = (value) => (value || Number(value) === 0)?`$${value.toFixed(2)}`:undefined;

        return {
            total: format(netProfit),
            stripe_fee: format(stripeFee),
            additional_fee: format(extraFee),
            sw_fee: format(swFee),
        }
    },

    initStripe (sk, ver) {
        try {
            let params = {
                maxNetworkRetries: 2
            };

            if(ver) {
                params.apiVersion = ver;
            }

            return stripeLib(sk, params);
        } catch (e) {
            loggers.errors_log.error(e);
            return null;
        }
    }
};

stripeService.webhook       = new _WebhookService(StripeConnect);
stripeService.payouts       = new _PayoutsService();
stripeService.notifications = new _NotificationsService(stripeService);
stripeService.paymentCard   = new _PaymentCardService(StripeConnect, swUtils);
stripeService.chargeUpdate  = new _ChargeUpdateService(StripeConnect);
stripeService.files  = new _FileService(StripeConnect);
stripeService.disputeEvidence  = new _DisputeEvidenceService(StripeConnect);

Object.defineProperty(stripeService, 'DESC_ALLOWED_LETTERS', {
    value           : DESC_ALLOWED_LETTERS,
    writable        : false,
    configurable    : false
})

Object.defineProperty(stripeService, 'DESC_FORBIDDEN_LETTERS', {
    value           : DESC_FORBIDDEN_LETTERS,
    writable        : false,
    configurable    : false
});

Object.defineProperty(stripeService, 'NUMBER_REG_EXP', {
    value           : NUMBER_REG_EXP,
    writable        : false,
    configurable    : false
});

Object.defineProperty(stripeService, 'NOT_AUTO_TR_ERR_MSG', {
    value: 'Balance transaction history can only be filtered on automatic transfers, not manual.',
    writable        : false,
    configurable    : false
})

Object.defineProperty(stripeService, 'DISPUTE_FEE', {
    value           : DISPUTE_FEE,
    writable        : false,
    configurable    : false
});

Object.defineProperty(stripeService, 'FAILED_ACH_PAYMENT_FEE', {
    value           : FAILED_ACH_PAYMENT_FEE,
    writable        : false,
    configurable    : false
});

Object.defineProperty(stripeService, 'DEFAULT_STRIPE_PERCENT', {
    value           : DEFAULT_STRIPE_PERCENT,
    writable        : false,
    configurable    : false
});

Object.defineProperty(stripeService, 'DEFAULT_STRIPE_FIXED', {
    value           : DEFAULT_STRIPE_FIXED,
    writable        : false,
    configurable    : false
});

Object.defineProperty(stripeService, 'DEFAULT_ACH_PERCENT', {
    value           : DEFAULT_ACH_PERCENT,
    writable        : false,
    configurable    : false
});

Object.defineProperty(stripeService, 'DEFAULT_ACH_FEE_CAP', {
    value           : DEFAULT_ACH_FEE_CAP,
    writable        : false,
    configurable    : false
});

Object.defineProperty(stripeService, 'DISPUTE_STATUS', {
    value           : DISPUTE_STATUS,
    writable        : false,
    configurable    : false
});

Object.defineProperty(stripeService, 'DEFAULT_SW_ACH_PERCENT', {
    value           : sails.config.stripe_api.default_sw_ach_percent,
    writable        : false,
    configurable    : false
});

Object.defineProperty(stripeService, 'DEFAULT_SW_ACH_FIXED', {
    value           : sails.config.stripe_api.default_sw_ach_fixed,
    writable        : false,
    configurable    : false
});

Object.defineProperty(stripeService, 'STRIPE_PERCENT_MIN_DEFAULT', {
    value           : STRIPE_PERCENT_MIN_DEFAULT,
    writable        : false,
    configurable    : false
});

Object.defineProperty(stripeService, 'STRIPE_PERCENT_MIN_BEFORE_2021_01_01', {
    value           : STRIPE_PERCENT_MIN_BEFORE_2021_01_01,
    writable        : false,
    configurable    : false
});

Object.defineProperty(stripeService, 'STRIPE_PERCENT_CHANGE_DATE_2020_12_31', {
    value           : STRIPE_PERCENT_CHANGE_DATE_2020_12_31,
    writable        : false,
    configurable    : false
});

Object.defineProperty(stripeService, 'DISPUTE_FEE_COLLECTION_CHANGE_DATE', {
    value: DISPUTE_FEE_COLLECTION_CHANGE_DATE,
    writable: false,
    configurable: false,
})

Object.defineProperty(stripeService, 'DISPUTE_FEE_COLLECTION_EXCLUDE_WON_DATE', {
    value: DISPUTE_FEE_COLLECTION_EXCLUDE_WON_DATE,
    writable: false,
    configurable: false,
})

module.exports = stripeService;
