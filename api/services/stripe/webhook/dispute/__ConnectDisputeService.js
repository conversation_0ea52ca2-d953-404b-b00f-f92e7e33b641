/* global sails */

const teamsConstants = require('../../../../constants/teams');

class ConnectDisputeService {
    constructor(ConnectService, ParentService) {
        this.ConnectService = ConnectService;
        this.ParentService = ParentService;
    }

    get  STRIPE_DISPUTE_FEE () {
        // https://stripe.com/docs/disputes/faq
        return 15;
    }

    process (disputeType, disputeStatus, chargeID, stripeEventId, purchase) {
        let isLostDispute = this.ParentService.isLostDispute(disputeType, disputeStatus);

        let actions = [
            this.__saveDisputeToDb(
                disputeType, disputeStatus, purchase.purchase_id, chargeID, stripeEventId, purchase.payment_for
            )
        ];

        if(isLostDispute) {
            actions.push(this.__processLostDispute(purchase, chargeID));
        }

        return Promise.all(actions);
    }

    getPurchaseData (purchaseID) {
        return Db.query(
            `SELECT 
                p.event_id, p.payment_for, p.ticket_barcode, p.amount, p.amount_refunded, p.purchase_id, p.user_id,
                p.additional_fee_amount, p.stripe_card_fingerprint "fingerprint", p.email, u.email "user_email",
                (
                   SELECT sa.stripe_account_id
                   FROM stripe_account sa
                   WHERE sa.secret_key = CASE
                                             WHEN p.payment_for = 'teams' THEN e.stripe_teams_private_key
                                             WHEN p.payment_for = 'tickets' THEN e.stripe_tickets_private_key
                                             WHEN p.payment_for = 'booths' THEN e.stripe_exhibitors_private_key
                       END
                   GROUP BY sa.stripe_account_id
                ) stripe_account_id,
                sc.stripe_payment_id,
                p.stripe_charge_id charge_id,
                spm.stripe_payment_method_id
             FROM purchase p
             LEFT JOIN "user" u 
                ON u.user_id = p.user_id
             JOIN event e ON e.event_id = p.event_id
             LEFT JOIN stripe_charge sc ON sc.stripe_charge_id = p.stripe_charge_id
             LEFT JOIN user_stripe_customer usc ON usc.user_id = u.user_id
             LEFT JOIN stripe_payment_method spm ON spm.stripe_customer_id = usc.stripe_customer_id AND spm.fingerprint = p.stripe_card_fingerprint
             WHERE purchase_id = $1`,
            [purchaseID]
        ).then(result => result.rows && result.rows[0] || null);
    }

    __processLostDispute (purchaseRow, chargeID) {
        let additionalFee = Number(purchaseRow.additional_fee_amount) || 0;

        const { stripe_payment_method_id, user_id } = purchaseRow

        return Promise.all([
            this.__updateEventBalance(purchaseRow.event_id, additionalFee),
            this.ConnectService.reverseFundsOfTheLostDisputes(chargeID),
            this.__updateMetadata(purchaseRow),
            stripe_payment_method_id
                ? StripeService.paymentCard.removeUserPaymentMethod(
                      user_id,
                      stripe_payment_method_id
                  )
                : Promise.resolve(),
        ]);
    }

    __updateMetadata (purchaseRow) {
        return StripeService.updateChargeMetadataAfterRefund(purchaseRow, {
            stripeFee: 0,
            extraFee: 0,
            swFee: 0,
            netProfit: 0
        })
    }

    __saveDisputeToDb (disputeType, disputeStatus, purchaseId, chargeId, stripeEventId, paymentFor) {
        let updatePurchaseSQL =
                squel.update().table('purchase')
                    .where('stripe_charge_id = ?', chargeId)
                    .where('purchase_id = ?', purchaseId)
                    .returning('event_id, payment_for, ticket_barcode, amount, amount_refunded, purchase_id, user_id'),
            saveHistorySQL =
                squel.insert().into('purchase_history')
                    .set('stripe_event_id', stripeEventId)
                    .set('purchase_id', purchaseId)

        let updateTeamsPaymentStatus, updatePurchaseTeamSQL;
        let statusPaid = teamsConstants.PAYMENT_STATUSES.DISPUTED;

        if(paymentFor === 'teams') {
            updateTeamsPaymentStatus = knex('roster_team AS rt')
                .whereNull('rt.deleted')
                .whereIn(
                    'roster_team_id',
                    knex('purchase_team AS pt').select('roster_team_id')
                        .join('purchase AS p', 'p.purchase_id', 'pt.purchase_id')
                        .where('p.purchase_id', purchaseId)
                        .where('p.payment_for', paymentFor)
                        .where('p.stripe_charge_id', chargeId)
                        .whereNull('pt.canceled')
                );
        }

        if(disputeType === this.ParentService.DISPUTE.CREATED) {

            updatePurchaseSQL.set('dispute_created', 'now()');
            updatePurchaseSQL.set('dispute_status', 'pending');

            saveHistorySQL.set('action', 'dispute.created');
            saveHistorySQL.set('description', `Dispute created. SW Charge #${purchaseId}, Stripe Charge ${chargeId}`)

        } else if (disputeType === this.ParentService.DISPUTE.CLOSED) {

            if (disputeStatus === StripeService.DISPUTE_STATUS.LOST) {
                updatePurchaseSQL.set('status', 'canceled');

                saveHistorySQL.set('action', 'dispute.lost');
                saveHistorySQL.set('description', `Dispute LOST for Stripe Charge ${chargeId}`);

                updatePurchaseTeamSQL =
                    knex('purchase_team AS pt').update({
                        canceled: knex.fn.now()
                    }).where('pt.purchase_id', purchaseId)
            } else if (disputeStatus === StripeService.DISPUTE_STATUS.WON) {
                statusPaid = teamsConstants.PAYMENT_STATUSES.PAID;

                saveHistorySQL.set('action', 'dispute.won');
                saveHistorySQL.set('description', `Dispute WON for Stripe Charge ${chargeId}`);

            } else if (disputeStatus === 'warning_closed') {
                /*
                * NOTE: "warning_closed" - this status means that the case has timed out and did not escalate into a full
                * chargeback (similar to a won status)
                * resource: https://support.stripe.com/questions/what-is-the-difference-between-a-chargeback-and-an-inquiry-or-retrieval
                */
                saveHistorySQL.set('action', 'dispute.won');
                saveHistorySQL.set('description', `Dispute WON (The case has timed out) for Stripe Charge ${chargeId}`);
                statusPaid = teamsConstants.PAYMENT_STATUSES.PAID;
            }

            updatePurchaseSQL.set('dispute_status', disputeStatus);
        } else {
            return Promise.resolve();
        }

        if(!_.isEmpty(updateTeamsPaymentStatus)) {
            updateTeamsPaymentStatus.update('status_paid', statusPaid);
        }

        return Promise.all([
            Db.query(updatePurchaseSQL),
            Db.query(saveHistorySQL),
            updateTeamsPaymentStatus
                ? Db.query(updateTeamsPaymentStatus)
                : Promise.resolve(),
            updatePurchaseTeamSQL
                ? Db.query(updatePurchaseTeamSQL)
                : Promise.resolve(),
        ])
    }

    __updateEventBalance (eventId, additionalFeeAmount) {
        let deductedBalance = this.STRIPE_DISPUTE_FEE + additionalFeeAmount;

        return Db.query(
            `UPDATE "event"
            SET "tickets_sw_balance" = COALESCE("tickets_sw_balance", 0) - $2
            WHERE "event_id" = $1`,
            [eventId, deductedBalance]
        ).then(() => {})
    }
}

module.exports = ConnectDisputeService;
