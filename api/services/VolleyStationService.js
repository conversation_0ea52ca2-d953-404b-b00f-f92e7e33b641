const { ENTRY_STATUSES, USAV_SANC_BODY } = require('../constants/teams');

class VolleyStationService {
    constructor() {}

    /**
     * Check if event has VolleyStation enabled
     * @param {number} eventId - The event ID
     * @returns {Promise<boolean>} Whether VolleyStation is enabled
     */
    async isEnabledForEvent(eventId) {
        const cacheKey = `volley-station:enabled:${eventId}`;

        return Cache.getResult(
            cacheKey,
            () => this._checkEventEnabled(eventId),
            {
                ttl: Cache.TTL_DEFAULT,
                tags: [
                    Cache.tag.dbTable('event', { event_id: eventId }),
                ],
            }
        );
    }

    /**
     * Check if the event has VolleyStation enabled (extracted for caching)
     * @private
     */
    async _checkEventEnabled(eventId) {
        const query = knex('event')
            .select('event_id')
            .where('event_id', eventId)
            .where('enable_volley_station', true);

        const { rows } = await Db.query(query.toString());
        return rows.length > 0;
    }

    /**
     * Get schedule data for a specific event with pagination
     * @param {number} eventId - The event ID
     * @param {number} page - Page number (1-based)
     * @param {number} limit - Items per page
     * @returns {Promise<Object>} Complete pagination response with data and metadata
     */
    async getPaginatedSchedule(eventId, page, limit) {
        const cacheKey = `volley-station:schedule:${eventId}:${page}:${limit}`;

        return Cache.getResult(
            cacheKey,
            () => this._getPaginatedScheduleData(eventId, page, limit),
            {
                ttl: Cache.TTL_DEFAULT,
                tags: [
                    Cache.tag.dbTable('event', { event_id: eventId }),
                    Cache.tag.dbTable('matches'),
                ],
            }
        );
    }

    /**
     * Get schedule data for a specific event with pagination (extracted for caching)
     * @private
     */
    async _getPaginatedScheduleData(eventId, page, limit) {
        const offset = (page - 1) * limit;

        const query = knex({ e: 'event' })
            .select([
                'd.gender',
                { division_name: 'd.name' },
                'd.division_id',
                { match_uuid: 'm.match_id' },
                { event: 'm.event_id' },
                knex.raw(`m.event_id || '_' || m.division_short_name || '_' || m.display_name as match_id`),
                { div: 'm.division_short_name' },
                'm.day',
                knex.raw(`to_char(m.secs_start AT TIME ZONE e.timezone, 'YYYY-MM-DD"T"HH24:MI:SS"Z"') as date_time`),
                { court: 'c.sort_priority' },
                { court_alpha: 'c.short_name' },
                { pool: 'pb.display_name' },
                'm.team1_roster_id',
                'm.team2_roster_id',
                'm.ref_roster_id',
                { team_1_name: 't1.team_name' },
                { team_2_name: 't2.team_name' },
                { ref_name: 'tr.team_name' },
                { master_team_id_1: 't1.master_team_id' },
                { master_team_id_2: 't2.master_team_id' },
                { match_type: 'm.type' },
                knex.raw(`(
                    SELECT jsonb_object_agg(key, value)
                    FROM jsonb_each_text(m.results)
                    WHERE key LIKE 'set%'
                ) as results`),
                knex.raw(`u.first || ' ' || u.last as official`),
                knex.raw('COUNT(*) OVER() as total_count'),
            ])
            .join({ d: 'division' }, 'd.event_id', 'e.event_id')
            .join({ m: 'matches' }, function () {
                this.on('m.event_id', '=', 'd.event_id')
                    .andOn('m.division_id', '=', 'd.division_id');
            })
            .join({ pb: 'poolbrackets' }, 'pb.uuid', 'm.pool_bracket_id')
            .join({ c: 'courts' }, 'c.uuid', 'm.court_id')
            .leftJoin({ t1: 'roster_team' }, function () {
                this.on('t1.roster_team_id', '=', 'm.team1_roster_id')
                    .andOn('t1.event_id', '=', 'd.event_id')
                    .andOn('t1.division_id', '=', 'd.division_id')
                    .onNull('t1.deleted')
                    .andOnVal('t1.status_entry', '=', ENTRY_STATUSES.ACCEPTED);
            })
            .leftJoin({ t2: 'roster_team' }, function () {
                this.on('t2.roster_team_id', '=', 'm.team2_roster_id')
                    .andOn('t2.event_id', '=', 'd.event_id')
                    .andOn('t2.division_id', '=', 'd.division_id')
                    .onNull('t2.deleted')
                    .andOnVal('t2.status_entry', '=', ENTRY_STATUSES.ACCEPTED);
            })
            .leftJoin({ tr: 'roster_team' }, function () {
                this.on('tr.roster_team_id', '=', 'm.ref_roster_id')
                    .andOn('tr.event_id', '=', 'd.event_id')
                    .andOn('tr.division_id', '=', 'd.division_id')
                    .onNull('tr.deleted')
                    .andOnVal('tr.status_entry', '=', ENTRY_STATUSES.ACCEPTED);
            })
            .leftJoin(
                knex.raw(`
                    LATERAL (
                        SELECT eos1.event_official_id
                        FROM event_official_schedule eos1
                        WHERE
                            eos1.event_id = m.event_id
                            AND eos1.division_id = m.division_id
                            AND eos1.match_name = m.display_name
                            AND eos1.ref_num = 1
                        ORDER BY eos1.event_official_schedule_id DESC
                        LIMIT 1
                    ) eos
                `),
                knex.raw('TRUE')
            )
            .leftJoin({ eof: 'event_official' }, 'eof.event_official_id', 'eos.event_official_id')
            .leftJoin({ of: 'official' }, 'of.official_id', 'eof.official_id')
            .leftJoin({ u: 'user' }, 'u.user_id', 'of.user_id') // ensures proper quoting
            .where('e.event_id', '=', eventId)
            .where(function () {
                this.whereNotNull('m.team1_roster_id').orWhereNotNull('m.team2_roster_id');
            })
            .orderBy('m.secs_start', 'asc')
            .limit(limit)
            .offset(offset);

        const { rows } = await Db.query(query.toString());

        let totalCount = 0;
        for (const row of rows) {
            if (!totalCount) totalCount = parseInt(row.total_count, 10);
            delete row.total_count;
        }

        return this._createPaginationResponse(rows, page, limit, totalCount);
    }

    /**
     * Get team roster data for a specific event with pagination
     * @param {number} eventId - The event ID
     * @param {number} page - Page number (1-based)
     * @param {number} limit - Items per page
     * @returns {Promise<Object>} Complete pagination response with data and metadata
     */
    async getPaginatedTeamRoster(eventId, page, limit) {
        const cacheKey = `volley-station:team-roster:${eventId}:${page}:${limit}`;

        return Cache.getResult(
            cacheKey,
            () => this._getPaginatedTeamRosterData(eventId, page, limit),
            {
                ttl: Cache.TTL_DEFAULT,
                tags: [
                    Cache.tag.dbTable('event', { event_id: eventId }),
                ],
            }
        );
    }

    /**
     * Get team roster data for a specific event with pagination (extracted for caching)
     * @private
     */
    async _getPaginatedTeamRosterData(eventId, page, limit) {
        const offset = (page - 1) * limit;

        const query = knex({ e: 'event' })
            .select([
                'd.division_id',
                'd.name as division_name',
                'rt.roster_team_id as team_id',
                'rt.team_name',
                'rc.club_name',
                knex.raw(`
                    COALESCE((
                        SELECT jsonb_agg(jsonb_build_object(
                            'athlete_id', ra.roster_athlete_id,
                            'first', ma.first,
                            'last', ma.last,
                            'position', sp.short_name,
                            'jersey', CASE 
                                WHEN e.sport_sanctioning_id = ${USAV_SANC_BODY.AAU} THEN COALESCE(ra.aau_jersey, ma.aau_jersey)
                                ELSE COALESCE(ra.jersey, ma.jersey)
                            END
                        )
                        ORDER BY
                            CASE WHEN e.sport_sanctioning_id = ${USAV_SANC_BODY.AAU} THEN COALESCE(ra.aau_jersey, ma.aau_jersey, 0)
                                 ELSE COALESCE(ra.jersey, ma.jersey, 0) END,
                            ma.last, ma.first
                        )
                        FROM roster_athlete ra
                        JOIN master_athlete ma ON ma.master_athlete_id = ra.master_athlete_id
                        AND ma.deleted IS NULL
                        LEFT JOIN sport_position sp ON sp.sport_position_id = COALESCE(ra.sport_position_id, ma.sport_position_id)
                        WHERE 
                            ra.roster_team_id = rt.roster_team_id
                            AND ra.deleted IS NULL
                            AND ra.deleted_by_user IS NULL
                            AND (ra.as_staff = 0 OR ra.as_staff IS NULL)
                    ), '[]'::jsonb) as athletes
                `),
                knex.raw(`
                    COALESCE((
                        SELECT jsonb_agg(jsonb_build_object(
                            'first', s.first,
                            'last', s.last,
                            'role_name', s.role_name
                        ) ORDER BY s.sort_order NULLS LAST, s.role_name, s.last, s.first)
                        FROM (
                            SELECT ms.first, ms.last, r.name AS role_name, r.sort_order
                            FROM master_staff ms
                            JOIN roster_staff_role rsr ON rsr.master_staff_id = ms.master_staff_id
                                AND rsr.roster_team_id = rt.roster_team_id
                                AND rsr.deleted IS NULL
                                AND rsr.deleted_by_user IS NULL
                            LEFT JOIN master_staff_role msr ON msr.master_staff_id = rsr.master_staff_id
                                AND msr.master_team_id = rsr.master_team_id
                            LEFT JOIN role r ON r.role_id = COALESCE(NULLIF(rsr.role_id, '0'), msr.role_id)
                            
                            UNION ALL
                            
                            SELECT ma.first, ma.last, 'Staff' AS role_name, NULL::INTEGER AS sort_order
                            FROM master_athlete ma
                            JOIN roster_athlete ra ON ra.master_athlete_id = ma.master_athlete_id
                                AND ra.event_id = rt.event_id
                                AND ra.deleted IS NULL
                                AND ra.deleted_by_user IS NULL
                            WHERE 
                                ra.roster_team_id = rt.roster_team_id
                                AND ra.as_staff > 0
                        ) s
                    ), '[]'::jsonb) as staff
                `),
                knex.raw('COUNT(*) OVER() as total_count')
            ])
            .join('division as d', 'd.event_id', 'e.event_id')
            .join('roster_team as rt', function () {
                this.on('rt.event_id', '=', 'd.event_id')
                    .on('rt.division_id', '=', 'd.division_id')
                    .onNull('rt.deleted')
                    .andOnVal('rt.status_entry', '=', ENTRY_STATUSES.ACCEPTED);
            })
            .join('roster_club as rc', function () {
                this.on('rc.event_id', '=', 'rt.event_id')
                    .on('rc.roster_club_id', '=', 'rt.roster_club_id')
                    .onNull('rc.deleted');
            })
            .where('e.event_id', eventId)
            .orderBy('rt.team_name')
            .orderBy('rt.roster_team_id')
            .limit(limit)
            .offset(offset);

        const { rows } = await Db.query(query.toString());

        let totalCount = 0;
        rows.forEach(row => {
            if (!totalCount) totalCount = parseInt(row.total_count, 10);
            delete row.total_count;
        });

        return this._createPaginationResponse(rows, page, limit, totalCount);
    }

    /**
     * Create a pagination response object
     * @param {Array} data - The data array
     * @param {number} page - Current page number
     * @param {number} limit - Items per page
     * @param {number} totalCount - Total number of items
     * @returns {Object} Pagination response
     */
    _createPaginationResponse(data, page, limit, totalCount) {
        const totalPages = Math.ceil(totalCount / limit);
        const hasNext = page * limit < totalCount;
        const hasPrev = page > 1;
        return {
            data,
            pagination: {
                page,
                limit,
                total: totalCount,
                totalPages,
                hasNext,
                hasPrev
            }
        };
    }
}

module.exports = new VolleyStationService();
