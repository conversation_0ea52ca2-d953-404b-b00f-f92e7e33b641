class ClubServiceEvents {
    constructor() {}

    /**
     * Returns a list of upcoming events
     * the club is registered to
     *
     * @param {number} masterClubID
     * @returns {Array}
     */
    getUpcomingEvents(masterClubID) {
        const query = squel
            .select()
            .field('e.long_name', 'name')
            .field('e.event_id')
            .from('roster_club', 'rc')
            .join('master_club', 'mc', 'mc.master_club_id = rc.master_club_id')
            .join('event', 'e', 'e.event_id = rc.event_id')
            .join('division', 'd',
                squel.expr()
                    .and('d.event_id = e.event_id')
                    .and('d.closed IS NULL')
                    .and('d.locked IS NOT TRUE')
                    .and('d.published IS TRUE')
            )
            .where('rc.master_club_id = ?', masterClubID)
            .where('rc.deleted IS NULL')
            .where('rc.is_local IS NOT TRUE')
            .group('e.event_id, rc.roster_club_id, mc.master_club_id, d.event_id')
            .having('max(coalesce(d.date_reg_close, e.date_reg_close)) > (NOW() AT TIME ZONE e.timezone)')
            .order('e.date_start');

        return Db.query(query).then(({ rows }) => rows);
    }

    getLinkToTeamsList(eventID) {
        const url = sails.config.urls.main_app.baseUrl;

        return `${url}/#/event/${eventID}/allteams`;
    }

    generateLinksToTeamsList(events) {
        _.forEach(events, (event) => {
            event.link_to_teams_list = this.getLinkToTeamsList(event.event_id);

            delete event.event_id;
        });

        return events;
    }

    async getEvent (eventID, masterClubID, clubRegCode) {
        const event = await this.#getEventData(eventID, masterClubID, clubRegCode);
        const divisions = await this.#getDivisions(eventID);

        await this.#addClubEventRules(eventID, masterClubID, event);

        return {
            ...event,
            divisions
        }
    }

    async #getEventData (eventID, masterClubID, clubRegCode) {
        const query = squel.select()
            .from('event', 'e')
            .join('pg_timezone_names', 'tn', 'tn.name = e.timezone')
            .field('(e.teams_settings->>\'show_vertical_insurance_form\')::BOOLEAN IS TRUE', 'allow_vertical_insurance')
            .field(
                `CASE 
                    WHEN (rc."roster_club_id" IS NOT NULL AND e.club_private_reg_code IS NOT NULL AND e.date_reg_open > (NOW() AT TIME ZONE e.timezone))
                        THEN e.club_private_reg_code
                    ELSE e."event_id"::TEXT
                 END`,
                'event_id')
            .field(`(
                SELECT COALESCE((
                    SELECT JSONB_AGG(JSONB_BUILD_OBJECT(
                        'custom_form_event_id', cfe.custom_form_event_id,
                        'type', cfe.type
                    ))
                     FROM custom_form_event cfe
                     WHERE cfe.type IN ('team_assign_for_event')
                       AND cfe.published IS NOT NULL
                       AND cfe.event_id = e.event_id
                 ), '[]'::JSONB)
            )`, 'custom_forms_requires_submitting')
            .field('e.long_name')
            .field('e.name')
            .field('e.roster_deadline')
            .field('e.team_members_validation', 'validation')
            .field('e.has_male_teams')
            .field('e.has_female_teams')
            .field('e.has_coed_teams')
            .field('e.reg_fee')
            .field('e.date_end')
            .field('e.event_type')
            .field('e.housing_company_id')
            .field('e.credit_surcharge')
            .field('e.sport_sanctioning_id')
            .field('rc.roster_club_id', 'roster_club')
            .field('e.social_links')
            .field('e.online_team_checkin_mode')
            .field('(e.date_start > (now() AT TIME ZONE e.timezone))', 'not_started')
            .field(`((e.date_start - INTERVAL '1 day') > (NOW() AT TIME ZONE e.timezone))`, 'earlier_day_before_start_event')
            .field('(e.date_end < (now() AT TIME ZONE e.timezone))', 'past')
            .field('e.online_team_checkin_available')
            .field(`to_char(e.online_team_checkin_end, 'Mon DD, YYYY, HH12:MI AM')`, 'online_team_checkin_end')
            .field(`to_char(e.online_team_checkin_start, 'Mon DD, YYYY, HH12:MI AM')`, 'online_team_checkin_start')
            .field('e.online_team_checkin_disclaimer')
            .field('e.online_team_checkin_info')
            .field(`e.online_team_checkin_end < (NOW() AT TIME ZONE e.timezone)`, 'online_checkin_over')
            .field(`e.online_team_checkin_start > (NOW() AT TIME ZONE e.timezone)`, 'online_checkin_not_started')
            .field('tn.abbrev', 'timezone_abbrev')
            .field('e.teams_entry_sw_fee')
            .left_join('roster_club', 'rc',
                squel.expr()
                    .and('rc.event_id = e.event_id')
                    .and('master_club_id = ?', masterClubID))

        if (clubRegCode) {
            query
                .where('rc.roster_club_id IS NOT NULL OR e.club_private_reg_active IS TRUE')
                .where('e.club_private_reg_code = ?', clubRegCode)
        } else {
            query
                .where('(rc.roster_club_id IS NOT NULL OR e.date_reg_open < (NOW() AT TIME ZONE e.timezone))')
        }

        query
            .where('e.event_id = ?', eventID)
            .where('e.published IS TRUE')
            .where('e.teams_use_clubs_module IS TRUE')
            .where('e.live_to_public IS TRUE');

        const { rows: [event] } = await Db.query(query);

        if(_.isEmpty(event)) {
           throw { validation: 'There is no event with such identifier' };
        }

        return event;
    }

    async #getDivisions (eventID) {
        const query =  `
            SELECT 
                d.division_id AS id, 
                d.name, 
                d.gender, 
                d.max_age, 
                d.closed  
            FROM division d 
            WHERE d.event_id = $1 
            AND d.published = TRUE
            ORDER BY d.sort_order, d.gender, d.max_age DESC, d.level_sort_order, d.level`;

        const { rows: divisions } = await Db.query(query, [eventID]);

        return divisions;
    }

    async #addClubEventRules (eventID, masterClubID, eventSettings) {
        await this.#addCustomFormsRules(eventID, masterClubID, eventSettings);
    }

    async #addCustomFormsRules (eventID, masterClubID, eventSettings) {
        let formsRequiresSubmitting = eventSettings?.custom_forms_requires_submitting;

        if(!_.isEmpty(formsRequiresSubmitting)) {
            const customFormsRules = await EventRegistrationService.registrationRules.customForms.getFormsSubmittingData(
                formsRequiresSubmitting,
                eventID,
                masterClubID
            );

            eventSettings.custom_forms_requires_submitting = formsRequiresSubmitting.reduce((result, form) => {
                let formRule = customFormsRules.find(rule => rule.customFormEventID === form.custom_form_event_id);

                if(formRule.isNotSubmitted) {
                    result.push(form);
                }

                return result;
            }, []);
        }
    }
}

module.exports = ClubServiceEvents;
