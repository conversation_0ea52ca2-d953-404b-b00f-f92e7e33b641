const {
    paymentData: paymentValidationSchema,
    changePaymentTypeData: changePaymentTypeValidationSchema,
    paymentHubData: paymentHubValidationSchema,
    changePaymentHubPaymentTypeData,
} = require('../../../validation-schemas/team-payment');

const swUtils = require('../../../lib/swUtils');

const { FEE_PAYER, PAYMENT_PROVIDER } = require('../../../constants/payments');

class UpdatePaymentService {
    async updatePayment (payment) {
        const { error } = paymentValidationSchema.validate(payment);

        if(!_.isEmpty(error)) {
            throw { validation: error.details[0].message };
        }

        let { settings, recountedReceipt, fees } =
            await this.__getDataForPaymentUpdate({
                ...payment,
                payment_provider: PAYMENT_PROVIDER.STRIPE,
            });

        await this.__updatePaymentIntent(settings, payment, recountedReceipt.total, fees);

        return { recountedReceipt, settings, fees };
    }

    async updatePaymentHubOrder (payment) {
        const { error } = paymentHubValidationSchema.validate(payment);

        if(!_.isEmpty(error)) {
            throw { validation: error.details[0].message };
        }

        let { settings, recountedReceipt, fees } =
            await this.__getDataForPaymentUpdate({
                ...payment,
                payment_provider: PAYMENT_PROVIDER.PAYMENT_HUB,
            });

        await this.__updatePaymentHubOrder(settings, payment, recountedReceipt.total, fees);

        return { recountedReceipt, settings, fees };
    }

    __validatePaymentChangeData(data, provider) {
        if(provider === 'stripe') {
            return changePaymentTypeValidationSchema.validate(data);
        }

        if(provider === 'payment-hub') {
            return changePaymentHubPaymentTypeData.validate(data);
        }

        throw new Error('Unsupported provider: ', provider)
    }

    __updatePaymentProviderData({ settings, data, total, fees }, provider) {
        if (provider === 'stripe') {
            return this.__updatePaymentIntent(
                settings,
                data,
                total,
                fees
            );
        }

        if (provider === 'payment-hub') {
            return this.__updatePaymentHubOrder(
                settings,
                data,
                total,
                fees
            );
        }

        throw new Error('Unsupported provider: ', provider)
    }

    async changePaymentType (data, provider) {
        const { error } = this.__validatePaymentChangeData(data, provider)

        if(!_.isEmpty(error)) {
            throw { validation: error.details[0].message };
        }

        const purchase = await TeamsPaymentService.getPaymentForTypeChange(
            data.event_id,
            data.purchase_id,
            data.season,
            data.master_club_id
        );

        if (purchase === null) {
            throw { validation: 'Suitable payment not found' }
        }

        if (purchase.type !== TeamsPaymentService.CHECK_TYPE) {
            throw { validation: 'Only check payments allowed for type change' }
        }

        const receipt = TeamsPaymentService.makeReceiptFromTeamsList(purchase.teams);

        const { declinedTeams, properTeams } =
            TeamsPaymentService.separateDeclinedTeams(purchase.teams);
        
        if (properTeams.length === 0) {
            throw { validation: 'Purchased Teams not found' }
        }

        const payment = {
            ...data,
            receipt
        }

        let { settings, recountedReceipt, fees } = await this.__getDataForPaymentUpdate(payment);
        
        await this.__updatePaymentProviderData(
            {
                settings,
                data,
                total: recountedReceipt.total,
                fees
            },
            provider
        );

        const paymentForSession = {
            ...payment,
            declinedTeams,
            changePaymentType: purchase.type,
        }

        return { payment: paymentForSession, settings, recountedReceipt, fees };
    }

    async __getDataForPaymentUpdate ({ purchase_id = null, ...payment }) {
        const settings = await TeamsPaymentService.getSettings(
            payment.event_id, payment.master_club_id, payment.club_owner_id, payment.season, purchase_id
        );

        const recountedReceipt = TeamsPaymentService._recountPrices(
            settings, payment.receipt, payment.amount, payment.type
        );

        const fees = await TeamsPaymentService.getApplicationFees(
            Number(payment.event_id), settings, recountedReceipt, payment, null, payment.receipt
        );

        return { settings, recountedReceipt, fees };
    }

    __updatePaymentIntent (settings, payment, total, fees) {
        const { applicationFee, extraFee, swFee, swFeeDetails, providerFee } = fees;

        const takenFees = swFeeDetails.details;
        const netProfitSubtract = TeamsPaymentService.__getFeesToSubtractFromNetProfit__(settings, takenFees);

        if(settings.stripe_teams_fee_payer === FEE_PAYER.BUYER) {
            total += Number(takenFees.provider_fee);
        }

        const metadata = TeamsPaymentService.metadata.generate({
            user: payment.user,
            event_name: settings.event_name,
            event_id: payment.event_id,
            totals: {
                netProfit: swUtils.normalizeNumber(total - netProfitSubtract),
                stripeFee: providerFee,
                extraFee,
                sw_fee: swFee
            }
        });

        const paymentIntentData = {
            amount: swUtils.normalizeNumber(total * 100),
            application_fee_amount: swUtils.normalizeNumber(applicationFee * 100),
            metadata
        };

        return StripeService.paymentCard.stripeService.updatePaymentIntent(
            payment.payment_intent_id, paymentIntentData
        );
    }

    __updatePaymentHubOrder (settings, payment, total, fees) {
        const { applicationFee, extraFee, swFee, swFeeDetails, providerFee } = fees;

        const takenFees = swFeeDetails.details;
        const netProfitSubtract = TeamsPaymentService.__getFeesToSubtractFromNetProfit__(settings, takenFees);

        const metadata = TeamsPaymentService.metadata.generate({
            user: payment.user,
            event_name: settings.event_name,
            event_id: payment.event_id,
            totals: {
                netProfit: swUtils.normalizeNumber(total - netProfitSubtract),
                stripeFee: providerFee,
                extraFee,
                sw_fee: swFee
            }
        });

        if(settings.payment_hub_teams_fee_payer === FEE_PAYER.BUYER) {
            total += Number(takenFees.provider_fee);
        }

        const paymentOrderData = {
            paymentIntentId: payment.payment_hub_payment_intent_id,
            amount: swUtils.normalizeNumber(total),
            metadata,
            eventId: payment.event_id,
            marketplaceOrderFee: swUtils.normalizeNumber(applicationFee - providerFee), 
        };

        return PaymentHubService.updatePayment(paymentOrderData);

    }
}

module.exports = new UpdatePaymentService();
