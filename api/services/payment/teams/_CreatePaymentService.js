const {argv} = require('optimist');
const stripeSettingsRow = argv.prod ? 'stripe_connect' : 'stripe_connect_dev';

class CreatePaymentService {

    get ACH_DAYS_DELAY () {
        return 10;
    }

    async createPaymentHubPayment (eventId, amount, user) {
        const paymentHubCustomer = await PaymentHubService.getCustomer(user.user_id)

        const data = {
            amount,
            eventId,
            customerId: paymentHubCustomer.payment_hub_customer_id,
            marketplaceOrderFee: 0
        }

        return PaymentHubService.createPayment(data);
    }

    async createPayment (eventID, amount, user) {
        const stripeCustomerID = await StripeService.paymentCard.getStripeCustomerID(user.user_id);

        const paymentIntent = await this.__createPaymentIntent(eventID, amount, user, stripeCustomerID);
        const stripePublicKey = await this.__getPlatformPublicKey();

        return { paymentIntent, stripePublicKey };
    }

    async __createPaymentIntent (eventID, amount, user, stripeCustomerID) {
        const { statementDescriptor, accountID, paymentMethods } = await this.__getConnectedAccountData(eventID);

        const billingDetails = this.__prepareBillingDetails(user);

        return StripeService.paymentCard.stripeService.createPaymentIntentWithoutConfirm({
            statementDescriptor, accountID, amount, billingDetails, paymentMethods, stripeCustomerID
        });
    }

    async __getConnectedAccountData (eventID) {
        const query = knex('event AS e')
            .select({
                date_start: 'e.date_start',
                statementDescriptor: 'e.stripe_statement',
                accountID: 'sa.stripe_account_id',
                allowCardPayments: 'e.allow_card_payments',
                allowACHPayments: knex.raw(
                    `e.allow_ach_payments 
                        AND DATE_PART('day', e.date_start - (NOW() AT TIME ZONE e.timezone)::DATE) > ?`,
                this.ACH_DAYS_DELAY),
            })
            .join('stripe_account AS sa', 'sa.secret_key', 'e.stripe_teams_private_key')
            .where('e.event_id', eventID)
            .whereNull('e.deleted')
            .whereRaw(`COALESCE((e.teams_settings ->> 'use_payment_intents')::BOOLEAN, FALSE) IS TRUE`);

        let result = await Db.query(query).then(result => result?.rows?.[0]);

        if(_.isEmpty(result)) {
            throw { validation: `Event not found` };
        }

        const allowedPaymentMethods = this.__getAllowedPaymentMethodTypes(result);

        return {
            paymentMethods: allowedPaymentMethods,
            statementDescriptor: result.statementDescriptor,
            accountID: result.accountID
        }
    }

    __prepareBillingDetails (user) {
        return Object.assign({}, _.pick(user, 'email', 'phone_mob'), { name: `${user.first} ${user.last}` })
    }

    __getPlatformPublicKey () {
        return Db.query(
            knex('settings')
                .select({ public_key: knex.raw(`"value"->>'public_key'`) })
                .where('key', stripeSettingsRow)
        ).then(result => result?.rows?.[0]?.public_key);
    }

    __getAllowedPaymentMethodTypes (settings) {
        let methods = [];

        if(settings.allowCardPayments) {
            methods.push(StripeService.paymentCard.stripeService.PAYMENT_METHOD.CARD);
        }

        if(settings.allowACHPayments) {
            methods.push(StripeService.paymentCard.stripeService.PAYMENT_METHOD.ACH);
        }

        if(!methods.length) {
            throw { validation: 'no enabled online payment methods found' };
        }

        return methods;
    }
}

module.exports = new CreatePaymentService();
