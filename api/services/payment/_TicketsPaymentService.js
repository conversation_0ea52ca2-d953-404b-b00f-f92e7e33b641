'use strict';

const knex = require('knex')({ client: 'pg' });
const moment = require('moment-timezone');

const RefundTicketsService = require('./tickets/_RefundTicketsService');
const ParticipationService = require('./tickets/_ParticipationService');
const SettingsService = require('./tickets/_SettingsService');
const PaymentsListService = require('./tickets/_PaymentsListService');
const HistoryService = require('./tickets/_HistoryService');
const CancellationService = require('./tickets/_CancellationService');

const formatName = name => `${name.first} ${name.last}`;

class TicketsPaymentService {
    constructor () {}

    get cancellation () {
        return CancellationService;
    }

    get refunds () {
        return RefundTicketsService;
    }

    get participation () {
        return ParticipationService;
    }

    get settings() {
        return SettingsService;
    }

    get list () {
        return PaymentsListService;
    }

    get history() {
        return HistoryService;
    }

    getFullRefundedTickets({paymentID, ticketBarcode}) {
        let expr = squel.expr().and('pt.purchase_id = p.purchase_id');

        const query = squel.select()
            .field('SUM(pt.quantity)', 'quantity')
            .field('et.label')
            .from('purchase', 'p')
            .join('purchase_ticket', 'pt', expr)
            .join('event_ticket', 'et', 'et.event_ticket_id = pt.event_ticket_id')
            .group('et.label');

        if(paymentID) {
            query.where('p.linked_purchase_id = ?', paymentID);
        } else {
            query.where('p.ticket_barcode = ?', ticketBarcode);
        }

        return Db.query(query).then(({ rows }) => rows || []);
    }

    async editPayerInfo(eventID, ticketBarcode, info, user, canEditScannedTicketName = false) {
        let tr;
        try {
            tr = await Db.begin();
            const purchase = await this._getTicketInfo(tr, eventID, ticketBarcode);
            if(!purchase) {
                throw {validation: 'No Payment found'};
            }
            await this._saveTicketChanges(tr, purchase, info, user, canEditScannedTicketName);
            await tr.commit();
            return purchase.purchase_id
        }
        catch(err) {
            if(tr && !tr.isCommited) {
                tr.rollback();
            }
            throw err;
        }
    }

    async editPaymentInfo(ticketBarcode, info, user, canEditScannedTicketName = false) {
        let tr;
        try {
            tr = await Db.begin();
            
            const purchase = await this._getUserTicketInfo(tr, ticketBarcode);

            if(!purchase) {
                throw {validation: 'No Payment found'};
            }

            const newInfo = Object.assign({}, purchase.ticket, info);
            await this._saveTicketChanges(tr, purchase, newInfo, user, canEditScannedTicketName);

            await tr.commit();

            return {
                eventId: purchase.event.event_id,
                purchaseId: purchase.purchase_id
            }
        }
        catch(err) {
            if(tr && !tr.isCommited) {
                tr.rollback();
            }
            throw err;
        }
    }

    _getTicketInfoForUpdateQuery(ticketBarcode) {
        return knex('purchase AS p')
                .select(
                    'p.purchase_id',
                    {
                        ticket: knex.select(knex.raw('to_jsonb("ticket")'))
                            .from(function () {
                                this.select([
                                    'p.ticket_barcode',
                                    'p.first',
                                    'p.last',
                                    'p.email',
                                    'p.phone',
                                    knex.raw('TO_JSONB(COALESCE(p.tickets_additional, \'{}\')) as additional'),
                                    'p.zip',
                                    knex.raw('(pt.available = 0 AND p.scanned_at IS NOT NULL) AS is_scanned'),
                                ])
                                    .as('ticket')
                            }),
                        event: knex.select(knex.raw('to_jsonb("event")'))
                            .from(function () {
                                this.select([
                                    'e.event_id',
                                    'e.timezone',
                                ])
                                    .as('event')
                            })
                    },
                )
                .forUpdate('p')
                .innerJoin('event AS e', 'e.event_id', 'p.event_id')
                .innerJoin('purchase_ticket AS pt', 'pt.purchase_id', 'p.purchase_id')
                .where('p.ticket_barcode', ticketBarcode)
    }

    async _getTicketInfo(tr, eventID, ticketBarcode) {
        const query = this._getTicketInfoForUpdateQuery(ticketBarcode);

        query.andWhere('p.event_id', eventID);

        return tr.query(query)
            .then(r => r.rows[0]);
    }


    async _getUserTicketInfo(tr, ticketBarcode) {
        const query = this._getTicketInfoForUpdateQuery(ticketBarcode);

        return tr.query(query)
            .then(r => r.rows[0]);
    }

    async _saveTicketChanges(tr, purchase, newInfo, user, canEditScannedTicketName = false) {
        const { email, phone, zip, additional, first, last } = newInfo;

        const hasNameChanges = first || last;
        const isScanned = purchase.ticket.is_scanned;

        if (isScanned && hasNameChanges && !canEditScannedTicketName) {
            throw { validation: 'You cannot edit the name on a ticket that has already been scanned at entry.'}
        }

        let changes = {
            email,
            phone,
            zip,
            tickets_additional: knex.raw(
                `TO_JSONB(COALESCE(p.tickets_additional, '{}')) || ?::JSONB`,
                additional && JSON.stringify(additional)
            ),
            tickets_scan: this._formatTicketChangesHistoryQuery(purchase.event, user, purchase.ticket, newInfo),
            first,
            last,
        };

        await tr.query(
            knex('purchase AS p')
                .update(changes)
                .where('p.event_id', purchase.event.event_id)
                .where('p.ticket_barcode', purchase.ticket.ticket_barcode)
        );
    }

    _formatTicketChangesHistoryQuery(event, user, oldValues, newValues) {
        const dateOfChange = new Date();
        const ticketScanValues = [
            ['Email', oldValues.email, newValues.email],
            ['Phone', oldValues.phone, newValues.phone],
            ['Name', formatName(oldValues), formatName(newValues)],
            ['Zip', oldValues.zip, newValues.zip],
        ].map(
            ([target, oldValue, newValue]) => this.history.formatTicketChangeHistoryRow(target, oldValue, newValue, dateOfChange, event.timezone, user, 'EO acc')
        );
        return knex.raw(
            `concat_ws(chr(10), p.tickets_scan${', ?::TEXT'.repeat(ticketScanValues.length)})`,
            ticketScanValues
        );
    }

    async updateCovidTestValidation (eventID, ticketBarcode, reset) {
        let query =
            knex('purchase_ticket AS pt')
            .update({
                has_covid_test: reset ? null : knex.fn.now()
            })
            .where(
                'pt.purchase_id',
                '=',
                knex('purchase').select('purchase_id')
                    .where({
                        event_id: eventID,
                        ticket_barcode: ticketBarcode
                    })
            ).returning({
                timezone: knex.select('timezone').from('event').where('event_id', eventID),
                has_covid_test: knex.raw(`pt.has_covid_test::TIMESTAMPTZ`)
            });

        let { timezone, has_covid_test } = await Db.query(query).then(result => result && result.rows[0] || {});

        if(has_covid_test) {
            has_covid_test = moment(has_covid_test).utc().tz(timezone).format('MMM DD, YYYY HH:mm A');
        }

        return has_covid_test;
    }
}

module.exports = new TicketsPaymentService();
