const AbstractPaymentStrategyContext = require("../_AbstractPaymentStrategyContext");
const FailedPaymentStrategy = require("./_FailedPaymentStrategy");
const ProcessingPaymentStrategy = require("./_ProcessingPaymentStrategy");
const RequiresActionPaymentStrategy = require("./_RequiresActionPaymentStrategy");
const SucceededPaymentStrategy = require("./_SucceedPaymentStrategy");


class StripePaymentStrategyContext extends AbstractPaymentStrategyContext {
    constructor(sessionPayment, paymentData, webhookData) {
        super(sessionPayment, paymentData, webhookData)

        const paymentIntent = this.__getPaymentIntent();

        const paymentIntentStatus = this.__matchOldApiStatuses(paymentIntent);

        switch (paymentIntentStatus) {
            case 'succeeded':
                this.strategy = new SucceededPaymentStrategy(sessionPayment, paymentData, webhookData);
                break;
            case 'processing':
                this.strategy = new ProcessingPaymentStrategy(sessionPayment, paymentData, webhookData);
                break;
            case 'requires_action':
                this.strategy = new RequiresActionPaymentStrategy(sessionPayment, paymentData, webhookData);
                break;
            case 'requires_payment_method':
                this.strategy = new FailedPaymentStrategy(sessionPayment, paymentData, webhookData);
                break;
            default:
                throw new Error('Invalid payment intent status');
        }
    }

    get STATUS_MATCHES() {
        return {
            ['requires_source']: 'requires_payment_method',
            ['requires_source_action']: 'requires_action'
        }
    }

    __matchOldApiStatuses(paymentIntent) {
        let newStatus = this.STATUS_MATCHES[paymentIntent.status];

        return newStatus || paymentIntent.status;
    }

    __getPaymentIntent() {
        return this.webhookData.data.object;
    }
}

module.exports = StripePaymentStrategyContext
