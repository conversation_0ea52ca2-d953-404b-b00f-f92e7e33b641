'use strict';

const TextDecoder = require('util').TextDecoder;
const textDecoder = new TextDecoder('utf-8');

const METHODS_WITH_BODY = ['POST', 'PUT', 'DELETE'];

const HISTORY_ACTION_TYPE_MAP = {
    '/api/swt/ping'             : 'ping',
    '/api/swt/lookup'           : 'lookup',
    '/api/swt/test'             : 'test',
    '/api/swt/scan'             : getScanRouteHistoryActionType,
    '/api/swt/redeem'           : 'redeem',
    '/api/swt/recent'           : 'recent',
    '/api/swt/buy'              : 'buy',
    '/api/swt/events'           : 'events',
    '/api/swt/debug'            : 'debug',
    '/api/swt/log_error'        : 'logerror',
    '/api/swt/wristband'        : 'wristband',
    '/api/swt/pending/recent'   : 'pending.recent',
    '/api/swt/pending/void'     : 'pending.void',
    '/api/swt/pending/pay'      : 'pending.pay',
    '/api/kiosk/events/:event'  : 'kiosk.event',
    '/api/kiosk/buy'            : 'kiosk.buy',
}

const RESET_ACTION_TYPE     = 'reset';
const REENTRY_ACTION_TYPE   = 'reentry';
const SCAN_ACTION_TYPE      = 'scan';

function getScanRouteHistoryActionType (req) {
    if (_.isObject(req.body)) {
        if (req.body.hasOwnProperty(RESET_ACTION_TYPE) && 
            (req.body[RESET_ACTION_TYPE] === 'true' || req.body[RESET_ACTION_TYPE] === true)) {
            return RESET_ACTION_TYPE;
        }

        if (req.body.hasOwnProperty(REENTRY_ACTION_TYPE) && 
            (req.body[REENTRY_ACTION_TYPE] === 'true' || req.body[REENTRY_ACTION_TYPE] === true)) {
            return REENTRY_ACTION_TYPE;
        }
    }

    return SCAN_ACTION_TYPE;
}

function getHistoryActionType (req) {
    const path = req.route.path || req.path;

    let actionType = HISTORY_ACTION_TYPE_MAP[path];

    if (!actionType) {
        throw new Error(`Path "${req.path}" is not supported by SWT API History`)
    }

    if (_.isFunction(actionType)) {
        return actionType(req);
    }

    return actionType;
}

function getRequestBodyProps (body) {
    if (!_.isObject(body)) {
        throw new Error('Expecting body to be an object')
    }

    let data ={
        scanner_name        : body.scanner,
        scanner_location    : body.location,
        ticket_barcode      : body.ticket,
        event_id            : Number(body.event_id) || null
    }

    return data;
}

function prepareObjectPropForSaving (obj) {
    if (_.isObject(obj)) {
        /* Do not save empty objects to DB */
        if (_.isEmpty(obj)) {
            return null;
        } else {
            return JSON.stringify(obj);
        }
    } else {
        return obj;
    }
}

function saveHistoryDataToDB (data) {
    return Promise.resolve().then(() => {

        data.request_body       = prepareObjectPropForSaving(data.request_body);

        let query = 
            squel.insert().into('swt_api_history')
            .setFields(data)
            .returning('swt_api_history_id "id"');

        return Db.query(query).then(res => Number(res.rows[0].id) || null);
    });
}

function isSuccessfulOperation (statusCode, responseBody) {
    if (statusCode === 200) {
        /* Expecting a stringified JSON here */
        if (_.isString(responseBody)) {
            /* 
             * SWT API error response may contain "success": false while status is 200. 
             * Not every successful response has "success": true, so we don't check it.
             */
            return !responseBody.includes('"success": false');
        } else if (_.isObject(responseBody) && Object.hasOwnProperty('success')) {
            return (responseBody.success === true);
        } else {
            return true;
        }
    } else {
        return false;
    }
}

function updateHistoryRow (id, data) {
    return Promise.resolve().then(() => {

        data.response_body = prepareObjectPropForSaving(responseBodyDto(data.response_body));

        let query = 
            squel.update().table('swt_api_history')
            .setFields(data)
            .where('swt_api_history_id = ?', id)

        return Db.query(query).then(() => {})
    })
}


/* ======= Public API ======= */

module.exports.save = function saveSWTAPIHistoryRow (req) {
    return Promise.resolve().then(() => {

        if (!_.isObject(req)) {
            return Promise.reject(new Error('Expecting request to be an object'));
        }

        if (_.isEmpty(req)) {
            return Promise.reject(new Error('Empty request object passed'));
        }


        let ticket_barcode      = null,
            event_id            = null, 
            scanner_name        = null, 
            scanner_location    = null;

        let verb = req.method;

        let { body, query } = req;

        if (METHODS_WITH_BODY.includes(verb)) {
            query = null;

            ({ 
                ticket_barcode, event_id, 
                scanner_name, scanner_location } = getRequestBodyProps(body));

            body = {
                tickets: body.tickets
                    ?.filter(ticket => ticket.barcode && ticket.mark_as_scanned)
                    .map(ticket => _.pick(ticket, ['barcode', 'mark_as_scanned'])),
                mark_as_scanned: body.mark_as_scanned,
            }
        } else {
            body     = null;
            event_id = Number(req.query.event) || null;
        }
        

        let dataToSave = {
            request_url      : req.path,
            request_body     : body,

            ticket_barcode,
            event_id,
            scanner_name,
            scanner_location
        };

        return saveHistoryDataToDB(dataToSave);
    });
};

function responseBodyDto(responseBody) {
    const fieldsToKeep = ['success'];

    if (!responseBody) {
        return null;
    }
    // at least local server record logs in Uint8Array
    if (responseBody instanceof Uint8Array)  {
        try {
            responseBody = JSON.parse(textDecoder.decode(responseBody));
        } catch (e) {
            return null;
        }
    }

    // Ensure it's a JSON object
    if (typeof responseBody !== 'object' || responseBody === null) {
        return null;
    }

    return _.pick(responseBody, fieldsToKeep);
}

module.exports.update = function updateSWTAPIHistoryRow (historyRowID, req, res) {
    return Promise.resolve()
    .then(() => {
        if (!(Number.isInteger(historyRowID) && historyRowID >= 0)) {
            return Promise.reject(new Error('Expecting History Row ID to be an Integer >= 0'));
        }

        if (!_.isObject(req)) {
            return Promise.reject(new Error('Expecting request to be an object'));
        }

        if (!_.isObject(res)) {
            return Promise.reject(new Error('Expecting response to be an object'));
        }

        let dataToSave = {
            response_body           : responseBodyDto(res._responseBody),
            success                 : isSuccessfulOperation(res.statusCode, res._responseBody) 
        }

        return updateHistoryRow(historyRowID, dataToSave);
    });
}

module.exports.exportCsv = function exportScanHistory (eventID, scanLocation, timeGroup) {

    if(!eventID) {
        return Promise.reject(new Error('Event ID required'));
    }

    if(!scanLocation) {
        return Promise.reject(new Error('Scanner location required'));
    }

    let queryData =
        `SELECT
          TO_CHAR(
            TO_TIMESTAMP(FLOOR((EXTRACT('epoch' FROM sah.created) / (60 * $3))) * (60 * $3)) AT TIME ZONE e.timezone
          , 'DD/MM/YYYY HH24:MI') "created",
          COUNT(DISTINCT sah.ticket_barcode) "barcode"
        FROM swt_api_history sah
          LEFT JOIN event e
            ON e.event_id = sah.event_id
        WHERE sah.ticket_barcode IS NOT NULL
              AND sah.event_id = $1
              AND action_type = 'scan'
              AND sah.scanner_location = $2
              AND sah.success IS TRUE
        GROUP BY
          e.timezone,
          (TO_TIMESTAMP(FLOOR((EXTRACT('epoch' FROM sah.created) / (60 * $3))) * (60 * $3)) AT TIME ZONE e.timezone)`;

    return Db.query(queryData, [eventID, scanLocation, timeGroup]).then(result => result.rows);
}

