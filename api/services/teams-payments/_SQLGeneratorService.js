'use strict';

const { PAYMENT_SESSION_STATUS, PAYMENT_PROVIDER } = require("../../constants/payments");

class SQLGeneratorService {
	constructor (settingsRow, plaidSettingsRow, isProdMode, entryDeclinedStatus) {
		this._settingsRow 		= settingsRow;
		this._plaidSettingsRow 	= plaidSettingsRow;
		this._isProdMode 		= isProdMode;
		this._declined 			= entryDeclinedStatus;
	}

    get PLAID_SECRET_KEY_SQL () {
        return (
            `CASE 
                 WHEN e."allow_ach_payments" IS TRUE 
                 THEN (
                     CASE 
                         WHEN e."teams_use_connect" IS TRUE
                         THEN (
                             SELECT "value"->>'secret' 
                             FROM "settings" 
                             WHERE "key" = '${this._plaidSettingsRow}'
                         )
                         ELSE (e."plaid_teams_private_key")
                     END
                 )
                 ELSE ''
             END`
        );
    }

    get PLAID_PUBLIC_KEY_SQL () {
        return (
            `CASE 
				 WHEN e."allow_ach_payments" IS TRUE
                 THEN (
                     CASE 
                         WHEN e."teams_use_connect" IS TRUE
                         THEN (
                             SELECT "value"->>'public_key' 
                             FROM "settings" 
                             WHERE "key" = '${this._plaidSettingsRow}'
                         )
                         ELSE (e."plaid_teams_public_key")
                     END
                 )
                 ELSE ''
             END`
        );
    }

	get EVENT_SUBQUERY () {
		return (
			`(
			        SELECT ROW_TO_JSON("e")
			        FROM (
			            SELECT e.payment_name, e.payment_city, e.payment_address, e.payment_state, e.payment_zip,
			                (e.teams_settings->>'use_payment_intents')::BOOLEAN IS TRUE "use_payment_intents",
			                (e.teams_settings->>'use_payment_hub')::BOOLEAN IS TRUE "use_payment_hub",
			                (e.teams_settings->>'show_vertical_insurance_form')::BOOLEAN IS TRUE "show_vertical_insurance_form",
							(
								CASE
									WHEN ((e.teams_settings->>'use_payment_hub')::BOOLEAN IS TRUE)
										THEN '${PAYMENT_PROVIDER.PAYMENT_HUB}'
									ELSE '${PAYMENT_PROVIDER.STRIPE}'
								END
							) AS payment_provider,
			                e.teams_sw_fee_payer, e.stripe_teams_fee_payer, e.payment_hub_teams_fee_payer,
			                COALESCE(e."teams_entry_sw_fee", 0) "sw_fee",
			                (
								CASE 
									WHEN (e.ach_teams_percent > 0)
										THEN (e.ach_teams_percent / 100)
									ELSE 0
								END
							 ) "ach_percent", 
							 e.ach_teams_max_fee,
			                (e.stripe_teams_percent / 100) "stripe_percent", 
			                COALESCE(e.stripe_teams_fixed, 0)::REAL "stripe_fixed",
			                e.reg_fee, e.credit_surcharge "card_surcharge", e.ach_surcharge, (
			                CASE 
			                    WHEN e.allow_card_payments IS TRUE 
			                    THEN (
			                        CASE 
			                            WHEN e.teams_use_connect IS TRUE
			                            THEN (
			                                SELECT "value"->>'public_key' 
			                                FROM "settings" 
			                                WHERE "key" = '${this._settingsRow}'
			                            )
			                            ELSE (sa.public_key)
			                        END
			                    )
			                    ELSE ''
			                END 
			            ) "stripe_key", (
			                ${this.PLAID_PUBLIC_KEY_SQL} 
			            ) "plaid_key",
			            e.allow_check_payments "check", e.allow_card_payments, e.allow_ach_payments,
			            ${this._isProdMode} "prod_mode"
			        ) "e"
			    ) "event"`
		);
	}

	get RT_SUBQUERY () {
		return (
			`SELECT COALESCE(ARRAY_TO_JSON(ARRAY_AGG(ROW_TO_JSON(teams_list))), '[]'::JSON) 
			 FROM ( 
			     SELECT  
			         rt.roster_team_id,  
			         rt.team_name,  
			         rt.roster_club_id,  
			         d.credit_surcharge "div_credit_surcharge",
			         d.short_name "division_name",  
			         COALESCE(rt.discount, 0) "discount",
			         rt.status_entry,  
			         d.reg_fee "div_reg_fee",  
			         d.division_id,  
			         rt.status_paid,
			         COALESCE(SUM(pt.amount) FILTER ( WHERE pt.canceled IS NULL ), 0) "paid"
			     FROM roster_team "rt"  
			     INNER JOIN master_team mt 
			         ON mt.master_team_id = rt.master_team_id 
			         AND mt.season = $3 
			     LEFT JOIN division "d" 
			         ON (d.division_id = rt.division_id)  
			     LEFT JOIN purchase_team pt ON rt.roster_team_id = pt.roster_team_id AND rt.event_id = pt.event_id
                 LEFT JOIN purchase p ON p.purchase_id = pt.purchase_id    
			     WHERE rt.roster_club_id = rc.roster_club_id 
			         AND (rt.event_id = e.event_id) 
			         AND (rt.deleted IS NULL)  
			         AND (rt.status_paid != 24)  
					 AND (rt.status_paid != 22) 
					 AND (rt.status_paid != 26 OR (rt.status_paid = 26 AND p.dispute_status = 'lost'))
					 AND rt.status_entry != 11
			         AND mt.season = $3 
					 AND NOT EXISTS (
						SELECT 1 
						FROM payment_session ps
						INNER JOIN payment_session_team pst ON ps.payment_session_id = pst.payment_session_id
						WHERE pst.roster_team_id = rt.roster_team_id 
						  AND ps.status = '${PAYMENT_SESSION_STATUS.PENDING}'
					 )	
			     GROUP BY rt.roster_team_id, d.division_id    
			 ) teams_list`
		);
	}

	get PAYMENT_INFO_SUBQUERY () {
		return (
			`, ( 
			    SELECT ROW_TO_JSON("payment")
			    FROM (
			        SELECT 
			            TO_CHAR(p.created::TIMESTAMPTZ AT TIME ZONE e.timezone, 'Dy, Mon DD, YYYY HH12:MI:SS AM') "created",
			            p.type "payment_method"
			    ) "payment"
			) "payment"`
		);
	}

	get PAYMENT_JOIN_SQL () {
		return (
			`INNER JOIN "purchase" p 
        	 ON p.purchase_id = $4
        	 AND p.event_id = e.event_id`
		)
	}
    
    getPtSubquery (withDeclinedTeams) {
        
        return (
            `SELECT COALESCE(ARRAY_TO_JSON(ARRAY_AGG(ROW_TO_JSON(teams_list))), '[]'::JSON)
		     FROM (
		         SELECT
		             rt.roster_team_id,
		             rt.team_name,
		             rt.roster_club_id,
		             d.short_name "division_name",
		             COALESCE(rt.discount, 0) "discount",
		             rt.status_entry,
		             d.credit_surcharge "div_credit_surcharge",
		             d.reg_fee "div_reg_fee",
		             d.division_id,
		             rt.status_paid, (
		                 SELECT COALESCE(SUM("purchase_team".amount), 0)
		                 FROM "purchase_team"
		                 WHERE rt.roster_team_id = "purchase_team".roster_team_id
		                     AND "purchase_team".canceled IS NULL
		                     AND "purchase_team".purchase_team_id <> pt.purchase_team_id
                             AND "purchase_team".event_id = pt.event_id
		             ) "paid",
		             (rt."status_entry" = ${this._declined}) "is_declined"
		         FROM "roster_team" "rt"
		         INNER JOIN "master_team" mt
		             ON mt.master_team_id = rt.master_team_id
		             AND mt.season = $3
		         INNER JOIN "purchase_team" pt
		             ON pt.purchase_id = p.purchase_id
		             AND pt.canceled IS NULL
		             AND pt.roster_team_id = rt.roster_team_id
		         LEFT JOIN "division" "d"
		             ON (d.division_id = rt.division_id)
		         WHERE rt.roster_club_id = rc.roster_club_id
		             AND (rt.event_id = e.event_id)
		             AND (rt.deleted IS NULL)
		             ${
                        withDeclinedTeams
		                ? `AND mt.season = $3`
		                : `AND mt.season = $3 AND rt.status_entry != 11`
                     }
					 
		     ) teams_list`
        );
       
    }
    
	getSettingsSQLQuery (hasPurchaseID) {
		let sql = 
		    `SELECT
		         rc.roster_club_id, e.long_name "event_name",
		         e.teams_sw_fee_payer, e.stripe_teams_fee_payer, e.payment_hub_teams_fee_payer,
		         e.teams_use_connect "use_connect", e.stripe_statement,
		         e.extra_fee_collection_mode = 'auto' "collect_extra_fee",
				(
					CASE
						WHEN ((e.teams_settings->>'use_payment_hub')::BOOLEAN IS TRUE)
							THEN '${PAYMENT_PROVIDER.PAYMENT_HUB}'
						ELSE '${PAYMENT_PROVIDER.STRIPE}'
					END
				) AS payment_provider,
		         (e.teams_settings->>'do_not_collect_sw_fee')::BOOLEAN IS TRUE "do_not_collect_sw_fee",
		         e.allow_check_payments, e.allow_card_payments, e.allow_ach_payments, e.reg_fee, (
		            CASE
		                WHEN (e.stripe_teams_percent > 0)
		                    THEN (e.stripe_teams_percent / 100)
		                ELSE 0
		            END
		         )::NUMERIC "stripe_percent", e.stripe_teams_fixed "stripe_fixed", (
		            CASE 
		                WHEN (e.ach_teams_percent > 0)
		                    THEN (e.ach_teams_percent / 100)
		                ELSE 0
		            END
		         ) "ach_percent",
		         e.credit_surcharge "card_surcharge", e.ach_surcharge, (
		             CASE
		                 WHEN e.teams_use_connect IS TRUE 
		                     THEN NULL
		                 ELSE COALESCE(sa.secret_key, (
		                        SELECT "value"->>'secret_key' FROM "settings" 
		                        WHERE "key" = $5
		                    )
		                 )
		             END
		         ) "secret_key", sa.stripe_account_id "account_id", 
                 (${this.PLAID_SECRET_KEY_SQL}) "plaid_secret", 
                 (${this.PLAID_PUBLIC_KEY_SQL}) "plaid_public", 
                 e."plaid_teams_account_id" "plaid_account_id", 
		         (COALESCE(e.teams_sw_balance, 0) - COALESCE(e.teams_sw_target_balance, 0)) "balance", 
		         e.teams_sw_extra_fee "extra_fee", e.teams_entry_sw_fee "sw_fee", (
		             SELECT COALESCE(ARRAY_TO_JSON(ARRAY_AGG(ROW_TO_JSON("tl"))), '[]'::JSON) 
		             FROM ( 
		                 SELECT  
		                     d.short_name "division_name", d.division_id,
		                     rt.roster_team_id "id", rt.team_name "name", 
		                     COALESCE(rt.discount, 0) "discount",
		                     NULLIF(d.reg_fee, 0) "division_fee",
		                     d.credit_surcharge "division_surcharge",
		                     COALESCE(NULLIF(d.reg_fee, 0), e.reg_fee) "reg_fee",  
		                     rt.status_paid, rt.status_entry, (   
		                         SELECT COALESCE(SUM(pt.amount), 0)
		                         FROM "purchase_team" pt  
		                         INNER JOIN "purchase" p 
		                            ON p.purchase_id = pt.purchase_id
		                            AND (p.status <> 'canceled' OR p.status IS NULL)
		                            AND p.canceled_date IS NULL
		                         WHERE rt.roster_team_id = pt.roster_team_id  
		                             AND pt.canceled IS NULL 
		                             ${
		                                hasPurchaseID
		                                    ?'AND pt.purchase_id <> $7'
		                                     :''
		                             }
		                     ) "paid",
		                     e.teams_entry_sw_fee "sw_fee"
		                 FROM roster_team "rt"  
		                 INNER JOIN master_team mt 
		                     ON mt.master_team_id = rt.master_team_id 
		                     AND mt.master_club_id = mc.master_club_id
		                 LEFT JOIN division "d" 
		                     ON (d.division_id = rt.division_id)  
		                 WHERE rt.roster_club_id = rc.roster_club_id 
		                     AND (rt.event_id = e.event_id) 
		                     AND (rt.deleted IS NULL)  
		                     ${
		                        hasPurchaseID
		                            ?''
		                            :`AND (rt.status_paid != $7)  
		                              AND (rt.status_paid != $8)`
		                     }
		                     AND rt.status_entry != $6
		                     AND mt.season = $4
		             ) "tl"
		         ) "teams"
		     FROM "event" e 
		     LEFT JOIN stripe_account sa 
		         ON sa.secret_key = e.stripe_teams_private_key 
		     INNER JOIN "roster_club" rc 
		         ON rc.event_id = e.event_id 
		         AND rc.master_club_id = $2
		     INNER JOIN "master_club" mc 
		         on mc.master_club_id = rc.master_club_id
		         and mc.club_owner_id = $3
		     WHERE e."event_id" = $1`;

	    return sql;
	}

	getPaymentForTypeChangeSQLQuery () {
        
        let withDeclinedTeams = true;
		
        return (
			`SELECT
	            (${this.getPtSubquery(withDeclinedTeams)}) "teams",
	            p.type
	         FROM "purchase" p 
	         INNER JOIN "event" e 
	            ON e.event_id = p.event_id
	         INNER JOIN roster_club rc 
	            ON rc.event_id = e.event_id 
	            AND rc.master_club_id = $4
	         WHERE p.event_id = $1
	            AND p.purchase_id = $2
	            AND p.status <> 'canceled'
	            AND p.canceled_date IS NULL`
		)
  
	}

	getPaymentDataSQLQuery (hasPurchaseID) {
        
        let withDeclinedTeams = false;
        
		return (
	        `SELECT ( 
	            ${hasPurchaseID?this.getPtSubquery(withDeclinedTeams):this.RT_SUBQUERY}
	            ) "teams",
	            ${this.EVENT_SUBQUERY}
	            ${hasPurchaseID?this.PAYMENT_INFO_SUBQUERY:''}
	        FROM "event" e 
	        LEFT JOIN roster_club rc 
	            ON rc.event_id = e.event_id 
	            AND rc.master_club_id = $2 
	        LEFT JOIN "stripe_account" sa 
	            ON sa.secret_key = e.stripe_teams_private_key
	        ${hasPurchaseID?this.PAYMENT_JOIN_SQL:''}
	        WHERE e.event_id = $1`
	    );
     
	}

	getClubPaymentsListSQLQuery () {
		return (
            `SELECT (
                 SELECT
                     COALESCE(ARRAY_TO_JSON(ARRAY_AGG(ROW_TO_JSON("payments"))), '[]'::JSON)
                 FROM (
                     SELECT  
                          p.purchase_id "id", p.amount, p.date_paid, p.created, p.received_date, p.status,  
                          p.canceled_date, rt.status_paid::INTEGER, p.check_num, 
                          COALESCE(SUM(pt.surcharge), 0) "surcharge", COALESCE(SUM(rt.discount), 0) "total_discount",    
                          p.type, COUNT(pt.purchase_team_id) "teams", ( 
                              SELECT SUM(COALESCE(NULLIF(pst.division_fee,0), pst.event_fee)) 
                              FROM purchase_team pst  
                              WHERE pst.purchase_id = p.purchase_id  
                                  AND pst.canceled IS NULL 
                          ) "due"   
                      FROM "purchase" p   
                      LEFT JOIN "purchase_team" pt   
                          ON p.purchase_id = pt.purchase_id   
                          AND pt.canceled IS NULL 
                      LEFT JOIN "roster_team" rt   
                          ON pt.roster_team_id = rt.roster_team_id
                      INNER JOIN "roster_club" rc 
                          ON rc.master_club_id = $2
                          AND rc.roster_club_id = p.roster_club_id
                          AND rc.event_id = p.event_id
                      WHERE p.event_id = e.event_id
                      GROUP BY p.purchase_id, p.amount, p.date_paid, p.created,  
                          p.received_date, p.status, p.type, p.canceled_date,  
                          p.check_num, rt.status_paid
                      ORDER BY p.created DESC
                 ) "payments"
              ) "payments",
             ${this.EVENT_SUBQUERY}
             FROM "event" e 
             LEFT JOIN "stripe_account" sa 
                 ON sa.secret_key = e.stripe_teams_private_key
             WHERE e.event_id = $1`
		);
	}
}

module.exports = SQLGeneratorService;
