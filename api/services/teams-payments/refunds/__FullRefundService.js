'use strict';
const { PAYMENT_PROVIDER } = require('../../../constants/payments');
const teamsConstants = require('../../../constants/teams');

class FullRefundService {
    constructor (parent) {
        this.__parent   = parent;
        this._Utils     = parent.utils;
    }

    //covered 😄👍
    async proceed (purchaseID, userID) {
        if(!purchaseID) {
            throw { validation: 'No payment identifier specified' };
        }

        let payment = await this.__getPaymentData(purchaseID);

        return this.__doRefund(purchaseID, payment, userID);
    }

    __getPaymentData (purchaseID) {
        let query =
            ` SELECT  
                 p.amount,
                 p.stripe_charge_id "charge", 
                 sc.stripe_payment_id,
                 e.stripe_statement, p.amount,
                 sa.stripe_account_id,
                 p."stripe_payment_type",
                 COALESCE(p."additional_fee_amount", 0) "additional_fee_amount", 
                 COALESCE(p."collected_sw_fee", 0) "collected_sw_fee",
                 p.event_id, (
                     CASE
                         WHEN (p."stripe_payment_type" = 'connect')
                             THEN e."stripe_teams_private_key"
                         ELSE COALESCE(
                            e."stripe_teams_private_key", (
                                SELECT "value"->>'secret_key' FROM "settings" 
                                WHERE "key" = '${this.__parent.STRIPE_SETTINGS_KEY}'
                            )
                         )
                     END
                ) "stripe_secret",
                COALESCE(p.payment_provider, '${PAYMENT_PROVIDER.STRIPE}') "payment_provider",
                php.payment_id "payment_hub_payment_id"
             FROM purchase p  
             LEFT JOIN "event" e 
                 ON e.event_id = p.event_id
             LEFT JOIN "stripe_charge" sc 
                ON sc.stripe_charge_id = p.stripe_charge_id
             LEFT JOIN "stripe_account" sa
                ON e."stripe_teams_private_key" = sa."secret_key"
             LEFT JOIN payment_hub."payment" php 
                ON php.payment_intent_id::TEXT = p.payment_hub_payment_intent_id
             WHERE p.purchase_id = $1 
                 AND p.type IN ('card', 'ach')
                 AND (p."status" = 'pending' AND p."type" = 'ach') IS FALSE
                 AND p."status" <> 'canceled'
                 AND p."payment_for" = 'teams'`;

        return Db.query(query, [purchaseID]).then(function (result) {
            let paymentData = _.first(result.rows);
            if(_.isEmpty(paymentData)) {
                throw ({ validation: 'No payment found' });
            } else {
                return paymentData;
            }
        })
    }

    async __doRefund (purchaseID, payment, userID) {
        let tr = await Db.begin();

        try {
            let purchaseUpdated = await this.__updPurchase__(purchaseID, payment, tr);

            if(!purchaseUpdated) {
                throw { validation: 'Purchase not found' };
            }

            let updatedTeamIDs  = await (this.__updatePurchaseTeamRow(purchaseID, tr));
            let refundedTeams   = await this.__updateRosterTeams(updatedTeamIDs, tr);

            await this.__updEscrow(payment, tr);

            await this.__makeRefund(payment);

            await this.__updateMetadata__(payment);
    
            await this.__parent.saveRefundHistory({tr, purchaseID, userID, isPartial: false });
            
            await tr.commit();

            return refundedTeams;
        } catch (err) {
            if(tr && !tr.isCommited) {
                await tr.rollback();
            }
            throw err;
        }
    }

    __updPurchase__ (purchaseID, payment, tr) {
        let query =
            squel.update().table('purchase')
                .set('date_refunded = NOW()')
                .set('amount_refunded = COALESCE("amount_refunded", 0) + "amount"')
                .set(`status = 'canceled'`)
                .set('canceled_date = NOW()')
                .where('purchase_id = ?', purchaseID)
                .where('event_id = ?', payment.event_id);

        return tr.query(query).then(result => result.rowCount > 0);
    }

    async __updatePurchaseTeamRow (purchaseID, tr) {
        const query = knex('purchase_team')
            .update({
                'canceled': knex.fn.now(),
            })
            .where({
                purchase_id: purchaseID,
                canceled: null
            })
            .returning('roster_team_id');

        let updatedTeams = await tr.query(query).then(result => result.rows);

        return this._Utils.numArrayToString(updatedTeams, 'roster_team_id');
    }

    async __updateRosterTeams (updatedTeamIDs, tr) {

        if(_.isEmpty(updatedTeamIDs)) {
            return Promise.resolve([]);
        }

        let query = squel.update().table('roster_team')
            .set('status_paid', teamsConstants.PAYMENT_STATUSES.NOT_PAID)
            .set('date_paid', null)
            .set('discount', null)
            .where(`roster_team_id IN(${updatedTeamIDs})`)
            .returning('team_name, roster_team_id');

        return tr.query(query).then(result => result.rows);
    }

    __updEscrow (payment, tr) {
        let swFee = (
            parseFloat(payment.additional_fee_amount) + parseFloat(payment.collected_sw_fee)
        );

        /* We need to decrease amount, so we multiply by -1 to make sw fee negative */
        swFee = this._Utils.normalizeNumber(swFee * -1);

        if (swFee < 0) {
            return TeamsPaymentService
                .modifyCollectedEscrowSWFeeAmount(tr, payment.event_id, swFee)
                .catch(err => loggers.errors_log.error(err))
        } else {
            return Promise.resolve();
        }
    }

    async __makeRefund (payment) {
        if(payment.payment_provider === PAYMENT_PROVIDER.PAYMENT_HUB) {
            return this.__makePaymentHubRefund(payment);
        }

        return this.__makeStripeRefund(payment)
    }

    async __makePaymentHubRefund(payment) {
        await PaymentHubService.createRefund({
            paymentHubPaymentId: payment.payment_hub_payment_id,
            amount: payment.amount,
            paymentAmount: payment.amount,
            marketplaceOrderFee: Number(payment.collected_sw_fee) + Number(payment.additional_fee_amount)
        })
    }

    async __makeStripeRefund(payment) {
        let useConnect = (payment.stripe_payment_type === 'connect');

        return this.__parent.makeStripeRefund({
            charge_id       : payment.charge,
            stripe_secret   : useConnect?null:payment.stripe_secret
        }, useConnect, true)
    }

    async __updateMetadata__ (payment) {
        if(payment.payment_provider === PAYMENT_PROVIDER.PAYMENT_HUB) {
            return;
        }

        return this.__updateStripeMetadata(payment);
    }

    async __updateStripeMetadata(payment) {
        let paymentData = {
            charge_id: payment.charge,
            ..._.pick(payment, ['stripe_payment_id', 'stripe_account_id'])
        };

        return StripeService.updateChargeMetadataAfterRefund(paymentData, {
            stripeFee: 0,
            swFee: 0,
            extraFee: 0,
            netProfit: 0
        })
    }
}

module.exports = FullRefundService;
