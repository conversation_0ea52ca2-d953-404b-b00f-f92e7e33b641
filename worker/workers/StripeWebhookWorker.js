const AbstractWorker = require('../AbstractWorker');
const { STRIPE_WEBHOOK_QUEUE } = require('../../api/constants/workers-queue');
const {CLUB_INVOICE} = require("../../api/constants/stripe/webhook-purchase-types");

class StripeWebhookWorker extends AbstractWorker {
    static queueName() {
        return STRIPE_WEBHOOK_QUEUE;
    }
    
    async doJob(job) {
        const webhookData = job.data;
        const eventType = webhookData.type;

        switch (eventType) {
            // Occurs whenever a new charge is created and is successful.
            case 'charge.succeeded':
                await StripeService.webhook.charge.handleSuccessCharge(webhookData);
                break;
            // Occurs whenever a charge is refunded, including partial refunds.
            case 'charge.refunded':
                await StripeService.webhook.refund.process(webhookData);
                break;
            // Occurs when a PaymentIntent transitions to requires_action state
            case 'payment_intent.requires_action':
            // Occurs when a PaymentIntent has successfully completed payment
            case 'payment_intent.succeeded':
            // Occurs when a PaymentIntent has started processing
            case 'payment_intent.processing':
            // Occurs when a PaymentIntent has failed the attempt to create a payment method or a payment.
            case 'payment_intent.payment_failed':
                const { metadata } = webhookData.data.object;

                if (metadata.purchase_type === 'custom') {
                    await EventPaymentMethodService.customPayment.webhook.process(webhookData);
                } else if (metadata.purchase_type === CLUB_INVOICE) {
                    await ClubInvoiceService.payment.webhook.process(
                        webhookData
                    );
                } else {
                    await PaymentService.teams.processPaymentWebhook(webhookData);
                }
                break;
            // Occurs whenever a customer disputes a charge with their bank (chargeback)
            case 'charge.dispute.created':
            // Occurs when the dispute is resolved and the dispute status changes to won or lost
            case 'charge.dispute.closed':
            // Occurs when funds are reinstated to your account after a dispute is won
            case 'charge.dispute.funds_reinstated':
            // Occurs when funds are removed from your account due to a dispute
            case 'charge.dispute.funds_withdrawn':
            // Occurs when the dispute is updated (usually with evidence)
            case 'charge.dispute.updated':
                await StripeService.webhook.dispute.process(webhookData);
                break;
            // Occurs when payment method is detached
            case 'payment_method.detached':
                await StripeService.webhook.paymentMethod.process(webhookData);
                break;
            /**
             * Occurs whenever a sent transfer is expected to be available
             * in the destination bank account. Note to Connect users: this event
             * is only created for transfers from your connected Stripe accounts to their
             * bank accounts, not for transfers to the connected accounts themselves
             **/
            case 'transfer.created':
                const stripeTransferType = webhookData?.data?.object?.type;

                if(stripeTransferType !== 'bank_account') {
                    await StripeService.webhook.transfer.handleCreatedTransfer(webhookData);
                }
                break;
            case 'transfer.paid':
            /**
             * transfers to bank accounts are called "payouts" in the newer Stripe API versions
             */
            case 'payout.paid':
                /**
                 * STRIPE API CHANGES: https://stripe.com/docs/upgrades#2017-05-25
                 *
                 * The "user_id" field of Connect-related event objects (and webhooks)
                 * has been renamed to "account"
                 *
                 * @type {String}
                 */
                let stripeAccID      = webhookData.user_id || webhookData.account;
                let stripeTransfer   = webhookData.data.object;
                let type             = stripeTransfer && stripeTransfer.type;
                let apiVer           = webhookData.api_version;

                if (!stripeAccID) {
                    loggers.errors_log.error(`"${eventType}" without "account_id"`);
                    break;
                }

                await ((type === 'bank_account')
                    ? StripeService.payouts.processStripePayout(stripeAccID, stripeTransfer, apiVer)
                    : Promise.resolve());

                break;
            case 'payout.canceled':
                // Occurs whenever a payout canceled from dashboard
                let stripeID    = webhookData.user_id || webhookData.account;
                let transferID  = webhookData.data.object.id;

                const payoutUpdated = await StripeService.payouts.cancelPayout(stripeID, transferID);
                if(!payoutUpdated) {
                    loggers.errors_log.error('Payout not found: ' + transferID);
                }
                break;
        }
    }
}

module.exports = StripeWebhookWorker;
