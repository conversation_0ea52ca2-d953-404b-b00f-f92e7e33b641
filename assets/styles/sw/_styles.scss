@import "globals.scss";
@import "stripe.scss";

html {
  min-height: 100%;
  position: relative;
}

body {
  margin-bottom: 60px;
}

/* Need this classes to make ng-if ng-show with ngAnimation turned on not to delay */
.ng-hide.ng-hide-animate {
    display: none !important;
    transition: display 0;
}

.ng-animate {
    animation: none 0s;
}

.no-outline {
    outline: none !important;
}

.no-decoration {
    text-decoration: none;
}

.no-decoration:hover {
    text-decoration: none;
}

.font-bold {
  font-weight: bold;
}

.athletes-table thead th {
    text-align: left;
    white-space: nowrap;
}

.athlete-contacts p {
    margin: 0 0 5px !important;
}

.dl-club-menu dt {
    width: 145px !important;
}

.dl-club-menu dd {
    text-overflow: ellipsis;
    white-space: nowrap;
    &::after {
        content: "\00a0";
    }
}

.inline-edit {
  .icon {
    margin-left: 3px;
    visibility: hidden;
  }
  &:hover {
    .icon {
      visibility: visible;
    }
  }
}

.popover-content-impr {
  padding: 9px 3px !important;
  overflow: hidden;
}

.gl-success {
    color: #3c763d !important;
}

.gl-danger {
    color: #a94442 !important;
}

.gl-info {
    color: #31708f !important;
}

.crossed {
  text-decoration: line-through !important;
}

.popover--wide {
  width: 320px;
}

.popover-no-max-width {
    max-width: none !important;
}

.birthdate-disabled {
    input.white-ro[readonly] {
        background-color: #EEEEEE !important;
        cursor: not-allowed !important;
    }
}

.table-extra-small {
  tbody {
      tr {
          td {
              padding: 4px !important;
              font-size: 0.8em !important;
          }
      }
  }
}

.badge-info {
    background-color: #83CFF5 !important;
}

.page-break {
    display: block;
    page-break-before: always;
}

.over-vis {
    overflow: visible !important;
}

.white-space-normal {
  white-space: normal;
}

.overflow-text {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.display-flex {
    display: flex;
}

.double-row-table {
    tbody {
        tr:nth-child(odd) {
            td {
                border-top: 2px solid #ddd !important;
            }
        }
    }
}

.spacer-sm-t {margin-top: 10px;}
.spacer-sm-r {margin-right: 10px;}
.spacer-sm-b {margin-bottom: 10px;}
.spacer-sm-l {margin-left: 10px;}
.spacer-xs-l {margin-left: 5px !important;}

.spacer-md-t {margin-top: 15px;}
.spacer-md-r {margin-right: 15px;}
.spacer-md-b {margin-bottom: 15px;}
.spacer-md-l {margin-left: 15px;}

.spacer-lg-t {margin-top: 20px;}
.spacer-lg-r {margin-right: 20px;}
.spacer-lg-b {margin-bottom: 20px;}
.spacer-lg-l {margin-left: 20px;}

@media only screen and (max-width: 1199px) {
  .spacer-mobile-sm-t {margin-top: 10px;}
  .spacer-mobile-sm-r {margin-right: 10px;}
  .spacer-mobile-sm-b {margin-bottom: 10px;}
  .spacer-mobile-sm-l {margin-left: 10px;}

  .spacer-mobile-md-t {margin-top: 15px;}
  .spacer-mobile-md-r {margin-right: 15px;}
  .spacer-mobile-md-b {margin-bottom: 15px;}
  .spacer-mobile-md-l {margin-left: 15px;}

  .spacer-mobile-lg-t {margin-top: 20px;}
  .spacer-mobile-lg-r {margin-right: 20px;}
  .spacer-mobile-lg-b {margin-bottom: 20px;}
  .spacer-mobile-lg-l {margin-left: 20px;}
}

@media only screen and (max-width: 992px) {
  .spacer-mid-sm-t {margin-top: 10px}
}

.form-control-4digits {
    width: 55px !important;
}

.alert.alert-sm {
  padding: 5px;
  margin-bottom: 10px;
}
.form-control-static {
    word-break: break-all;
}

.form-control-static {
    word-break: break-all;
}

.form-control-input-small, .form-control-select-small, .form-control-4digits, .form-control-select-small--59,
 .form-control-select-small--162, .btn-short, .form-control-select-small--65, .form-control-select-small--90,
 .select-sm, .addon-sm {
    height: 23px;
    padding: 2px 3px;
}

.btn-short {
  padding-top: 1px !important;
  padding-bottom: 1px !important;
}

.form-control-4digits, .form-control-input-small, .addon-sm {
    padding: 3px !important;
}

.form-control-input-small {
    width: 33px !important;
}

.sw-input-group-sm {
  width: 61px;
  .input-group-addon {
    padding-top: 0;
    padding-bottom: 0;
  }
  select {
    border: 0;
    outline: 1px solid #CCC;
    outline-offset: -1px;
    background-color: white;
    box-shadow: none;
  }
}

.sw-btn-group-sm {
  .btn {
    padding: 1px 7px 1px 7px;
  }
  .addon {
    background-color: #eee;
    color: #666;
    padding-left: 11px;
    padding-right: 11px;
  }
}

.form-control-select-small {
    width: 65px !important;
    padding: 1px;
}

.form-control-select-small--59 {
    width: 59px !important;
    padding: 1px;
}

.form-control-select-small--65 {
    width: 65px !important;
    padding: 1px;
}

.form-control-select-small--90 {
    width: 90px !important;
    padding: 1px;
}

.form-control-select-small--162 {
    width: 162px !important;
    padding: 1px;
}

.btn-relative-block {
  position: relative;
  display: block;
  width: 100%;
  .dropdown-menu {
    width: 100%;
  }
}

.no-margin {
    margin: 0;
}

.col-sm-checkbox {
    width: 3%;
}

.col-sm-q {
    width: 5%;
}

.table-condensed--sm>tbody>tr>td {
  padding: 1px !important;
}

.no-border td {
  border: 0 !important;
}

.badge-dark {
    background-color: #999 !important;
}

.list-item-selected {
    background-color: #bebebe !important;
}

.text-grey {
    color: #999 !important;
}

.text-dark {
    color: #333 !important;
}

.large-drop-down-menu {
    min-width: 450px !important;
}

.dropdown-team-filter {
    width: 400px;
}

.selectable-dd {
    width: 200px;
}

.dropdown-team-filter, .selectable-dd {
  padding: 0;

  .list-group {
    margin: -1px;
  }

  .col-1,
  .col-2,
  .col-3 {
    float: left;
    margin-left: 10px;
  }

  .col-1 {
    width: 15px;
    margin-left: 0;
  }

  .col-2 { width: 15px; }
  .col-3 { width: 110px; }
  .col-3-1 { width: 200px; float: left; overflow: hidden; }
  .col-4 { overflow: hidden; float: left; }
}

.input-group-addon-sm {
    padding-left: 9px;
    padding-right: 9px;
}

.date-input {
    display: inline-table !important;
}

.text-link {
  color: $link-color;
  &:hover {
    color: $link-hover-color;
  }
}

.pointer {
  cursor: pointer !important;
}

.navbar {
  background-image: linear-gradient(#ffffff, #eeeeee 50%, #e4e4e4);
  border: 1px solid #d5d5d5;
  min-height: 66px;
  border-radius:0 !important;

  >li {
  	a, p {
  		line-height: 65px;
  		padding-top: 0;
  		padding-bottom: 0;
  	}
    a.lh--small {
      line-height: 22px !important;
    }
  }
  .navbar-nav {
    >li {
      >a {
        @media (min-width: 768px) {
          padding-top: 22px;
          padding-bottom: 22px;
        }
      }
    }
  }
}

label.no-p {
    padding: 0 !important;
}

.inline-block {
    display: inline-block;
}

.form-login__checkbox {
    margin-left: 20px;
}

.form-login-email.invalid,
.form-login-password.invalid {
    background-color: rgba(255, 0, 12, 0.44);
}

.form-login .label.error {
    background-color: rgba(255, 0, 12, 0.44);
}

.required::before{
  color: blue;
  content: '*';
  margin-right: 5px;
}

.divisions--border {
    border-right: 1px solid $divisions-create-form-border-color;
    border-left: 1px solid $divisions-create-form-border-color;
}

.block-height-min {
    min-height: 550px;
}
.block-height-min.remove-min-height {
    min-height: 0;
}

.search-box,
.my-modal-body,
.repeated-item.ng-enter.ng-enter-active,
.repeated-item.ng-move.ng-move-active,
.repeated-item.ng-leave,
.relative {
  position: relative;
}

.table-title {
  font-weight: bold;
}
.controls {
  padding-bottom: 10px;
}

.search-box span {
  position: absolute;
  top: 10px;
  left: 4px;
}
.search-box input {
  padding-left: 19px;
}

.top-space {
  margin-top: 10px;
}

.big-icon {
  font-size: 18px !important;
  color: #333;
}

.glyphicon, .fa-chevron-up, .fa-chevron-down {
  color: $icons-blue-color;
}
.btn-primary .glyphicon {
  color: #fff;
}
.my-modal-body {
  padding: 20px;
}
.larger {
  width: 640px;
}
.lead {
  margin-bottom: 10px;
}

.green-check, .date-time-modal {
  height: $bootstrap-line-height;
}

.date-time-modal, .wrong-email-msg, .center-form-text, .green-check {
  line-height: $bootstrap-line-height;
}

.green-check {
  color: green;
  margin-left: 3px;
}

.w90 {
  width: 90% !important;
  display: inline-block;
}

.wrong-email-msg {
  color: rgb(255, 0, 12);
}

.panel-no-margins {
  .panel-group {
    margin-bottom: 0;
    .payments-list--sm {
      margin-bottom: 0 !important;
    }
  }
}

.team-staffers-list-wrapper {
    margin-top: -15px !important;
    margin-bottom: -15px !important;
}

.alert-sm {
  padding: 2px !important;
  margin-bottom: 0;
}

.center-form-elem-v {
  margin-top: 5px;
}

.payments-list--sm {
    margin-bottom: 10px !important;

    .panel-heading {
        padding: 5px 5px !important;

        a:hover {
            cursor: pointer;
        }

        .panel-title {
            font-size: 12px !important;
            line-height: 1 !important;
        }
    }
}

.teams-table tbody td,
.events-table tbody td,
.master-table tbody td,
.big-icon,
.sw-fort-icon,
.status-column {
  cursor: pointer;
}

.teams-table thead, .teams-table tbody {
  border: 0 !important;
}

.teams-table thead, .table thead {
  border-bottom: 1px solid #ddd !important;
  background: #fff;
}

.vertical-text-alignment tbody td{
  vertical-align: middle !important;
}

.entered-team {
  background-color: rgb(112, 248, 86) !important;
}
.rowm0 {
  margin-left: 0;
  margin-right: 0;
}
.row-space {
  margin-top: 10px;
  margin-bottom: 10px;

  @media only screen and (max-width : 991px) {
    [class^="col-md-"] {
      padding-top: 3px;
      padding-bottom: 3px;
    }

    [class^="col-md-"]:first-child {
      padding-top: 0;
    }

    [class^="col-md-"]:last-child {
      padding-bottom: 0;
    }
  }
}

.row-space--small {
    margin-bottom: $divisions-create-form-row-bottom-space;
}

.input-inline {
    display: inline-block !important;
}

span.m--r {
    margin-right: 7px;
}

.hrm0 {
  margin: 0;
}
.notification-holder{
  position: fixed;
  z-index: 9999;
  top: 20px;
  right: 20px;
  width: 280px;
  .alert {
    margin: 20px 0 0;
    &:first-child {
      margin: 0;
    }
  }
}

.alert-disabled {
  color: #777;
  background-color: #f5f5f5;
  border-color: #e4e4e4;
}

@media (max-width: 767px) {
  .notification-holder{
    width: 350px;
  }
}

@media (max-width: 479px) {
  .notification-holder{
    width: 300px;
    top: auto;
    bottom: 20px;
    right: 10px;
  }
}

#teamsModal .radio-inline {
  height: 20px;
}

.edit-icon {
  line-height: 38px;
}
.payment-table td {
  vertical-align: middle !important;
  padding: 1px !important;
}
.black {
  color: black;
}
.yellow {
  color: rgb(248, 193, 50);
}

.yellow-waiting {
    color: rgb(255, 153, 0);
}
.yellow-faulty {
    color: #FFD966;
}
.green {
  color: green;
}
.blue {
    color: #294FE7;
}
.red {
    color: red;
}
.bg-amount {
    background-color: #eee;
}

.bg-amount:hover {
    background-color: #eee !important;
}

.padding-sm {
  padding-left: 3px;
  padding-right: 3px;
}
.datepicker.dropdown-menu {
  z-index: 3000 !important;
}
.sortable-item {

}
.sortable-item, .sortable-placeholder {
  padding: 5px 7px !important;
  border-radius: 10px !important;
}
.athletes-list {
  min-height: 20px;
  padding-left: 0;
  padding-right: 0;
}

.roster-item {
  background-color: #BAFFAC !important;
}

.athletes-item {
  background-color: #BCBCBC !important;
}

.col-sm-1-5 {
  width: 9% !important;
}

.col-sm-1-3 {
    width: 7% !important;
    text-align: right !important;
}

.bookmark {

}

.padding-sm-vertical {
  padding-top: 1px;
  padding-bottom: 1px;
  height: 22px;
}

.team-entered, .head-official {
    background-color: #dff0d8;
    border-color: #d6e9c6;
    color: #3c763d;
}
.team-deleted {
    background-color: #fcf8e3;
    border-color: #faebcc;
    color: #8a6d3b;
}
.team-not-entered, .athlete-not-in-team {
    background-color: #f0f0f0;
    border-color: #444;
    color: #5a5a5a;
}

.roster-managing-table {
  border-collapse:separate;
}

.roster-managing-table thead {
  background-color: #C7D8FF;
  border: 1px solid #6B73C5;
}

.roster-managing-table thead tr {

}

.roster-managing-table thead tr th:first-child {
  border-radius: 10px 0 0 0;
}

.roster-managing-table thead tr th:last-child {
  border-radius: 0 10px 0 0;
}

.white {
  background-color: white;
  color: black;
}

.violet {
  color: rgb(177, 30, 246) !important;
}

.notification {
  position: fixed;
  right: 10px !important;
  top: -100px;
  z-index: 999999;
  width: 250px;
  min-height: 50px;
  border-radius: 10px;
  padding: 10px;
  text-align: center;
}

.notification.show {
  top: 10px !important;
  @include animation(0.7s, notif);
}

.notification.hide {
  @include animation(0.7s, notif_hide);
}

.italic {
  font-style: italic !important;
}

.table td.resetpadding {
  padding: 0 !important;
}

.pending, .accepted, .waiting, .not-accepted {
  border-left: 4px solid;
  box-sizing: border-box;
}

.pending {
  border-color: $entry-pending-color;
}

.accepted {
  border-color: $entry-accepted-color;
}

.not-accepted {
  border-color: $entry-not-accepted-color;
}

.waiting {
  border-color: $entry-waiting-color;
}

.event-managing-container {
  width: 1268px !important;
}

.club-menu-container {
  // width: 1300px !important;
}

form.submitted .ng-invalid {
  border: 1px solid #a94442
}

.male {
    background-color: $male-color-bg;
}

.female {
    background-color: $female-color-bg;
}

.coed {
    background-color: $coed-color-bg;
}

.static-textpage {
  font: 18px/21px Georgia,'Times New Roman', Times, serif;
  padding-top: 30px;
  p {
    margin: 0 0 21px;
  }
  h3 {
    margin: 0 0 30px;
  }
}

.validation-valid,
.validation-invalid {
  display: inline-block;
  vertical-align: top;
  padding: 4px 0 0;
}
.validation-valid {
  color: #5cb85c;
}
.validation-invalid {
  color: #a94442;
}

form[name="assign_doubles"] .ng-invalid.ng-dirty {
  border: 1px solid #a94442;
}

form[name="assign_doubles"] .ng-valid.ng-dirty {
  border: 1px solid #5cb85c;
}

.manage-teams>tbody>tr>td {
    vertical-align: middle !important;
}

.validation-required {
  >label {
    position: relative;
    &:after {
      content: ' *';
      color: #f00;
      position: absolute;
      right: 3px;
    }
  }
}

.validation-required-group {
    @extend .validation-required;

    .control-label {
        padding-right: 12px;
    }
}

.form-inline {
  .validation-required {
    .control-label {
      padding-right: 10px;
    }
  }
}

/* .payments-table th {
    min-width: 100px;
} */

.homepage__tab-info-text {
  display: block;
  font-weight: bold;
  margin: 10px 0;
}

.teams-table-columns, .payments-table-columns, .table-history-columns {
  .col-05{
    width: 4%;
    max-width: 20px;
    min-width: 20px;
    text-align: left;
  }
  .col-1{
    width: 5%;
    max-width: 90px;
    min-width: 60px;
    text-align: left;
  }
  .col-2{
    width: 10%;
    max-width: 130px;
    min-width: 130px;
    white-space: normal;
  }
  .col-3{
    width: 17%;
    max-width: 200px;
    min-width: 200px;
  }
  .col-4{
    width: 10%;
    max-width: 55px;
    min-width: 55px;
  }
  .col-5{
    width: 15%;
    max-width: 174px;
    min-width: 130px;
  }
  .col-6{
    width: 17%;
    max-width: 200px;
    min-width: 200px;
  }
  .col-7{
    width: 7%;
    max-width: 55px;
    min-width: 55px;
  }
  .col-8{
    width: 7%;
    max-width: 55px;
    min-width: 55px;
  }
  .col-9{
    width: 7%;
    max-width: 55px;
    min-width: 55px;
  }
  .col-10{
    width: 7%;
    max-width: 55px;
    min-width: 55px;
  }
  tbody {
    height: 380px;
  }
}

.camps-stats {
  .camp-name {
    width: 370px;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .check {
    width: 220px;
  }
  .card {
    width: 90px;
  }
  .amount {
    width: 290px;
  }
}

.club-athletes-columns {
  .col-05{
    width: 1%;
    max-width: 15px;
    min-width: 15px;
    text-align: left;
  }
  .col-08{
    width: 2%;
    max-width: 30px;
    min-width: 30px;
    text-align: left;
  }
  .col-1{
    width: 5%;
    max-width: 50px;
    min-width: 50px;
    text-align: left;
  }
  .col-2{
    width: 5%;
    max-width: 75px;
    min-width: 75px;
  }
  .col-3{
    width: 5%;
    max-width: 75px;
    min-width: 75px;
  }
  .col-4{
    width: 25%;
    max-width: 200px;
    min-width: 200px;
  }
  .col-5{
    width: 5%;
    max-width: 80px;
    min-width: 80px;
  }
  .col-6{
    width: 5%;
    max-width: 80px;
    min-width: 80px;
  }
  .col-7{
    width: 5%;
    max-width: 100px;
    min-width: 100px;
  }
  .col-8{
    width: 20%;
    max-width: 100px;
    min-width: 100px;
  }
  .col-9{
    width: 15%;
    max-width: 100px;
    min-width: 100px;
  }
  .col-10{
    width: 10%;
    max-width: 125px;
    min-width: 125px;
  }
}

.teams-table td, .payments-table td, .table-history td, .event-title > .lead {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.teams-table thead, .payments-table thead, .table-history thead {
  display: table;
  width: 100%;
  tr {
    display: table-row;
    width: 100%;
  }
}

.teams-table, .payments-table, .table-history {
  thead {
    display: block;
    margin: 0 -21px 0 0;
    overflow-y: scroll;
    overflow-x: hidden;
    tr {
      display: table;
      width: 100%;
    }
  }
  tbody {
    display: block;
    overflow-y: scroll;    /* Trigger vertical scroll    */
    overflow-x: hidden;
    min-height: 300px;
    tr {
      display: table;
      width: 100%;
    }
  }
}

// sticky table styles
.sticky-wrap {
    overflow-x: auto;
    position: relative;
    width: 100%;
}
.sticky-wrap .sticky-thead,
.sticky-wrap .sticky-col,
.sticky-wrap .sticky-intersect {
    opacity: 0;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    transition: all .4s ease-in-out;
    z-index: 50;
    width: auto;
    td, th {
      background: #fff;
      vertical-align: middle;
      text-align: center;
      border: 1px solid #ddd;
      padding: 5px;
      font-weight: bold;
      color: #333;
      white-space: nowrap;
    }
}
.sticky-wrap .sticky-thead {
    z-index: 100;
    width: 100%; /* Force stretch */
    left: 0; /* offset fix */
}
.sticky-wrap .sticky-intersect {
    opacity: 1;
    z-index: 150;
}

.sticky-wrap .sticky-col {
    tr:nth-child(odd) {
      th {
        background: #f9f9f9;
      }
    }
}

.sw_header_breadcrumbs.breadcrumb {
    text-overflow: ellipsis;
    white-space: nowrap;
  .active {
    a {
        color: #777 !important;
    }

    a:hover {
        color: #333 !important;
    }
  }
}

@media only screen and (max-width: 1199px) {
  .teams-table thead {
    padding: 0;
  }
}

.housing-history {
  background: rgba(245,245,245,0.9);
  font-size: 13px;
  line-height: 1.3em;
  .housing-history-item {
    border-top: 1px solid rgb(224, 224, 224);
    padding: 20px 20px;
      .col-sm-6 {
          padding-left: 10px;
          padding-right: 0;
      }
  }
}

.teams-in-division {
  table {
    border: 1px solid silver;
    td, th {
      padding: 7px !important;
    }
  }
  p {
    margin: 0;
    padding: 10px 5px;
  }
}

.club-label {
  padding-top: 7px;
}

.save-time-text {
  font-size: 12px;
}

.save-time-box .checkbox {
  display: inline-block;
  margin-right: 20px;
}

.list-arrow {
  float: right;
}

.list-text-right {
  float: right;
  margin: 0 10px;
}

/* === For portrait phones =================================== */
@media (max-width: 479px) {
  .span-right-strict {
    display: none;
    margin-right: 5px;
  }
}

.mobile-menu {
  float: left;
  margin-left: 5px;
  margin-right: 5px;
  margin-top: 14px;
}

.public-events {
  height: 260px;
  overflow: auto;
  .list-text-right {
    min-width: 22px;
    min-height: 10px;
  }
}

.public-event-name {
  display: block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.public-event-date {
  display: inline-block;
  width: 40px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.btn-group.btn-vertical-aligned {
  display: table;
  width: 100%;
  .btn-group {
    float: none;
    display: table-cell;
    width: 50%;
    border: 1px solid #ccc;
    background: #fff;
    border-radius: 4px;
    &:first-child {
      border-bottom-right-radius: 0;
      border-top-right-radius: 0;
    }
    &:last-child {
      border-bottom-left-radius: 0;
      border-top-left-radius: 0;
    }
    .btn {
      display: block;
      width: 100%;
      text-align: center;
      border: none;
      background: none;
      white-space: normal;
    }
  }
}

.loading-image path{
  fill: #333;
}

@media (max-width: 1000px) {
  .club-teams, .events-table, .club-athletes {
    .hideable {
      display: none;
    }
  }
}

@media (max-width: 800px) {
  .club-staff .hideable {
    display: none;
  }
}

.events-tabset {
  border-spacing: 0;
}

@media (min-width: 680px) {
  .event-info-container {
    width: 70%;
    margin: 0 15%;
    .event-info-title {
      display: inline-block;
      margin-left: 10%;
    }
  }
}

@media (max-width: 680px) {
  .event-info-container {
    width: 100%;
    .event-info-title {
      display: inline-block;
    }
  }
}

.division-line td {
  padding: 10px !important;
}

.dl-event-horizontal {
    overflow: hidden;
    dt {
        float: left;
        width: 160px;
        clear: left;
        text-align: left !important;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
}

.editable-cell {

    .input-sm {
        height: 23px !important;
    }

    .input-sm.w-sm {
      width: 56px !important;
    }

    .text-sm {
      font-size: 0.8em !important;
    }

    .input-sm.w-lg {
      width: 100% !important;
    }

    .offset-sm {
      margin-right: -14px;
    }

    .offset-lg {
      margin-right: -18px;
    }

    .edit-text {
      i {
        visibility: hidden;
      }
    }
    .edit-text:hover {
      i {
        visibility: visible;
      }
    }

    &.border-color {
      .edit-text {
        i {
          visibility: visible;
        }
      }
    }
}

.editable-cell.block-label {
    width: 75%;
}

.editable-cell.block-short-label {
    width: 25%;
}

.editable-cell.block-md {
  width: 48%;
}

.editable-cell.block-lg {
  width: inherit;
}

.row-mb--20 {
    margin-bottom: 20px
}

.tickets-table {
    margin-bottom: 0;

    .wl-cell {
      width: 73px;
    }

    .check-box-cell {
      width: 40px;
    }

    .check-box-cell, .wl-cell {
      height: 100px;
      white-space: nowrap;

      div {
        width: 20px;
        max-width: 20px;
        transform: translate(0, -4px) rotate(315deg);
        > span { padding: 2px; }
      }
    }

    .description-cell {
      text-overflow: ellipsis;
      white-space: nowrap;
      width: 65px;
      max-width: 65px;
      overflow: hidden;
      cursor: pointer;
      font-size: 0.9em;
    }
    .color-cell {
        width: 90px;
        max-width: 90px;
    }
    .date-cell {
      width: 130px;
      min-width: 130px;
    }
    .valid-dates-cell {
        width: 130px;
        min-width: 130px;
    }
    .init-date {
      width: 160px;
      min-width: 160px;
    }
    .date-cell, .init-date {
      text-align: right;
      .title {
        a {
            color: #333;
            text-decoration: none;
          }
        a:hover {
            text-decoration: none;
        }
      }
      .title.label {
        a {
          color: #fff;
        }
      }
    }
    .label-cell {
      width: 270px;
    }
    .label-ticket-type, .label-sub-type, .label-allow-reentry {
        width: 130px;
        min-width: 100px;
    }
    .label-border-color {
        width: 135px;
    }
    .save-button-cell {
      width: 38px;
    }
    .loading-image {
        margin: -4px -6px;
        width: 21px;
        height: 21px;
    }
    .dropdown-menu {
        table[role="grid"] {
            td, th {
                >.btn-sm {
                    border: 0 !important;
                    padding: 3px 8px !important;
                }
            }
        }

        table[timepicker] {
            td {
                button {
                    margin-left: 7px;
                }
            }
        }
        li {
            span {
                button {
                    padding: 1px 10px !important;
                }
            }
        }
    }
}

.tickets-report, .scanner-modal, .tickets-payment-modal, .tickets-settings-form, .edit-athlete-form,
.date-time-control {
  .dropdown-menu {
        table[role="grid"] {
            td, th {
                >[type="button"] {
                    border: 0 !important;
                    padding: 3px 8px !important;
                }
            }
        }

        table[timepicker] {
            td {
                button {
                    margin-left: 7px;
                }
            }
        }
        li {
            span {
                button {
                    padding: 1px 5px !important;
                }
            }
            button {
                padding: 1px 5px !important;
            }
        }
    }
}

.tickets-receipt {
    .title {
        h3 {
            white-space: normal;
        }
    }
    .content {
        width: 1024px;
        margin: 0 auto;
    }
}

.event-history {
  .created {
    width: 150px;
  }
  .comments {
    min-width: 450px;
  }
  .action {
    width: 200px;
  }
  .history-info {
    width: 275px;
  }
}

dl.payment-details {
    @media (min-width: 1000px) {
        dt {
          width: 110px;
        }
        dd {
          margin-left: 120px;
          &::after {
            content: "\00a0";
          }
        }
    }
}

.charge-info {

    .item {
        display: block;
        clear: both;
        div {
            display: inline-block;
            float: left;
        }

        .title {
            font-weight: bold;
            width: 100px;
        }

        .value {
            width: 180px;
        }
    }


    dd {
        width: 200px;
        &::after {
            content: "\00a0";
        }
    }
}

.col-pd-sm {
    padding-right: 5px !important;
    padding-left: 5px !important;
}

.tickets-payments-bar {
    .search-input {
        width: 300px;
    }

    .form-group-ml {
        margin-left: 15px !important;
        margin-top: 15px;
    }
}

.tickets-payment-history {
  table-layout:fixed;
  td {
    word-break: break-all;
  }
  .date-col {
    min-width: 185px
  }
}

.tickets-payments-table {
    font-size: 0.9em;

    .code-col {
        min-width: 91px;
    }

    .ticket-count-col {
        min-width: 10px;
    }

    .total-col {
        min-width: 77px;
    }

    .purchased-col {
        min-width: 150px;
    }

    .firstname-col {
        min-width: 50px;
    }

    .lastname-col {
       min-width: 65px;
    }

    .email-col {
       min-width: 185px;
    }

    .zip-col {
       min-width: 50px;
    }

    .status-col {
        min-width: 60px;
    }

    .scanned-col {
        min-width: 230px;
    }

    thead {
        th {
            cursor: pointer;
        }

        th.ticket-count-col {
            cursor: default;
        }
    }

    tbody {
        tr {
            td {
                cursor: pointer;
            }
        }
    }
}

.tickets-payment-modal {
    .panel-body, .panel-heading {
        padding: 5px;
    }

    .center-text {
        line-height: 32px;
    }

    .alert {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        padding-top: 7px;
        padding-bottom: 7px;
    }

    .alert-block {
        position: absolute;
        bottom: 39px;
        width: 100%;
    }
    .panel {
        position: relative;
    }
}

.not-loaded {
    min-height: 440px;
    text-align: center;
}

.payment-modal-header {
    h4 {
        margin: 0;
    }
}

.tickets-on-map {
    .angular-google-map-container {
        height: 600px;
    }

    .marker-label {
        color: #000;
        font-size: 0.9em;
    }
}

.tickets-showcase-table {
    thead th, tbody td {
        text-align: right;
    }
    .title {
        font-weight: bold;
        text-align: left;
    }
}

.tickets-additional-table {
    width: auto;
    .label-column {
        width: 250px;
        min-width: 250px;
    }

    .grid-label-column {
        width: 150px;
        min-width: 150px;
    }

    .field-column {
      width: 250px;
      min-width: 250px;
      cursor: default;
      font-size: 80%;
      color: grey;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .type-column {
        width: 150px;
        min-width: 150px;
    }

    .show-on-column {
        width: 150px;
        min-width: 150px;
    }

    .required-column {
        width: 50px;
        min-width: 50px;
        text-align: center;
    }

    .options-column {
        width: 250px;
        min-width: 250px;
    }

    tbody {
        td {
            cursor: pointer;
        }
    }

    .readonly-field {
      cursor: default;
    }

    .new-line {
        border: 2px solid  #5bc0de;
        .add-options {
            margin-left: 10px;
        }
    }
}
.editable-field:hover:after {
    content: " \f044";
    font: normal normal normal 14px/1 FontAwesome;
    font-size: inherit;
    text-rendering: auto;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    color: #999;
}

.additional-select-modal {
  .embedded-edit {
    display: inline-block;
    margin-left: 5px;
  }
}

.ui-draggable-dragging {
    box-shadow: 0 0 2px #000;
    background-color: #d8e5ff;
}

.dragging-row-over {
  background-color: #f1aa03;
}


.embedded-edit .sm, .tickets-additional-table .new-line .sm {
    padding: 1px;
    height: auto;
}


.confirm-alert {
    position: fixed;
    right: 14px;
    z-index: 9999999;
    margin-bottom: 12px;
    background-color: #A6AEA2;
    color: #fff;
    min-width: 250px
}

.confirm-alert.bottom {
    bottom: 0
}

.vis-hidden {
    visibility: hidden;
}

.white-ro[readonly] {
    background-color: #fff
}

.q-addon::after {
    font: normal normal normal 14px/1 FontAwesome;
    font-size: 1.2em;
    content: "\f128";
    position: absolute;
    right: 1px;
    color: #337ab7
}

form[name="newDivisionsForm"] {
    .age-row {
        [class^="col-xs-"] {
            padding: 0;
        }
        [class^="col-xs-"]:first-child {
            padding-left: 3px;
        }
        [class^="col-xs-"]:last-child {
            padding-right: 3px;
        }
    }
}

.social-img {
    width: 24px;
    height: 24px;
}

.clubownermenucontent {

    .clubownermenutabpanel {
        .social-tab {
            @media (min-width: 768px) {
                margin-left: 25px;
            }
            padding: 5px 10px;
        }
    }
}

.roster-list {
    font-size: 0.9em;
    [class^="col-xs-"] {
        padding-left: 8px;
        padding-right: 8px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
    }
    .row {
        margin-left: -8px;
        margin-right: -8px;
    }
    .list-group-item {
        padding: 8px 8px;
    }
    .col-checkbox {
        width: 5.33333%
    }
    li:hover {
        color: #555;
        background-color: #f5f5f5;
    }
}

.events-list-table {
    margin-bottom: 0;
    .date-col {
        width: 8%
    }
    .name-col {
        width: 55%
    }
    .city-col {
        width: 15%
    }
    .state-col {
        width:  8%
    }
    .state-col-large {
        width: 10%
    }
    .stub-col {
        border-bottom: 2px solid #fff;
        width: 1%;
    }
    .unbordered {
        border-bottom: 0
    }
    .scroll-table {
        margin-bottom: 0;
        tbody {
            tr:first-child {
                td {
                    border-top: 0
                }
            }
        }
    }
    td.scroll-container {
        border-top: 0;
        padding: 0;
        @media (max-width: 767px) {
            max-width: 1px;
        }
    }
}

.event-member-header {
    h3 {
        margin: 0;
        .small {
            color: #999;
        }
    }
}

.link-block {
    overflow-wrap: break-word;
}

.alert-sm {
    .alert {
        padding: 5px;
        margin-bottom: 7px
    }
}

.row-sm-pd {
    @media (min-width: 768px) {
        [class^="col-sm-"] {
            padding-left: 7px;
            padding-right: 7px;
        }
    }
}

.table tbody {
    .remove-border {
        td {
            border-top: 0
        }
    }
}

.nowrap {
  white-space: nowrap;
}

.ellipsis {
  text-overflow: ellipsis;
}

.striked-out {
  text-decoration: line-through;
}

.red-line {
   text-decoration-color: #a94442
}

.spinner-wrapper {
  width: 100%;
}

.form-inline {
  .control-width-lg {
    width: 400px;
  }
}

.table {
  .bg-danger {
    border: 1px solid #D02222;
    @media print {
      background-color: #f2dede !important;
      -webkit-print-color-adjust: exact;
    }
    &:hover {
      background-color: rgba(242, 222, 222, 0.57);
    }
  }
}

.fs-bordered {
    margin: 10px;
    border: 1px solid #E4E4E4;
    padding: 8px 0;
    border-radius: 4px;
    @media (max-width: 768px) {
      padding: 8px;
    }
    legend {
      width: auto;
      margin: 0 0 0 15px;
      font-size: inherit;
      border-bottom: 0;
    }
}

.table {
  .striked-row {
    td {
      position: relative;
      &:before {
        content: " ";
        position: absolute;
        top: 49%;
        left: -10%;
        border-bottom: 1px solid #D02222;
        width: 115%;
      }
    }
  }
  .striked-cell {
    td:not(:empty) {
      position: relative;
      &:before {
        content: " ";
        position: absolute;
        top: 49%;
        border-bottom: 1px solid #D02222;
        width: 100%;
      }
    }
  }
}

.members-list {
  position: relative;
  tr {
    &:before {
        content: " ";
        position: absolute;
        visibility: hidden;
    }
    &.removed-row:hover {
      color: rgba(153, 153, 153, 0.31) !important;
      &:before {
        content: attr(member-removed);
        position: absolute;
        width: 100%;
        text-align: center;
        color: black;
        font-size: 1.2em;
        padding: 5px;
        visibility: visible;
      }
      td {
        background-color: rgba(255, 0, 0, 0.2);
      }
    }
  }
}

.member-overwrite {
  position: relative;
  &:before {
    content: "to";
    font-weight: bold;
    position: absolute;
    top: 12%;
    left: -10%;

    @media (max-width: 768px) {
      top: 0;
      left: 2%;
    }
  }
  .form-control {
      @media (max-width: 768px) {
      width: 95%;
      float: right;
    }
  }
}

.event-member-modal {
  .form-group {
    .to-right {
      @media (min-width: 768px) {
        float: right !important;
      }
    }
  }
  .table-wrapper {
    max-height: 135px;
    overflow-y: auto;
  }
}

.form[name="pickerForm"] {
  .has-error {
    position: relative;
     &:after {
      content: " Test ";
      position: absolute
     }
  }
}

.mt1 {
  margin-top: 1px;
}

.table.p-sm {
  td {
    padding-left: 1px;
    padding-right: 1px;
  }
}

.m-b0 {
  margin-bottom: 0
}

.ov-x-auto {
  overflow-x: auto;
}

.badge-sm {
  padding: 2px 5px !important;
  font-size: 0.8em;
}

.nowrap {
  white-space: nowrap;
}

.sw-confirm {
  .modal-footer {
    padding: 10px;
  }
}

.sub-drop-down {
  list-style: none;
  margin: 0;
  padding: 0 0 0 10px;
  >li {
      >a {
      display: block;
      padding: 3px 20px;
      clear: both;
      font-weight: normal;
      line-height: 1.42857;
      color: #333;
      white-space: nowrap;
    }
    >a:hover, >a:focus {
        text-decoration: none;
        color: #373b3b;
        background-color: #f5f5f5;
    }
  }

  >.active {
    >a, >a:hover, >a:focus  {
      color: #fff;
      text-decoration: none;
      outline: 0;
      background-color: #428bca;
    }
  }
}

.p28 {
  @media (min-width: 768px) {
    padding-top: 28px !important;
  }
}

.form-inline {
  @media (min-width: 768px) {
    .form-group {
      margin-right: 1px;
      margin-left: 1px
    }
    .form-group:last-child {
      margin-right: 0;
    }
    .form-group:first-child {
      margin-left: 0;
    }
    &.form-indent {
      .form-group {
        margin-right: 5px;
        margin-left: 5px
      }
      label {
        margin-right: 2px;
      }
    }
  }
}

.btn {
  @media (min-width: 768px) {
    margin-right: 1px;
    margin-left: 1px;
  }
}

.m0 {
  margin: 0;
}

$fading-border-r: 222;
$fading-border-g: 51;
$fading-border-b: 51;
@keyframes fadeBorderInOut {
  0% {
    border-color: rgba($fading-border-r, $fading-border-g, $fading-border-b, 0);
  }

  50% {
     border-color: rgba($fading-border-r, $fading-border-g, $fading-border-b, 1);
  }

  60% {
    border-color: rgba($fading-border-r, $fading-border-g, $fading-border-b, 0.8);
  }

  70% {
    border-color: rgba($fading-border-r, $fading-border-g, $fading-border-b, 0.6);
  }

  80% {
    border-color: rgba($fading-border-r, $fading-border-g, $fading-border-b, 0.4);
  }

  90% {
    border-color: rgba($fading-border-r, $fading-border-g, $fading-border-b, 0.2);
  }

  100% {
    border-color: rgba($fading-border-r, $fading-border-g, $fading-border-b, 0);
  }
}

@-webkit-keyframes fadeBorderInOut {
  0% {
    border-color: rgba($fading-border-r, $fading-border-g, $fading-border-b, 0);
  }

  50% {
     border-color: rgba($fading-border-r, $fading-border-g, $fading-border-b, 1);
  }

  60% {
    border-color: rgba($fading-border-r, $fading-border-g, $fading-border-b, 0.8);
  }

  70% {
    border-color: rgba($fading-border-r, $fading-border-g, $fading-border-b, 0.6);
  }

  80% {
    border-color: rgba($fading-border-r, $fading-border-g, $fading-border-b, 0.4);
  }

  90% {
    border-color: rgba($fading-border-r, $fading-border-g, $fading-border-b, 0.2);
  }

  100% {
    border-color: rgba($fading-border-r, $fading-border-g, $fading-border-b, 0);
  }
}

[fading-border].fading-border {
  border-bottom: 2px solid rgba($fading-border-r, $fading-border-g, $fading-border-b, 0) !important;
  animation-name: fadeBorderInOut;
  -webkit-animation-name: fadeBorderInOut;
  animation-duration: 600ms;
  -webkit-animation-duration: 600ms;
}

[fading-border].fading-elem {
  border-bottom: 2px solid rgba(0, 0, 0, 0);
}

.multiselect-filter {
  min-width: 152px;
  position: relative;
  vertical-align: baseline !important;

  [uib-dropdown-toggle] {
    height: 34px;
    line-height: 28px;
  }

  a {
    &:hover {
      text-decoration: none;
    }
  }

  .dropdown-menu {
    margin-top: -4px;
  }

  &:hover {
    border-color: #3399F3;
  }

  .header-item:first-child {
    padding-left: 0;
  }

  .header-item:last-child {
    padding-right: 0;
  }

  .filter-item {
    padding-top: 7px;
    padding-bottom: 7px;
  }

  &.large {
    .dropdown-menu {
      min-width: 250px;
    }
  }

  &.middle {
    .dropdown-menu {
      min-width: 180px;
    }
  }

  &.middle-large {
    .dropdown-menu {
       min-width: 210px;
    }
  }

  @media screen and (max-width: 1199px) {
    &.increase-mb {
      margin-bottom: 15px !important;
    }
  }
}

.sw-searchbox {
  .form-control {
     width: 100%;
  }

  @media screen and (max-width: 1199px) {
    &.increase-mb {
      margin-bottom: 15px;
    }
  }
}

.teams-actions, .change-paid-team {
    .dropdown-menu {
        @media screen and (min-width: 1200px) {
            right: 0;
            left: auto;
        }

        .separator {
            position: relative;

            .text {
                z-index: 1;
                background-color: white;
                padding: 0 3px 0 3px;
            }

            &:before {
                border-top: 1px solid #ddd;
                content: "";
                margin: 0 auto;
                position: absolute;
                top: 49%;
                left: 0;
                right: 0;
                width: 95%;
                z-index: -1;
            }
        }
    }
}

.recover-pswd {
  @media screen and (min-width: 768px) {
      .form-inline {
          .form-control {
              width: 100%;
          }
      }
  }

  @media screen and (max-width: 767px) {
      [class^="col-sm"] {
          margin-top: 3px;
          margin-bottom: 3px;
      }
  }
}

.stripe-accounts-list {
  .list-group-item {

    [class^="col-xs-"] {
        padding: 3px;
    }

    [class^="col-xs-"]:first-child {
        padding-left: 15px;
    }
    [class^="col-xs-"]:last-child {
        padding-right: 15px;
    }

    .acc-title {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

  }
}

.eo-partial-refund {
  .team-name {
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

.tmpls-list, .supervisor {
    .xs-text {
      font-size: 0.8em;
    }
    .is-live {
      color: #1dab51;
    }
    .not-live {
      color: #cc3c3c;
    }
}

.register-dates {
    border-left: solid 3px #d3d3d3;
    margin-bottom: 15px;

    @media screen and (min-width: 768px) {
        margin-left: 18.5%;
    }

    @media screen and (max-width: 767px) {
        margin-left: 5%;
    }
}

.register-dates-teams {
    border-left: solid 3px #d3d3d3;
    margin-bottom: 15px;
    margin-left: 10%;
}

.has-error-div {
    color: #a94442;
    font-weight: bold;
}

.has-warning-div {
    color: #8a6d3b !important;
    font-weight: bold;
}

.delete-event {
    color: #ee5f5b;
    &:hover {
        color: #ee5f5b;
        text-decoration: underline;
    }
}

.housing-notes {
    .col-sm-12 {
        padding-bottom: 20px;
        textarea {
            resize:vertical;
        }
    }
}

.note-add-block {
    padding-top: 10px;
    padding-bottom: 10px;

    label:first-child {
        padding-right: 10px;
    }

    input[type='radio'] {
        margin-top: 2px;
    }
}

.white-space-pre-line {
    white-space: pre-line;
}

.staffers-list {
    a:-webkit-any-link {
        text-decoration: none;
    }
    .pointer {
        text-decoration: none;

        .mt1 {
            font-size: 14px;
            a {
                font-weight: 400;
            }
        }

        .staffers-phones {
            padding-top: 5px;
            font-size: 14px;
            font-weight: 400
        }
    }
}

.supervisor {

    .form-horizontal {
        margin-top: 10px;

        .staff-safesport {
            text-decoration: underline;
            color: red;
            font-weight: 300
        }
    }
}

.helper-descr {
    cursor: help;
    border-bottom: 1px dotted;
}

.helper-tooltip {
    cursor: pointer;
    border-bottom: 1px dotted;
    &:hover {
        text-decoration: none;
    }
}

.checkin-unlock {
    padding-left: 0 !important;
    margin-left: 0 !important;
}

.chkin {
    margin-left: 0;
    margin-right: 0;
    padding-left: 7px;
    padding-right: 6px;
}

online-checkin-status {
    hr {
        margin-bottom: 10px;
    }
}

.tickets-settings-form .dates-button {
    margin-top: 5px;
    margin-bottom: 5px;
}

.status-checkin, .status-online-checkin {
    cursor: pointer;
}

.send-email-stripe {
    .row {
        margin-top: 5px;
    }
}

.payment-emails-list {
    padding-bottom: 0;
    margin-bottom: 0;

    .font-bold  {
        font-size: 14px;
        font-weight: 400;
    }
}

.payment-send-button {
    padding-bottom: 10px;
}

.payment-send-email-form {
    border: 1px solid lightgrey;
    border-radius: 4px;
}

.roster-lock {
    text-decoration: underline;
    cursor: pointer;
}

.well-checkin {
    padding-left: 10px;
    padding-right: 10px;
}

.add-change-note {
    .control-label {
        color: #0A0A0A;
        cursor: pointer;
        font-weight: 400;
    }
}

.fa-check, .fa-times, .fa-clock-o, .fa-exclamation-circle {
    font-size: 1.1em
}

.panel-smooth {
  opacity: 0;
  transition: opacity 1s ease;
}

.panel-smooth.showed {
  opacity: 1;
}

.receive-check-form {

    @media only screen and (min-width: 765px) {
        .w140 {
            max-width: 140px
        }

        .w115 {
            max-width: 115px
        }
    }

}

.canellation-link, .ml7 {
  margin-left: 7px;
}

.dropdown-menu-close {
    padding-right: 10px !important;
    padding-bottom: 4px !important;
}

.assign-template {
    padding-top: 20px;

    .assign-template-types {
        padding-bottom: 10px;
        padding-top: 10px;

        .dropdown-menu  li {
            padding-right: 10px;
            padding-top: 3px;
            padding-left: 10px;
            padding-bottom: 3px;
        }
    }

    #single-button {
        width: 200px;
        text-overflow: ellipsis;
        white-space: nowrap;
        overflow: hidden;
    }
}

.input-group-btn {
  .adjust-height {
    line-height: 1.42857143;
  }
}

.form-group {
  .control-invalid {
    border: 1px solid #a94442;
  }
  input[type="radio"][required] {
      box-shadow: none;
  }
}

.text-xs {
      font-size: 80% !important;
  }

.gender-radio-list {
    label {
        padding-right: 10px
    }
}

.earned-bids-form {
    padding-top: 14px;

    .checkbox {
        padding-top: 8px;
    }

    .save-button-row {
        padding-top: 20px;
    }
}

// StickyTableHeaders using margin-top for header on Safari, but
// absolutely positioned elements should use top property.
.tableFloatingHeaderOriginal {
    top: 0;
}

.travel-coordinator-name {
    padding-left: 0
}

.purchase-refunds-list {
    padding: 0 0 3px 0;
}


.team-history-email .form-group {
    margin-bottom: 0;
}

.staffers-list {
    a {
        &:hover, &:visited, &:active, &:focus {
            text-decoration: none;
        }
    }

    .staffers-list-gender {
        padding-left: 4px;
    }
}

.assigned-tickets-list {
    .col-xs-12 {
        padding-top: 15px;
        text-align: center;

        ul {
            padding-top: 10px;
            text-align: left;

            li {
                padding-bottom: 10px;
            }
        }
    }
}

.official-edit {
  .help-block {
    margin-bottom: 0;
  }
}

.no-resize {
    resize: none;
}

.pr-0 {
    padding-right: 0;
}

.mark-valid-checkbox {
    padding-right: 7px;

    label {
        font-weight: normal;
    }
}

.qr-code-wrapper {
    text-align: center;
}

.mt-5 {
    margin-top: 5px;
}

.mb-5 {
    margin-bottom: 5px;
}

.mt-10 {
    margin-top: 10px;
}

.stripe-card-form {
    background-color: rgba(18, 91, 152, 0.05);
    border-radius: 5px;
    margin-bottom: 30px;
    margin-top: 30px;

    .col-xs-12 {
        padding-bottom: 5px;
        padding-top: 15px;
    }

    .stripe-card-form-title {
        text-align: center;
        margin-bottom: 5px;

        span {
            color:#8898aa;
        }
    }

    form {
        padding: 10px;
        border-radius: 5px;
        background-color: white;
        box-shadow: 0 4px 6px rgba(50, 50, 93, 0.11), 0 1px 3px rgba(0, 0, 0, 0.08);
        margin-top: 10px;

        legend {
            text-align: center;
            font-size: 16px;
        }
    }

    #card-errors {
        padding-top: 5px;
        color: #eb1c26;
    }

    button {
        background: #32325d;
        color: white;
        padding-top: 5px !important;
        margin-bottom: 5px;
        margin-top: 25px;
        width: 100%;
    }

    button:focus {
        color: white !important;
    }

    button:hover {
        color: white !important;
    }
}

.stripe-form-label {
  font-family: #{$stripeFontFamily};
  font-size: #{$stripeFontSizeSm};
  font-weight: #{$stripeFontWeightNormal};
  margin-bottom: #{$stripeSpacing1};
}
.stripe-card-list {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: #{$stripeSpacing2};
  margin-bottom: #{$stripeSpacing3};
  font-family: #{$stripeFontFamily};
  padding: 0;
  list-style-type: none;

  .stripe-card {
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    padding: #{$stripeSpacing3};
    background-color: #{$stripeBgPrimary};
    border-radius: 5px;
    font-size: #{$stripeFontSizeXs};
    font-weight: #{$stripeFontWeightSemiBold};
    color: #{$stripeTextSecondary};
    transition: background .15s ease, border .15s ease, box-shadow .15s ease;
    border: 1px solid #e6e6e6;
    box-shadow: 0px 1px 1px rgba(0, 0, 0, 0.03), 0px 3px 6px rgba(0, 0, 0, 0.02);
  }

  .stripe-card-selected {
    border-color: #{$stripePrimary};
    color: #{$stripePrimary};
    box-shadow: 0px 1px 1px rgba(0, 0, 0, 0.03), 0px 3px 6px rgba(0, 0, 0, 0.02), 0 0 0 1px #{$stripePrimary};
  }
}

.mt-30 {
    margin-top: 30px;
}

.teams-use-clubs-module-form {
    margin-bottom: 25px;
}

.cancel-participation-btn, .cancel-participation-btn:hover  {
    color: red;
    cursor: pointer;
}

.btn-outline {
    background-color: transparent;
    color: inherit;
    transition: all .5s;
}

.btn-danger.btn-outline {
    color: #d9534f;
}

.btn-danger.btn-outline:hover {
    color: #fff;
}

.event-image-upload-wrapper {
    margin-bottom: 20px;
}

.file-upload {
    &-action-buttons {
        :first-child {
            margin-bottom: 5px;

            @media only screen and (max-width: 768px) {
                margin-bottom: 0;
            }
        }
    }

    &-input {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    &-wrapper {
        display: flex;

        @media only screen and (max-width: 568px) {
            flex-direction: column;
            align-items: flex-start;
        }
    }

    &-error-message {
        padding: 5px;
        margin: 0;
    }
}

.restrictions {
    margin: 5px 0;
    color: #737373;
    font-size: 11px;
}

.upload-image-btn {
    @media only screen and (max-width: 568px) {
        margin-top: 10px;
    }
}

.images-upload-form {
    padding: 15px;
}

.no-pd-top {
    padding-top: 0 !important;
}

.clothing-totals {
    margin-bottom: 5px;

    .col-xs-1 {
        margin-right: 5px;
    }
}

.sizing-chart {
    overflow-x: auto;
    margin-bottom: 40px;

    .scrollable-name {
        overflow-x: auto;
        max-width: 100px;
    }

    tbody > tr > td:last-child {
        overflow-x: auto;
        max-width: 150px;
    }
}

table.table > tbody > tr > td.sizing-chart-totals {
    padding: 0;
}

.sizing-chart-totals {
    .clothes-title {
        border-right: solid 1px #ddd;
    }
    .table {
        table-layout: fixed;
        margin-bottom: 0;

        .clothes-title {
            font-weight: bold;
            text-align: center;
            vertical-align: middle;
        }
    }
}

.mobile-mt-5 {
    @media only screen and (max-width: 434px) {
        margin-top: 5px;
    }
}

.padding-0 {
    padding: 0 !important;
}

.tooltip-inner {
    max-width: 1000px !important;
}

.multi-select {
  .multiselect-parent {
    width: 100% !important;
  }
}

.has-error-div .multi-select .btn {
  border-color: #a94442 !important;
  color: #a94442 !important;
}

#error-message {
    color: #a94442 !important;
}

.state-picker {
    .dropdown-menu {
        overflow: scroll;
        height: 300px;
    }
}

.not-allowed {
    cursor: not-allowed !important;
}

.mw-100 {
    max-width: 100px;
}

.mw-10 {
    max-width: 10px;
}

.number-without-arrows {
    -moz-appearance:textfield;
}
.number-without-arrows::-webkit-inner-spin-button,
.number-without-arrows::-webkit-outer-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

.pointer-events-auto {
    pointer-events: auto !important;
}

//Temporary solution for SW-3674, need to replace ckeditor
div[class^="cke_notifications_area"] {
    display: none;
}

.club-invoice-table {
    .ng-table-pager {
        float: right;
    }
}

.invoice-data-list {
    text-align: right;
}
