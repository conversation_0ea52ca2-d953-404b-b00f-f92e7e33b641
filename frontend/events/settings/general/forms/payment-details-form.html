<form name="paymentDetailsForm" class="form-horizontal">
    <h4 class="text-center">Check Acceptance Information<br/>
        <small class="text-grey">(This information will appear on your customers invoice)</small>
    </h4>
    <div class="form-group">
        <label class="col-sm-offset-3 col-sm-9">
            <input type="checkbox" ng-model="tournament.allow_check_payments"> Tournament Uses Check Payments
        </label>
    </div>
    <p class="text-center">
        <a href="" ng-click="duplicateHostInfo()"><i class="fa fa-files-o"></i> Same as Host Organization Information</a>
    </p>
    <div ng-class="{ 'form-group validation-required': true, 'has-error': (utils.formSubmitted && paymentDetailsForm.country.$invalid) }">
        <label class="control-label col-sm-3">Country</label>
        <div class="col-sm-4">
            <select      
                class="form-control"         
                name="country"                         
                ng-model="tournament.payment_country"
                ng-options="c.id as c.name for c in utils.countries | orderBy:'name':true"
                ng-disabled="true"
                required
                >
            </select>
        </div>
    </div>
    <div ng-class="{ 'form-group validation-required': true, 'has-error': (utils.formSubmitted && paymentDetailsForm.address.$invalid) }">
        <label class="control-label col-sm-3">Address</label>
        <div class="col-sm-7">
            <input
                type="text" 
                class="form-control"
                name="address"
                ng-model="tournament.payment_address"
                placeholder="Address ..."
                required
                >
        </div>
    </div>
    <div ng-class="{ 'form-group validation-required': true, 'has-error': (utils.formSubmitted && paymentDetailsForm.city.$invalid) }">
        <label class="control-label col-sm-3">City</label>
        <div class="col-sm-7">
            <input
                type="text" 
                class="form-control"
                name="city"
                ng-model="tournament.payment_city"
                placeholder="City ..."
                required
                >
        </div>
    </div>
    <div ng-class="{ 'form-group validation-required': true, 'has-error': (utils.formSubmitted && paymentDetailsForm.state.$invalid) }">
        <label class="control-label col-sm-3">State</label>
        <div class="col-sm-7">
            <spinner active="utils.statesLoading"></spinner>
            <select 
                class="form-control" 
                ng-show="!utils.statesLoading"
                ng-model="tournament.payment_state"
                name="state"
                ng-options="st.state as st.name for st in utils.states | orderBy:'name'"
                required>
                <option value="" selected>Choose a State ...</option>
            </select>
        </div>
    </div>
    <div ng-class="{ 'form-group validation-required': true, 'has-error': (utils.formSubmitted && paymentDetailsForm.zip.$invalid) }">
        <label class="control-label col-sm-3">Zip</label>
        <div class="col-sm-7">
            <input
                type="text" 
                class="form-control" 
                placeholder="ZIP ..."
                ng-model="tournament.payment_zip"
                name="zip"
                usa-zip
                required>
            <form-error-block 
                ng-if="utils.formSubmitted && paymentDetailsForm.zip.$error.usaZip" 
                message="Host Zip should be a 5-digit Number">
            </form-error-block>
        </div>
    </div>
    <div ng-class="{ 'form-group validation-required': true, 'has-error': (utils.formSubmitted && paymentDetailsForm.payment_name.$invalid) }">
        <label class="control-label col-sm-3">Checks Payable to</label>
        <div class="col-sm-7">
            <input 
                type="text" 
                class="form-control" 
                ng-model="tournament.payment_name" 
                name="payment_name" 
                required>
        </div>
    </div>

    <div class="row row-space"></div>

    <div ng-if="showSWFeesNotificationEmailField"
        ng-class="{ 'form-group': true, 'has-error': (utils.formSubmitted && paymentDetailsForm.team_fees_notification_email.$invalid) }">
        <label class="col-sm-3 control-label">SW Team Fees Notifications Email</label>
        <div class="col-sm-7">
            <input
                type="email"
                name="team_fees_notification_email"
                ng-model="tournament.team_fees_notification_email"
                class="form-control"
                placeholder="<EMAIL>"
                autofill-sync
                email-validator>
            <form-error-block
                ng-if="utils.formSubmitted && paymentDetailsForm.team_fees_notification_email.$error.email"
                message="Invalid Email"
            ></form-error-block>
        </div>
    </div>

    <h4 class="text-center">Stripe Cards Payment Information<br/>
        <small class="text-grey">(This Information will be used for Teams Registration and Exhibitors' Booths Payments. For Ticket Payments open "Tickets" tab)</small>
    </h4>
    <div class="form-group">
        <div class="col-sm-offset-3 col-sm-4">
            <label>
                <input type="checkbox" name="allow_card_payments" ng-model="tournament.allow_card_payments" ng-change="loadAccs()"> Tournament Uses Card Payments
            </label>
        </div>
    </div>
    <fieldset ng-if="tournament.allow_card_payments">
        <statement-descriptor-input
            is-required="true"
            ng-model="tournament.stripe_statement"
            has-error="utils.formSubmitted && paymentDetailsForm.stripe_statement.$invalid"
        ></statement-descriptor-input>
        <div ng-class="{ 'form-group validation-required': true, 'has-error': (utils.formSubmitted && paymentDetailsForm.surcharge.$invalid) }">
            <label class="control-label col-sm-3">Credit Surcharge</label>
            <div class="col-sm-8">
                <div class="input-group col-sm-4">
                    <input
                        type="number"
                        class="form-control"
                        ng-model="tournament.credit_surcharge"
                        name="surcharge"
                        min="0"
                        required>
                     <span class="input-group-addon">$</span>
                </div>
                <p class="help-block">This amount is added for each team in payment completed by credit card</p>
            </div>
        </div>
        <stripe-account-select
            account-id="tournament.teams_stripe_account_id"
            event-id="tournament.event_id"
            disabled="tournament.block_teams_keys_edit"
            has-error="utils.formSubmitted && paymentDetailsForm.stripe_account.$invalid"
            account-name="tournament.teams_stripe_account_name"
            on-change="onStripeAccountChange"
        ></stripe-account-select>
    </fieldset>

    <div ng-if="false">
        <h4 class="text-center">Vertical Insurance Information</h4>
        <div class="form-group">
            <div class="col-sm-offset-3 col-sm-4">
                <label>
                    <input type="checkbox" name="show_vertical_insurance_form" ng-model="tournament.teams_settings.show_vertical_insurance_form"> Show Vertical Insurance Form
                </label>
            </div>
        </div>
    </div>
</form>
