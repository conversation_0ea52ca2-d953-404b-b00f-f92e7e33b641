angular.module('SportWrench').directive('eventSettings', eventSettings);

eventSettings.$inject = ['EventSettingsService', '$stateParams', '$state', 'eventStrorage', 'APP_ROUTES', 'userService'];

function eventSettings(EventSettingsService, $stateParams, $state, eventStrorage, APP_ROUTES, userService) {
    return {
        restrict        : 'E',
        scope           : {
            mode    : '@'
        },
        templateUrl     : 'events/settings/event-settings.html',
        require         : 'eventSettings',
        link: function (scope, attrs, elem, ctrl) {            
            scope.utils = {
                title: (scope.mode === 'update')?'Update':'Create'
            };
            scope.settings = {
                tabs            : EventSettingsService.getSectionsList(),
                tournament      : {},
                activeTabIndex  : 0
            };
            scope.isUpdateMode = function () {
                return (this.mode === 'update');
            };
            scope.openDashboard = function () {
                eventStrorage.openLastVisited(ctrl.getEventId());
            };

            scope.showEventUsers = function () {
                // show tab ONLY if user isAdmin or user is the owner of the event
                var _tournament = ctrl.getTournament();
                return userService.hasAccess(_tournament);
            };
        },
        controller: ['$scope', function ($scope) {
            var updateStateName = APP_ROUTES.EO.UPDATE_EVENT;
            this.getMode = function () {
                return $scope.mode;
            }
            this.getEventId = function () {
                return $stateParams.event;
            }
            this.activateUpdateMode = function (id) {
                var tournamentId = id || $scope.settings.tournament.event_id;
                $state.go(updateStateName, { event: tournamentId })
            }            
            /* Tabs Activation */
            this.isDivisionsActive = function () {
                return ($scope.settings.tabs.divisions.index === $scope.settings.activeTabIndex);
            }
            this.isGeneralActive = function () {
                return ($scope.settings.tabs.general.index === $scope.settings.activeTabIndex);
            }
            this.isLocationsActive = function () {
                return ($scope.settings.tabs.locations.index === $scope.settings.activeTabIndex);
            }
            this.isAssignTemplateActive = function () {
                return ($scope.settings.tabs.assignTemplates.index === $scope.settings.activeTabIndex);
            }
            this.isEventImagesActive = function () {
                return ($scope.settings.tabs.images.index === $scope.settings.activeTabIndex);
            }
            /* Setters & Getters*/
            this.setTournament = function(tournament) {
                $scope.settings.tournament                  = tournament;
                $scope.settings.tabs.general.isEventLoading = false;
            }
            this.getTournament = function () {
                return $scope.settings.tournament // need to return promise maybe
            }
            this.setDivisions = function (divisions) {
                $scope.settings.divisions = divisions;
            }
            this.setLocations = function (locations) {
                $scope.settings.locations = locations;
            }
            this.setTournamentLoading = function () {
                $scope.settings.tabs.general.isEventLoading = true;
            }
            this.setLocationsLoading = function () {
                $scope.settings.tabs.locations.isLoading = true;
            }
            this.setLocationsLoaded = function () {
                $scope.settings.tabs.locations.isLoading = false;
            }
        }]
    }
}
