const CSS_CLASSES = {
    DRAGGING_OVER             : 'dragging-row-over',
    ADDITIONAL_TABLE          : 'tickets-additional-table',
    // special class added by jquery when draggable element gets dragging
    JQUI_DRAGGABLE_DRAGGING   : '.ui-draggable-dragging'
};

class Controller {
    constructor (customFormBuilderService, $stateParams, toastr) {
        this.customFormBuilderService = customFormBuilderService;
        this.eventID = $stateParams.event;
        this.toastr = toastr;
    }

    $onInit () {
        this.utils = {
            fields: {}
        }

        this.types = [
            {
                id: 'text',
                name: 'Text'
            },
            {
                id: 'select',
                name: 'Select'
            },
            {
                id: 'multiselect',
                name: 'Multi Select',
            },
            {
                id: 'signature',
                name: 'Signature',
            },
            {
                id: 'signature_checkbox',
                name: 'Signature Checkbox',
            },
            {
                id: 'checkbox',
                name: 'Checkbox',
            },
            {
                id: 'paragraph',
                name: 'Paragraph',
            },
            {
                id: 'date',
                name: 'Datepicker'
            }
        ];
        this.sections = [
            {
                id: 1
            }, {
                id: 2
            }, {
                id: 3
            }];

        this.__copyFields();

        this.jquiDraggableOptions = {
            revert: 'invalid',
            helper: 'clone',
        };
    }

    onVariationChange(field, variation) {
        field.variation = variation;
        this.onFieldChange(field);
    }

    onFieldChange (field) {
        let initialField = this.initialFields.find(f => f.id === field.id) || null;

        if(!this.utils.fields[field.id]) {
            this.utils.fields[field.id] = {};
        }

        this.utils.fields[field.id].changed = JSON.stringify(initialField) !== JSON.stringify(field);
    }

    async openOptionsEditModal (field) {
        const newOptions = await this.customFormBuilderService.openOptionsEditModal(
            field.label,
            field.options || [],
            !this.allowEditing && !field.is_new
        );

        if(_.isUndefined(newOptions)) {
            return;
        }

        field.options = newOptions;

        this.onFieldChange(field);
    }

    disableSaveButton (field) {
        return !field.label || (['select', 'multiselect'].includes(field.type) && _.isEmpty(field.options));
    }

    hideIsRequired (field) {
        return ['paragraph'].includes(field.type);
    }

    hideHelpText (field) {
        return ['paragraph', 'checkbox', 'signature_checkbox'].includes(field.type);
    }

    async save (field) {
        try {
            if(field.id > 0) {
                await this.customFormBuilderService.updateField(
                    this.eventID,
                    this.formId,
                    field.id,
                    _.omit(field, ['id'])
                );

                if(this.utils.fields[field.id]) {
                    this.utils.fields[field.id].changed = false;
                }
            } else {
                let fieldID = await this.customFormBuilderService.createField(
                    this.eventID,
                    this.formId,
                    _.omit(field, ['is_new', 'id'])
                );

                if(fieldID) {
                    field.id = fieldID;
                    delete field.is_new;
                }
            }

            this.__copyFields();

            this.toastr.success('Updated');
        } catch (err) {
            this.toastr.error('Not updated');
        }
    }

    async remove (field) {
        const fieldID = field.id;

        try {
            if(fieldID > 0) {
                await this.customFormBuilderService.deleteField(this.eventID, this.formId, fieldID);

                this.fields = this.fields.filter(f => f.id !== fieldID);

                this.__copyFields();

                this.toastr.success('Removed');
            } else {
                //NOTE if field not saved to DB it doesn't have a ID
                this.fields = this.fields.filter(f => f.sort_order !== field.sort_order);
            }
        } catch (err) {
            console.error(err);
        }
    }

    async changeSortOrder () {
        try {
            let orderData = this.fields.map(f => ({ field_id: f.id, sort_order: f.sort_order }));

            await this.customFormBuilderService.changeFieldSortOrder(this.eventID, this.formId, orderData);

            this.toastr.success('Success');
        } catch (err) {
            console.error(err);
        }
    }

    addField () {
        let maxOrderNumber = 0;
        if(this.fields.length) {
            let lastField = _.max(this.fields, function (f) { return f.sort_order });

            maxOrderNumber = lastField.sort_order;
        }

        this.fields.push({
            id: -1,
            type: 'text',
            options: null,
            default_value: null,
            is_required: false,
            label: '',
            variation: 'default',
            help_text: null,
            section: 1,
            settings: null,
            sort_order: maxOrderNumber + 1,
            is_new: true
        })
    }

    __copyFields () {
        this.initialFields = JSON.parse(JSON.stringify(this.fields));
    }

    dragRowOptions (field, $index) {
        return {
            index: $index,
            animate: true,
            deepCopy: true,
            onStart: '$ctrl.onRowDragStart(field, field.sort_order)',
            onStop:'$ctrl.onRowDragStop(field, field.sort_order)'
        };
    };

    onRowDragStart (event, ui, field, $index) {
        angular.element(CSS_CLASSES.JQUI_DRAGGABLE_DRAGGING).addClass(CSS_CLASSES.ADDITIONAL_TABLE);

        this.dragedItem = field;
        this.dragedIdx = $index;
    };

    onRowDragStop (event, ui, field, $index) {}

    dropRowOptions (field, $index) {
        return {
            onDrop: '$ctrl.onRowDrop(field, field.sort_order)',
            onOut: '$ctrl.onDragedItemOut(field, field.sort_order)',
            onOver: '$ctrl.onDragedItemOver(field, field.sort_order)',
            multiple: true,
            index: $index
        };
    };

    onRowDrop (event, ui, field, $index) {
        let draggedIndex = _.findIndex(this.fields, (f) => f.id === this.dragedItem.id);
        let droppedIndex = _.findIndex(this.fields, (f) => f.id === field.id);

        this.fields.splice(draggedIndex, 1);
        this.fields.splice(droppedIndex, 0, this.dragedItem);

        this.fields = this.fields.map((f, id) => {
            f.sort_order = id+1;

            return f;
        })

        let attr ='#row-' + $index;
        angular.element(attr).removeClass(CSS_CLASSES.DRAGGING_OVER);

        this.changeSortOrder();
    };

    onDragedItemOut (event, ui, field, $index) {
        let attr ='#row-' + $index;
        angular.element(attr).removeClass(CSS_CLASSES.DRAGGING_OVER);
    }

    onDragedItemOver (event, ui, field, $index) {
        let attr = '#row-' + $index;
        angular.element(attr).addClass(CSS_CLASSES.DRAGGING_OVER);
    }
}

Controller.$inject = ['customFormBuilderService', '$stateParams', 'toastr'];

angular.module('SportWrench').component('customFormFieldsEditor', {
    templateUrl: 'events/dashboard/custom-forms-builder/custom-form-editor/custom-form-fields-editor/custom-form-fields-editor.template.html',
    bindings: {
        fields: '<',
        formId: '<',
        allowEditing: '<'
    },
    controller: Controller
});
