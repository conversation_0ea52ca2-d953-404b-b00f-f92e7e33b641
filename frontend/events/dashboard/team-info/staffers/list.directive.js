angular.module('SportWrench').directive('teamStaffersList', teamStaffersList);

function teamStaffersList (
    STAFF_ROLES, toastr, ClubCheckinService, SAFESPORT_VALID_STATUS, $stateParams, EventStaffService, ACTIVATE,
    DEACTIVATE, ConfirmationService, $window, GENDER_VALUES) {
	return {
		restrict: 'E',
		scope: {
			staff 			: '=',
            rolesAllowed    : '=',
			send 			: '&',
			updateStaffer 	: '&',
			remove 			: '&',
            sendBarcode     : '&',
            teamId          : '='
		},
		templateUrl: 'events/dashboard/team-info/staffers/list.html',
		link: function (scope) {
            const filteredStaffRoles = STAFF_ROLES.filter(role => scope.rolesAllowed[role.id]);

            scope.GENDER_VALUES = GENDER_VALUES;

            scope.utils = {
				isOpened 	    : {},
				editor 	 	    : {},
				roles 		    : filteredStaffRoles,
				updating 	    : false,
                disableSendCheckinBarcodeBtn: {}
			};

			scope.toggleCollapse = function (s, update) {
				var id = s.id;

				if (!update) {
                    scope.utils.isOpened[id] = !scope.utils.isOpened[id];
                }

				if(scope.utils.isOpened[id]) {

					if(_.isEmpty(scope.utils.editor[id])) {
						scope.utils.editor[id] = angular.copy(s);
					}

					scope.staff.forEach(function (s) {
						if(s.id !== id) {
							scope.utils.isOpened[s.id] = false;
						}
                    })
                }
			}

			var __onUpdateFinished = function () {
				scope.utils.updating = false;
			}

			var __findRoleName = function (id) {
				for(var i = 0, l = STAFF_ROLES.length; i < l; ++i) {
					if(STAFF_ROLES[i].id === id)
						return STAFF_ROLES[i].short_name
				}
			}

            scope.removeMember = function (staff) {
                let additionalAskText
                    = 'When you will remove this staff, we will automatically deactivate that coach’s QR code.';

                if(!scope.showDeactivatedButton(staff)) {
                    scope.remove({id: staff.id, additionalAskText: null });
                } else {
                    scope.remove({id: staff.id, additionalAskText });
                }
            }

			scope.save = function (s, form) {
				if(form.$invalid) {
					toastr.warning('Invalid From Data');
					return;
				}

				scope.utils.updating = true;

				var editedStaff = scope.utils.editor[s.id];

				var data = {
					email 	            : editedStaff.email,
					name 	            : editedStaff.name,
					phonem 	            : editedStaff.phonem || null,
					phonew 	            : editedStaff.phonew || null,
					phoneh 	            : editedStaff.phoneh || null,
					phoneo 	            : editedStaff.phoneo || null,
					role_id             : editedStaff.role_id,
                    cert                : editedStaff.cert,
                    is_impact           : editedStaff.is_impact,
                    safesport_statusid  : editedStaff.safesport_statusid || null,
					primary             : (editedStaff.primary === undefined || editedStaff.primary === null)
								                                                ?undefined:editedStaff.primary
				}

                const confirmMessage = getPrimaryChangeConfirmMsg(s, data);

                Promise.all([
                    confirmMessage
                    ? ConfirmationService.ask(confirmMessage, {
                            title: 'Confirm team change:',
                            disableNoBtn: true
                      })
                    : Promise.resolve()
                ]).then(([answer]) => {
                    if (!_.isUndefined(answer) && answer !== ConfirmationService.YES_RESP) {
                        return __onUpdateFinished();
                    }

                    scope.updateStaffer({
                        id 		: s.id,
                        staffer : data
                    }).then(function (response) {
                        var keys = Object.keys(data),
                            roleName;
                        keys.forEach(function (key) {
                            s[key] = data[key];
                        })
                        if(data.primary) {
                            delete s.primary_team
                        }
                        roleName = __findRoleName(data.role_id);
                        if(roleName) {
                            s.role_name = roleName
                        }
                        scope.toggleCollapse(s, true);

                        scope.utils.editor[s.id].safeSportChanged = false;
                        scope.utils.editor[s.id].certChanged      = false;
                    }).then(__onUpdateFinished, __onUpdateFinished)
                })
			}

            function getPrimaryChangeConfirmMsg (staff, updateData) {
                const anotherTeamIsPrimary =
                    !_.isEmpty(staff.primary_team) && Number(staff.primary_team.id) !== (scope.teamId);

                if(anotherTeamIsPrimary && updateData.primary !== staff.primary && !staff.primary) {
                    return `You are changing primary role for the staff. 
                        If staff has primary role in another team it will be removed. 
                        Also, if staff checked in with another team check in row for staff will be deleted and new one 
                        for current team will be created. So staffer could checkin with current team.`;
                }
            }

			scope.setSafeSportOk = function (s) {
			    scope.utils.editor[s.id].safeSportChanged   = true;
                scope.utils.editor[s.id].safesport_statusid = SAFESPORT_VALID_STATUS;
            }

            scope.cancelSafeSportChange = function (s) {
                scope.utils.editor[s.id].safeSportChanged   = false;
                scope.utils.editor[s.id].safesport_statusid = null;
            }

            scope.setCertImpact = function (s) {
                scope.utils.editor[s.id].prevCertName   = scope.utils.editor[s.id].cert;
                scope.utils.editor[s.id].cert           = 'IMPACT by EO';
                scope.utils.editor[s.id].is_impact      = true;
                scope.utils.editor[s.id].certChanged    = true;
            };

            scope.cancelSetCertImpact = function (s) {
                scope.utils.editor[s.id].certChanged    = false;
                scope.utils.editor[s.id].is_impact      = null;
                scope.utils.editor[s.id].cert           = scope.utils.editor[s.id].prevCertName;
            };

            scope.openStaffQRCodePage = function (s) {
                $window.open(s.checkin_description_link, '_blank');
            };

            scope.sendCheckinBarcode = function (id, data) {
                scope.utils.disableSendCheckinBarcodeBtn[id] = true;

                return scope.sendBarcode({ id, data })
                    .then(() => {
                        toastr.success('Email and Sms have been sent');
                        scope.utils.disableSendCheckinBarcodeBtn[id] = false;
                    })
                    .catch(() => {
                        scope.utils.disableSendCheckinBarcodeBtn[id] = false;
                    })
            }

            scope.openEditStaffDataModal = function(staff) {
                ClubCheckinService.openEditStaffDataModal(staff, scope.sendCheckinBarcode);
			}

			scope.showDeactivatedButton = function(staff) {
				return staff.is_deactivated !== null; // null - means staffer doesn't have event_team_checkin row
			}
			
			scope.deactivateBarcode = function(data, staffer) {
				const action = staffer.is_deactivated ? ACTIVATE : DEACTIVATE;

				return EventStaffService.deactivateBarcode({
					eventID: $stateParams.event,
					stafferID: staffer.id,
					data,
					action,
				})
				.then(() => {
					staffer.is_deactivated = !staffer.is_deactivated;
					staffer.deactivated_reason = data.reason;
				})
			}
		}
	}
}
