angular.module('SportWrench').service('EventACLService', EventACLService);

EventACLService.$inject = ['EVENT_OPERATIONS'];

function EventACLService(EVENT_OPERATIONS) {
    this.acl                = null;
    this.EVENT_OPERATIONS   = EVENT_OPERATIONS;
}

EventACLService.prototype.isOperationAllowed = function (eventUserAcl, operation) {
    return eventUserAcl && eventUserAcl[operation];
};

EventACLService.prototype.setUserAcl = function (acl) {
    this.acl = acl;
};

EventACLService.prototype.getUserAcl = function () {
    return this.acl;
};

EventACLService.prototype.userHasOnlyEventEditPermissions = function (userPermissions) {
    if(!userPermissions) {
        return true;
    }

    return userPermissions[this.EVENT_OPERATIONS.EDIT_EVENT] && Object.keys(userPermissions).length <= 1;
};
