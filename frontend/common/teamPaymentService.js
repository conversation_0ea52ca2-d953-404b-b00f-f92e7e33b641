angular.module('SportWrench').service('teamPaymentService', ['$http', '$q', '$stateParams', Service]);

function Service ($http, $q, $stateParams) {
    this._$http = $http;
    this._$q = $q;
    this.$stateParams = $stateParams;
}

Service.prototype.createPaymentIntent = function (amount) {
    return this._$http.post(
        '/api/v2/club/purchase',
        { event: this.$stateParams.event, amount }
    ).then(response => response.data);
}

Service.prototype.updatePaymentIntent = function (payment) {
    return this._$http.put(
        '/api/v2/club/purchase',
        { payment, event: this.$stateParams.event}
    );
}

Service.prototype.changePaymentType = function (payment) {
    return this._$http.put(
        '/api/v2/club/purchase/change-type',
        angular.extend({}, payment, {event_id: this.$stateParams.event})
    );
}

Service.prototype.removePaymentSession = function (payment) {
    return this._$http.delete('/api/v2/club/purchase',
        { data: angular.extend({}, payment, { event_id: this.$stateParams.event }) }
    );
}

Service.prototype.createPaymentHubPaymentIntent = function (amount) {
    return this._$http.post(
        '/api/club/v2/purchase/payment-hub',
        { event: this.$stateParams.event, amount }
    ).then(response => response.data);
}

Service.prototype.updatePaymentHubPaymentIntent = function (payment) {
    return this._$http.put(
        '/api/club/v2/purchase/payment-hub',
        angular.extend({}, payment ,{event: this.$stateParams.event})
    );
}

Service.prototype.changePaymentHubPaymentType = function (payment) {
    return this._$http.put(
        '/api/club/v2/purchase/payment-hub/change-type',
        angular.extend({}, payment, {event: this.$stateParams.event})
    );
}
