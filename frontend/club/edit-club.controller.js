angular.module('SportWrench')

.controller('EditClubController', EditClubController);

EditClubController.$inject = [
    '$scope', '$state', 'masterClubService',  '$uibModalInstance', 'geoService', 'sportsService', '$filter', 'userService', 'toastr',
    'APP_ROUTES', 'DateService', 'moment', 'ProfileRestoreAlertService', 'USER_ROLE', 'CLUB_ADDRESSES_BLACKLIST_REGEXES', 'COUNTRY_CODES'
];

function EditClubController (
    $scope, $state, masterClubService,  $uibModalInstance, geoService, sportsService, $filter, userService, toastr,
    APP_ROUTES, DateService, moment, ProfileRestoreAlertService, USER_ROLE, CLUB_ADDRESSES_BLACKLIST_REGEXES, COUNTRY_CODES
) {
    var initialClub     = {},
        editMode        = false;

    let usavCodeRestoredFromLS = false;

    $scope.master_club  = {
        sport_variations        : [],
        sport_sanctionings      : [],
        has_male_teams          : false,
        has_female_teams        : false,
        country                 : userService.getCountry(),
        sport_id                : '2'
    };

    // Value should not be in public.sport_sanctioning
    const US_PHONE_CODE = 1;
    let ACTIVE_SANC_IDS = [];

    $scope.utils = {
        required_error      : 'This Field is required',
        one_value_error     : 'Choose at least one value',
        submitted           : false,
        director_usav       : /^([A-Z]{2}\d{7}[A-Z]{2,3}\d{2,3}|\d{7})$/i,
        isPickedOpened      : false,
        pickerSettings      : { datepickerMode: 'year' },
        showProfileRestoreAlert: false,
        notValidFieldsFromServer: [],
    };

    $scope.geo_data     = {};
    $scope.title        = 'Club Profile';
    $scope.disable_btn  = false;

    $scope.sport_variations = [];
    $scope.sanctionings     = [{id: masterClubService.SANCTIONING_IDS.NONE, name: 'None'}];
    $scope.isUSAVSancOriginallyExists = false;
    $scope.isAAUSancOriginallyExists = false;
    $scope.initialClubData      = {}

    let baseSanctionings = [];

    $scope.$watch('master_club.country', (countryCode, oldCountryCode) => {
        geoService.getStates(countryCode).then(function (data) {
            $scope.geo_data.states = data;

            if($scope.geo_data.states.length === 1) {
                $scope.master_club.state = $scope.geo_data.states[0].state;
            } else if($scope.geo_data.states.length === 0) {
                $scope.master_club.state = null;
            }
        });

        geoService.getRegions(countryCode).then(function (data) {
            $scope.geo_data.regions = data;

            if($scope.geo_data.regions.length === 1) {
                $scope.master_club.region = $scope.geo_data.regions[0].region;

                if (countryCode === oldCountryCode) {
                    initialClub.region = $scope.geo_data.regions[0].region;
                }
            } else if($scope.geo_data.regions.length === 0) {
                $scope.master_club.region = null;
            }
        });
    });

    if ($state.is(APP_ROUTES.CD.INFO_UPDATE)) {
        $scope.utils.editMode = editMode = true;

        masterClubService.getClub().then(function (resp) {
            resp.data.club.sport_id = resp.data.club.sport_id
                                                        ?('' + resp.data.club.sport_id)
                                                        :resp.data.club.sport_id;
            $scope.master_club      = resp.data.club;

            $scope.utils.showProfileRestoreAlert = ProfileRestoreAlertService.showAlert(USER_ROLE.CD, $scope.master_club.modified);

            $scope.master_club.director_birthdate
                = moment($scope.master_club.director_birthdate, 'YYYY-MM-DD').toDate();

            let sancs = $scope.master_club.sport_sanctionings;

            if (!(sancs && sancs.length) && editMode)  {
                $scope.master_club.sport_sanctionings = [masterClubService.SANCTIONING_IDS.NONE];
            }

            $scope.initialClubData.code     = $scope.master_club.code;
            $scope.initialClubData.region   = $scope.master_club.region;

            baseSanctionings = $scope.master_club.sport_sanctionings.slice();
            $scope.isUSAVSancOriginallyExists = baseSanctionings.includes(masterClubService.SANCTIONING_IDS.USAV);
            $scope.isAAUSancOriginallyExists = baseSanctionings.includes(masterClubService.SANCTIONING_IDS.AAU);

            initialClub = angular.copy($scope.master_club);
        });
    } else {
        userService.getUser(function (user) {
            $scope.master_club.director_first       = user.first;
            $scope.master_club.director_last        = user.last;
            $scope.master_club.director_email       = user.email;
            $scope.master_club.director_phone       = removeCountryCode(user.phone_mob);
            $scope.master_club.director_usav_code   = '';

            initialClub = angular.copy($scope.master_club);

            if (initialClub.director_birthdate) {
                initialClub.director_birthdate = formatDate(initialClub.director_birthdate);
            }

            $scope.utils.showProfileRestoreAlert = ProfileRestoreAlertService.showAlert(USER_ROLE.CD, $scope.master_club.modified);
        });
    }

    function removeCountryCode (phone) {
        return phone && String(phone).length === 11 && Number(phone.charAt(0)) === US_PHONE_CODE
            ? phone.substr(1)
            : phone;
    }

    geoService.getCountries().then(function (data) {
        $scope.geo_data.countries = data;
    });

    sportsService.getSportVariations({ sport: 2 }, function (resp) {
        $scope.sport_variations = resp.data.sport_variation;
    });

    sportsService.getSportSanctionings({ sport: 2 }, function (resp) {
        ACTIVE_SANC_IDS = resp.data.sport_sanctioning.map(item => item.id);
        $scope.sanctionings = $scope.sanctionings.concat(resp.data.sport_sanctioning);
    });

    $scope.disableMale = function () {
        return editMode && initialClub.has_male_teams;
    };

    $scope.disableFemale = function () {
        return editMode && initialClub.has_female_teams;
    };

    $scope.disableCoed = function () {
        return editMode && initialClub.has_coed_teams;
    };

    $scope.hasUsav = function() {
        return $scope.hasSanc(masterClubService.SANCTIONING_IDS.USAV);
    };

    $scope.isUS = function() {
        return $scope.master_club.country === 'US';
    };

    $scope.showUsavRegion = function() {
        return ![COUNTRY_CODES.POLAND, COUNTRY_CODES.TAIWAN, COUNTRY_CODES.HONDURAS, COUNTRY_CODES.COLOMBIA].includes($scope.master_club.country);
    };

    $scope.hasAau = function() {
        return $scope.hasSanc(masterClubService.SANCTIONING_IDS.AAU);
    };

    $scope.aauFieldHasChanged = function() {
        if($scope.hasAau()) {
            return ($scope.master_club.aau_club_code && initialClub.aau_club_code !== $scope.master_club.aau_club_code)
                || ($scope.master_club.aau_primary_zip && initialClub.aau_primary_zip !== $scope.master_club.aau_primary_zip)
                || ($scope.master_club.aau_primary_membership_id && initialClub.aau_primary_membership_id !== $scope.master_club.aau_primary_membership_id)
        }

        return false;
    };

    $scope.changeSanc = function(id) {
        var index = $scope.master_club.sport_sanctionings.indexOf(id);
        if(index < 0) {

            // If sanctioning is not "None" and "None" is checked - uncheck "None"
            if(id !== masterClubService.SANCTIONING_IDS.NONE) {
                let noneIndex = $scope.master_club.sport_sanctionings.indexOf(masterClubService.SANCTIONING_IDS.NONE);

                if(noneIndex >= 0) {
                    $scope.master_club.sport_sanctionings.splice(noneIndex, 1);
                }
            }

            $scope.master_club.sport_sanctionings.push(id);
        } else {
            $scope.master_club.sport_sanctionings.splice(index, 1);
        }

    };

    $scope.changeVar = function(id) {
        var index = $scope.master_club.sport_variations.indexOf(id);
        if(index < 0) {
            $scope.master_club.sport_variations.push(id);
        } else {
            $scope.master_club.sport_variations.splice(index, 1);
        }
    };

    $scope.hasVariation = function(id) {
        if(!$scope.master_club || !$scope.master_club.sport_variations)
            return false;
        return ($scope.master_club.sport_variations.indexOf(id) >= 0)?true:false;
    };

    var isNoneDisabled = function (sancs) {
        for (let i = 0; i < sancs.length; ++i) {
           if (ACTIVE_SANC_IDS.includes(sancs[i])) {
               return true;
           }
        }
        return false;
    }

    $scope.isSancDisabled = function (sancID) {
        let sancs   = $scope.master_club.sport_sanctionings;

        if ($scope.utils.editMode) {
            if (sancID === masterClubService.SANCTIONING_IDS.NONE) {
                return sancs.length > 0;
            } else {
                return (baseSanctionings.indexOf(sancID) >= 0)
            }
        } else {
            if (sancID === masterClubService.SANCTIONING_IDS.NONE) {
                return isNoneDisabled(sancs);
            }
        }

        return false;
    };

    $scope.isUsavSancAdded = function () {
        return !$scope.isUSAVSancOriginallyExists && $scope.hasUsav();
    }

    $scope.isAauSancAdded = function () {
        return !$scope.isAAUSancOriginallyExists && $scope.hasAau();
    }

    $scope.getUSAVCode = function () {
        if (!$scope.isUSAVSancOriginallyExists) {
            if ($scope.hasUsav()) {
                return null;
            } else {
                return $scope.initialClub.code;
            }
        }
        return $scope.isUsavSancAdded() ? null : $scope.master_club.code;
    }

    $scope.getAAUCode = function () {
        if (!$scope.isAAUSancOriginallyExists) {
            if ($scope.hasAau()) {
                return null;
            } else {
                return $scope.initialClub.aau_club_code;
            }
        }
        return $scope.isAauSancAdded() ? null : $scope.master_club.aau_club_code;
    }

    $scope.getAauPrimaryMembershipId = function () {
        if (!$scope.isAAUSancOriginallyExists) {
            if ($scope.hasAau()) {
                return null;
            } else {
                return $scope.initialClub.aau_primary_membership_id;
            }
        }
        return $scope.isAauSancAdded() ? null : $scope.master_club.aau_primary_membership_id;
    }

    $scope.getAauPrimaryZip = function () {
        if (!$scope.isAAUSancOriginallyExists) {
            if ($scope.hasAau()) {
                return null;
            } else {
                return $scope.initialClub.aau_primary_zip;
            }
        }
        return $scope.isAauSancAdded() ? null : $scope.master_club.aau_primary_zip;
    }

    $scope.getUSAVRegion = function () {
        return $scope.isUsavSancAdded() ? null : $scope.master_club.region;
    }

    $scope.onClubUSAVCodeChange = function (code) {
        $scope.master_club.code = code;
    }

    $scope.onClubAAUCodeChange = function (code) {
        $scope.master_club.aau_club_code = code;
    }

    $scope.onAauPrimaryMembershipIdChange = function (membership) {
        $scope.master_club.aau_primary_membership_id = membership;
    }

    $scope.onAauPrimaryZipChange = function (zip) {
        $scope.master_club.aau_primary_zip = zip;
    }

    $scope.onClubUSAVRegionChange = function (region) {
        $scope.master_club.region = region;
    }

    $scope.hasSanc = function(id) {
        let sport_sanctionings = $scope.master_club.sport_sanctionings;

        if(!$scope.master_club || !sport_sanctionings) {
            return false;
        }

        return (sport_sanctionings.indexOf(id) >= 0);
    };

    $scope.dropdown_filer = function(name) {
        return (name ===  'Other')?-2:0;
    };

    $scope.submit = function() {
        if($scope.disable_btn === true) {
            return;
        }

        $scope.utils.notValidFieldsFromServer = [];

        if($scope.master_club.country === 'US' && !$scope.master_club.state) {
            $scope.createMCForm.state.$setValidity('required', false);
        }

        if (validateAddress(filterAddress($scope.master_club.address), CLUB_ADDRESSES_BLACKLIST_REGEXES)) {
            $scope.createMCForm.address.$setValidity('required', false);
        }

        if(!$scope.hasUsav()) {
            if($scope.master_club.code) {
                delete $scope.master_club.code;
            }
        }

        if(!$scope.hasAau()) {
            if($scope.master_club.aau_club_code) {
                delete $scope.master_club.aau_club_code;
            }
        }

        if ($scope.createMCForm.$invalid) {
            toastr.warning('Invalid Form Data');
            return;
        }

        $scope.disable_btn = true;

        var mc                  = angular.copy($scope.master_club, {});
        mc.club_name            = mc.club_name.replace(/(\r\n|\n|\r)/gm,'');
        mc.team_prefix          = mc.team_prefix.replace(/(\r\n|\n|\r)/gm,'');
        mc.director_usav_code   = mc.director_usav_code?mc.director_usav_code.toUpperCase():'';
        mc.sport_id             = parseInt(mc.sport_id, 10);
        mc.director_birthdate   = moment(mc.director_birthdate).format('YYYY-MM-DD');

        var submitPromise;
        if (editMode) {
            const sportSanctioningsToUpdate = getSportSanctioningsToUpdate();

            let fieldsToOmit = ['sport_variations', 'sport_sanctionings', 'sport', 'allow_xlsx_import',
                'sport_id', 'director_name', 'sport_sanctionings_str', 'sport_variations_str', 'modified',
                'master_club_id',
            ];

            if (!sportSanctioningsToUpdate.includes(masterClubService.SANCTIONING_IDS.USAV)) {
                fieldsToOmit.push('code');
                fieldsToOmit.push('region');
            }

            mc = _.omit(mc, ...fieldsToOmit);
            mc.has_male_teams   = (mc.has_male_teams !== initialClub.has_male_teams)?mc.has_male_teams:undefined;
            mc.has_female_teams = (mc.has_female_teams !== initialClub.has_female_teams)?mc.has_female_teams:undefined;
            mc.has_coed_teams   = (mc.has_coed_teams !== initialClub.has_coed_teams)?mc.has_coed_teams:undefined;
            mc.sport_sanctionings = sportSanctioningsToUpdate.length ? sportSanctioningsToUpdate : [];

            //Temporary solution for SW-3705, need to be refactored
            mc.has_aau_sanctioning = $scope.aauFieldHasChanged();

            if (!$scope.isUsavSancAdded()) {
                mc.code     = $scope.initialClubData.code;
                mc.region   = $scope.initialClubData.region;
            }

            submitPromise = masterClubService.updateClub(mc);
        } else {
            if( !(mc.has_male_teams || mc.has_female_teams || mc.has_coed_teams)) {
                $scope.createMCForm.team_genders.$setValidity('required', false);
                angular.element('.ng-invalid').focus();
                $scope.disable_btn = false;
                return;
            }
            submitPromise = masterClubService.createClub(mc);
        }

        submitPromise.then(function () {
            ProfileRestoreAlertService.removeUnsavedFieldsFromLocalStorage(USER_ROLE.CD);
            $scope.utils.submitted = true;
            $uibModalInstance.close();
            toastr.success('Successfully updated');
        }, function ({ data: { not_valid_fields }}) {
            $scope.utils.notValidFieldsFromServer = not_valid_fields || [];
            $scope.disable_btn = false;
        });
    };

    $scope.fieldValidationFromServer = function(fieldName) {
        return $scope.utils.notValidFieldsFromServer.includes(fieldName);
    }

    $scope.regionHasValidationError = function(fieldName) {
        return $scope.fieldValidationFromServer(fieldName);
    }

    $scope.clubUsavCodeHasValidationError = function(fieldName) {
        return $scope.fieldValidationFromServer(fieldName);
    }

    $scope.clubAauCodeHasValidationError = function(fieldName) {
        return $scope.fieldValidationFromServer(fieldName);
    }

    $scope.aauPrimaryMembershipIdHasValidationError = function(fieldName) {
        return $scope.fieldValidationFromServer(fieldName);
    }

    $scope.aauPrimaryZipHasValidationError = function(fieldName) {
        return $scope.fieldValidationFromServer(fieldName);
    }

    $scope.openSupport = function () {
        $uibModalInstance.close(APP_ROUTES.SUPPORT.ASK);
    };

    $scope.disableRegion = function() {
        const sport_sanctionings = getSportSanctioningsToUpdate();

        const rules = [
            ($scope.geo_data.regions && $scope.geo_data.regions.length < 1),
            $scope.utils.editMode && !sport_sanctionings.includes(masterClubService.SANCTIONING_IDS.USAV),
        ];

        return rules.some(rule => rule);
    };

    $scope.restoreProfileData = function() {
        $scope.master_club = ProfileRestoreAlertService.joinUnsavedFieldsWithProfile(USER_ROLE.CD, $scope.master_club);

        if ($scope.master_club.director_birthdate && _.isNumber($scope.master_club.director_birthdate)) {
            $scope.master_club.director_birthdate = moment.unix($scope.master_club.director_birthdate, 'YYYY-MM-DD').toDate();
        }

        if ($scope.master_club.code && (initialClub.code !== $scope.master_club.code)) {
            $scope.isUSAVSancOriginallyExists = $scope.master_club.sport_sanctionings.includes(masterClubService.SANCTIONING_IDS.USAV);
            $scope.initialClubData.code = $scope.master_club.code;

            usavCodeRestoredFromLS = true;
        }

        if ($scope.master_club.aau_club_code && (initialClub.aau_club_code !== $scope.master_club.aau_club_code)) {
            $scope.isAAUSancOriginallyExists = $scope.master_club.sport_sanctionings.includes(masterClubService.SANCTIONING_IDS.AAU);
            $scope.initialClubData.aau_club_code = $scope.master_club.aau_club_code;
        }

        ProfileRestoreAlertService.removeUnsavedFieldsFromLocalStorage(USER_ROLE.CD);

        $scope.utils.showProfileRestoreAlert = false;
    };

    $scope.disableUsav = function() {
        return $scope.isUSAVSancOriginallyExists && !usavCodeRestoredFromLS;
    };

    $uibModalInstance.result.then(function (stateName) {
        $state.go((stateName || '^'), null, {
            reload: true,
            inherit: true,
            notify: true
        });
    }, function() {
         $state.go('^');
    });

    function getSportSanctioningsToUpdate () {
        return $scope.master_club.sport_sanctionings.filter(x => !baseSanctionings.includes(x));
    }

    const formatDate = function(date) {
        return moment(date).format('YYYY-MM-DD');
    };

    const onModalClosing = function() {
        if ($scope.utils.submitted) {
            return;
        }

        const master_club = angular.copy($scope.master_club);

        if (master_club.director_birthdate) {
            master_club.director_birthdate = moment(master_club.director_birthdate).unix();

            initialClub.director_birthdate = moment(initialClub.director_birthdate).unix();
        }

        const unsavedFields = ProfileRestoreAlertService.getUnsavedProfileFields(initialClub, master_club);

        if (!_.isEmpty(unsavedFields)) {
            ProfileRestoreAlertService.saveUnsavedFieldsToLocalStorage(USER_ROLE.CD, unsavedFields, master_club.modified);
        }
    };

    const validateAddress = (address, notAllowedAddresses) => {
        return notAllowedAddresses.some(addressRegex => addressRegex.test(address));
    };

    const filterAddress = (address) => {
        const replaceRegex = /[\s,.-]/g;

        return address.split(' ')
            .map(subString => subString.replace(replaceRegex, ''))
            .filter(subString => subString)
            .join(' ')
            .toLowerCase();
    };

    $scope.$on('modal.closing', onModalClosing);

    // detect page reload and closing tab
    window.onbeforeunload = function() {
        onModalClosing();
    };

    $scope.$on('$destroy', function() {
        window.onbeforeunload = null;
    });

    $scope.getPhoneMask = function() {
        const PHONE_MASKS = {
            [COUNTRY_CODES.HONDURAS]: '9999 9999',
            [COUNTRY_CODES.POLAND]: '999 999 999'
        };

        return PHONE_MASKS[$scope.master_club.country] || '(*************';
    };

}
