
const INVALID_FLOW_MESSAGE = 'Something went wrong! Check the list of payments and your bank statements in a ' +
    'few minutes and retry the payment if it is not successful!';
const MAX_RETRIES = 4;
const RETRIES_INTERVAL = 3 * 1000;

class Controller {
    constructor (
        $q, purchaseService, PaymentCardService, $location, $scope, $rootScope, PAYMENT_TYPE, $window, toastr,
        $timeout, $interval, teamPaymentService
    ) {
        this.$q = $q;
        this.$location = $location;
        this.purchaseService = purchaseService;
        this.$scope = $scope;
        this.PAYMENT_TYPE = PAYMENT_TYPE;
        this.$window = $window;
        this.toastr = toastr;
        this.$timeout = $timeout;
        this.$interval = $interval;
        this.$rootScope = $rootScope;
        this.failedChargeError = INVALID_FLOW_MESSAGE;

        this._PaymentCardService = PaymentCardService;

        this.returnUrl = this.$location.absUrl();
        this.stripeFormIsReady = false;

        this.teamPaymentService = teamPaymentService;
    }

    $onInit () {
        this.active = this.isOnline() ? 0 : 1;

        this.payment = {
            subtotal: 0,
            surcharge: 0,
            total: 0,
            serviceFee: 0,
            receipt: []
        };

        this.paymentInterval = null;
        this.teamsLoading = true;

        this.usePaymentHub = this.event.use_payment_hub;
        this.usePaymentIntent = !this.usePaymentHub && this.event.use_payment_intents;

        this.isVerticalInsuranceAgree = false;

        this.$q.when(this.teams)
            .then(teamsList => this.teamsList = teamsList)
            .finally(() => this.teamsLoading = false);

    }

    onAmountChanged (amount, receipt, discounts) {
        this.payment.subtotal 	= amount;
        this.payment.receipt 	= receipt;
        this.payment.discounts 	= discounts;

        if (!!this.payment.type) {
            this.recountTotal();
        }
    }

    recountTotal () {
        let totals = this.purchaseService.recountTotal(
            this.payment.subtotal, this.payment.type, this.payment.receipt, this.event, this.teamsList
        );

        this.payment = Object.assign(this.payment, totals);
    }

    onPaymentTypeChange (type) {
        this.lastOnlinePaymentTypeSelected = type;
        this.stripeFormIsReady = true;

        if(this.onlineTypeChangeEnabled) {
            this.payment.type = type;

            this.recountTotal();

            this.$scope.$digest();
        }
    }

    noPaymentType () {
        return !(this.teamsLoading || this.event.check || this.event.card || this.event.ach);
    }

    isOnline () {
        return this.event.card || this.event.ach;
    }

    showVerticalInsuranceForm () {
        return this.event.show_vertical_insurance_form;
    }

    $onDestroy () {
        if(this.paymentInterval) {
            this.$interval.cancel(this.paymentInterval);
        }
    };

    onComplete (paymentDataId, provider = 'stripe') {
        let deferred = this.$q.defer();

        if(!paymentDataId) {
            deferred.resolve();
        } else {
            this.paymentInterval = this.$interval(async () => {

                const payment = await this.purchaseService.getPayment(this.getPaymentParams(paymentDataId, provider));

                if(!_.isEmpty(payment)) {
                    this.payment.id = payment.purchase_id;
                    this.payment.chargeCompleted = true;
                    this.$interval.cancel(this.paymentInterval);

                    this.$rootScope.$broadcast('club.entry.paid');
                }

            }, RETRIES_INTERVAL, MAX_RETRIES);

            this.paymentInterval.finally(() => {
                if(!this.payment.chargeCompleted) {
                    this.payment.chargeFailed = true;

                    this.$rootScope.$broadcast('club.entry.payment.failed');
                }

                deferred.resolve();
            });
        }

        return deferred.promise;
    }

    onPaymentConfirmStarted() {
        this.$rootScope.$broadcast('team.payment.in-progress');
    }

    onPaymentProcessed() {
        this.$rootScope.$broadcast('team.payment.processed');
    }

    getPaymentParams(paymentDataId, provider = 'stripe') {
        if(provider === 'stripe') {
            return {
                paymentIntentId: paymentDataId
            }
        }

        return {
            paymentHubPaymentIntentId: paymentDataId
        }
    }

    onCheckTypeChanged () {
        this.onlineTypeChangeEnabled = false;
        this.payment.type = this.PAYMENT_TYPE.CHECK;

        this.recountTotal();
    }

    onOnlineTypeChanged () {
        this.onlineTypeChangeEnabled = true;

        if(this.lastOnlinePaymentTypeSelected) {
            this.payment.type = this.lastOnlinePaymentTypeSelected;
        }

        if(this.stripeFormIsReady) {
            this.recountTotal();
        }
    }

    payByCheck () {
        this.payment.inProgress = true;

        return this.payCheck({payment: this.payment})
            .then((id) => {
                this.payment.id = id;
                this.payment.chargeCompleted = true
            })
            .finally(() => this.payment.inProgress = false);
    }

    verticalInsuranceDecisionChange (quote, isAgree) {
        this.isVerticalInsuranceAgree = isAgree;

        if(this.isVerticalInsuranceAgree) {
            this.payment.verticalInsuranceQuote = quote;
        } else {
            this.payment.verticalInsuranceQuote = undefined;
        }
    }

    getInlineNoticeMessage() {
        return `
            You will be charged the <b>Team Registration Fee</b> as well as the <b>Insurance
            Coverage Fee.</b> The <b>Insurance Coverage Fee</b> will be withdrawn <b>separately</b> using the same payment method.`;
    }
}

Controller.$inject = [
    '$q', 'purchaseService', 'PaymentCardService', '$location', '$scope', '$rootScope', 'PAYMENT_TYPE', '$window',
    'toastr', '$timeout', '$interval', 'teamPaymentService'
];

angular.module('SportWrench').component('teamPaymentForm', {
    templateUrl: 'components/team-payment-form/template.html',
    bindings: {
        event: '<',
        teams: '<',
        typeChangePayment: '<',
        onPaymentComplete: '&',
        payCheck: '&'
    },
    controller: Controller
});
