<div ng-show="$ctrl.teams.length == undefined">
    <img class="loading-image" src="/images/loading-bubbles.svg" width="64" height="64" alt="">
</div>
<div ng-show="!$ctrl.teams.length">0 teams</div>
<a ui-sref="events.event.divisions.division.divisionteams.divisionteam(
    {division: team.division_id, team: team.roster_team_id}
    )"
   class="list-group-item list-group-item-info"
   ng-repeat-start="team in $ctrl.teams">
<span class="club-line clearfix">
  <span class="list-text-right-strict pull-right hidden-xs">
    <span class="span-right-strict">
      <span ng-if="team.matches_won || team.matches_lost">{{team.matches_won}}-{{team.matches_lost}}</span>
      <span ng-if="team.sets_won || team.sets_lost">({{team.sets_won}}-{{team.sets_lost}})</span>
    </span>
    <span class="list-arrow glyphicon glyphicon-chevron-right"></span>
  </span>
  <span class="info">
    <genders
    m="team.gender === 'male'"
    f="team.gender === 'female' || team.gender === 'coed'"
    ></genders>
    <span class="division-short-name">{{team.division_short_name}}</span>
    <span ng-if="team.ds_rank">{{team.ds_rank | suffix}}</span>
  </span>
  <span class="name underlined">{{team.team_name}}</span>
</span>
</a>

<a ng-repeat="match in team.upcoming | limitTo: 2"
   class="list-group-item"
   ui-sref="events.event.matches.match({match: match.match_id})"
>
<div title="{{ match.date_start_formatted }}">
  <div class="clearfix">
    <div class="match-info-list">
      <i class="match-info-icon"
         ng-class="{'fa fa-flag': match.match_type == 'ref', 'fa fa-play-circle-o': match.match_type != 'ref'}"></i>
      <span class="list-arrow glyphicon glyphicon-chevron-right"></span>
      <span>
          Next
          <span ng-bind="match.match_type == 'ref'? 'ref' : 'match'"></span>
          {{ match.date_start_formatted }} {{ match.court_name }}
          <span ng-show="match.match_type == 'ref'"> - Officiate</span>
      </span>
      <br/>
      <span class="match-opponent" ng-show="match.match_type != 'ref'">
          Opponent <b>{{ match.opponent_team_name }}</b> ({{ match.opponent_organization_code }})
      </span>
    </div>
  </div>
</div>
</a>
<span ng-repeat-end></span>
