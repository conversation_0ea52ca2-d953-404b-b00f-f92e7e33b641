
class POSPaymentProviderAccountEntityService {
    constructor(APIService, StorageService) {
        this.APIService = APIService;
        this.StorageService = StorageService;
    }

    async createIfNotExists(eventID, pointOfSalesType, eventPaymentProviderAccountID) {
        const eventData = await this.StorageService.getEventData(eventID, pointOfSalesType);

        if(this.#providerAccountNotSetOrChanged(eventData, eventPaymentProviderAccountID)) {
            if(eventData.paymentProviderAccountID) {
                await this.APIService.delete(eventData.pointOfSalesID, eventData.paymentProviderAccountID);
            }

            const dataForCreateRequest = this.#preparePoSPaymentProviderAccountData(
                eventData,
                eventPaymentProviderAccountID
            );

            await this.APIService.create(eventData.pointOfSalesID, dataForCreateRequest);
            await this.StorageService.create(eventData.pointOfSalesID, eventPaymentProviderAccountID);
        }
    }

    #preparePoSPaymentProviderAccountData(data, paymentProviderAccountID) {
        if(paymentProviderAccountID) {
            data.paymentProviderAccountId = paymentProviderAccountID;
        }

        return _.omit(data, 'pointOfSalesID', 'paymentProviderAccountID', 'stripeAccountID');
    }

    #providerAccountNotSetOrChanged(eventData, eventPaymentProviderAccountID) {
        return !eventData.paymentProviderAccountID ||
            eventPaymentProviderAccountID !== !eventData.paymentProviderAccountID;
    }
}

module.exports = POSPaymentProviderAccountEntityService;
