'use strict';

const sinon = require('sinon');
const chai = require('chai');
const expect = chai.expect;

// Mock sails global for isolated testing
const mockSails = {
    config: {
        connections: {
            redis: process.env.REDIS_URL || 'redis://itrdev.lan:6379'
        },
        volleyStation: {
            apiKey: 'test-api-key-123',
            rateLimit: {
                read: {
                    points: 100,
                    duration: 60,
                    blockDuration: 300
                }
            }
        }
    },
    once: sinon.stub(),
    services: {}
};

// Mock loggers global for isolated testing
const mockLoggers = {
    debug_log: {
        verbose: sinon.stub(),
        warn: sinon.stub()
    },
    errors_log: {
        error: sinon.stub()
    }
};

// Set up globals before requiring the service
global.sails = mockSails;
global.loggers = mockLoggers;

const RateLimiterService = require('../../api/services/RateLimiterService');

// Make RateLimiterService available globally for policies
global.RateLimiterService = RateLimiterService;

describe('RateLimiterService - Integration Tests', function () {
    beforeEach(function () {
        // Reset all stubs
        Object.values(mockLoggers.debug_log).forEach(stub => stub.resetHistory());
        Object.values(mockLoggers.errors_log).forEach(stub => stub.resetHistory());
        mockSails.once.resetHistory();

        // Ensure global references are properly set
        global.loggers = mockLoggers;
        global.sails = mockSails;

        // Reset RateLimiterService state to ensure clean state between tests
        RateLimiterService._client = null;
        RateLimiterService._initPromise = null;
    });

    describe('Volley Station Policy Integration', function () {
        let rateLimitReadPolicy, rateLimitWritePolicy;

        before(function () {
            // Load the actual policies
            rateLimitReadPolicy = require('../../api/policies/volley-station/rateLimitRead');
            rateLimitWritePolicy = require('../../api/policies/volley-station/rateLimitWrite');
        });

        it('should create rate limiter with correct configuration for read policy', function () {
            const mockReq = {
                method: 'GET',
                path: '/api/volley-station/v1/events/123/schedule',
                user: { clientId: 'test-client-read' }
            };
            const mockRes = {
                set: sinon.stub(),
                status: sinon.stub().returnsThis(),
                json: sinon.stub()
            };
            const mockNext = sinon.stub();

            // Test that the policy creates a rate limiter
            expect(typeof rateLimitReadPolicy).to.equal('function');
            
            // The policy should create a rate limiter middleware
            const result = rateLimitReadPolicy(mockReq, mockRes, mockNext);
            expect(result).to.be.a('promise');
        });

        it('should create rate limiter with correct configuration for write policy', function () {
            const mockReq = {
                method: 'POST',
                path: '/api/volley-station/v1/events/123/update',
                user: { clientId: 'test-client-write' }
            };
            const mockRes = {
                set: sinon.stub(),
                status: sinon.stub().returnsThis(),
                json: sinon.stub()
            };
            const mockNext = sinon.stub();

            // Test that the policy creates a rate limiter
            expect(typeof rateLimitWritePolicy).to.equal('function');
            
            // The policy should create a rate limiter middleware
            const result = rateLimitWritePolicy(mockReq, mockRes, mockNext);
            expect(result).to.be.a('promise');
        });

        it('should use the same rate limiter instance for multiple calls to read policy', function () {
            const mockReq = {
                method: 'GET',
                path: '/api/volley-station/v1/events/123/schedule',
                user: { clientId: 'test-client-cached' }
            };
            const mockRes = {
                set: sinon.stub(),
                status: sinon.stub().returnsThis(),
                json: sinon.stub()
            };
            const mockNext = sinon.stub();

            // First call should create the rate limiter
            const firstCall = rateLimitReadPolicy(mockReq, mockRes, mockNext);
            expect(firstCall).to.be.a('promise');

            // Second call should reuse the same rate limiter
            const secondCall = rateLimitReadPolicy(mockReq, mockRes, mockNext);
            expect(secondCall).to.be.a('promise');

            // The rate limiter should be cached in the module (check if it exists)
            expect(rateLimitReadPolicy.rateLimiter).to.exist;
            expect(typeof rateLimitReadPolicy.rateLimiter).to.equal('function');
        });

        it('should handle auth flow integration correctly', function () {
            // Load the auth policy
            const authPolicy = require('../../api/policies/volley-station/auth');
            
            const mockReq = {
                get: sinon.stub().returns('test-api-key-123'),
                user: {}
            };
            const mockRes = {
                status: sinon.stub().returnsThis(),
                json: sinon.stub()
            };
            const mockNext = sinon.stub();

            // Test auth policy
            authPolicy(mockReq, mockRes, mockNext);
            
            // Should set clientId on user
            expect(mockReq.user.clientId).to.be.a('string');
            expect(mockNext.calledOnce).to.be.true;
        });

        it('should generate consistent client IDs for the same API key', function () {
            const authPolicy = require('../../api/policies/volley-station/auth');
            
            const mockReq1 = {
                get: sinon.stub().returns('test-api-key-123'),
                user: {}
            };
            const mockReq2 = {
                get: sinon.stub().returns('test-api-key-123'),
                user: {}
            };
            const mockRes = {
                status: sinon.stub().returnsThis(),
                json: sinon.stub()
            };
            const mockNext = sinon.stub();

            // Process both requests
            authPolicy(mockReq1, mockRes, mockNext);
            authPolicy(mockReq2, mockRes, mockNext);
            
            // Should generate the same clientId for the same API key
            expect(mockReq1.user.clientId).to.equal(mockReq2.user.clientId);
        });

        it('should generate different client IDs for different API keys', function () {
            const authPolicy = require('../../api/policies/volley-station/auth');
            
            const mockReq1 = {
                get: sinon.stub().returns('test-api-key-123'),
                user: {}
            };
            const mockReq2 = {
                get: sinon.stub().returns('test-api-key-456'),
                user: {}
            };
            const mockRes = {
                status: sinon.stub().returnsThis(),
                json: sinon.stub()
            };
            const mockNext = sinon.stub();

            // Process both requests
            authPolicy(mockReq1, mockRes, mockNext);
            authPolicy(mockReq2, mockRes, mockNext);
            
            // Should generate different clientIds for different API keys
            expect(mockReq1.user.clientId).to.not.equal(mockReq2.user.clientId);
        });
    });

    describe('Configuration Integration', function () {
        it('should use configuration from sails.config.volleyStation', function () {
            // Verify the configuration is properly structured
            expect(mockSails.config.volleyStation).to.be.an('object');
            expect(mockSails.config.volleyStation.rateLimit).to.be.an('object');
            expect(mockSails.config.volleyStation.rateLimit.read).to.be.an('object');
            expect(mockSails.config.volleyStation.rateLimit.read.points).to.be.a('number');
            expect(mockSails.config.volleyStation.rateLimit.read.duration).to.be.a('number');
        });

        it('should handle missing configuration gracefully', function () {
            // Temporarily remove configuration
            const originalConfig = mockSails.config.volleyStation;
            delete mockSails.config.volleyStation;

            try {
                // This should not crash even without configuration
                const middleware = RateLimiterService.createRateLimiter(
                    'test-no-config',
                    { points: 10, duration: 60 },
                    (req) => req.user && req.user.clientId
                );
                expect(typeof middleware).to.equal('function');
            } finally {
                // Restore configuration
                mockSails.config.volleyStation = originalConfig;
            }
        });
    });

    describe('Error Response Format', function () {
        it('should return proper error format when rate limited', async function () {
            const config = { points: 1, duration: 60 };
            const keySelector = (req) => req.user.clientId;
            
            // Mock a rate limiter that always throws rate limit error
            const originalCreateRateLimiter = RateLimiterService._createRateLimiter;
            RateLimiterService._createRateLimiter = function() {
                return {
                    consume: () => {
                        const error = new Error('Rate limit exceeded');
                        error.remainingPoints = 0;
                        error.msBeforeNext = 30000;
                        return Promise.reject(error);
                    }
                };
            };

            const middleware = RateLimiterService.createRateLimiter('test-error-format', config, keySelector);
            
            const mockReq = {
                method: 'GET',
                path: '/api/volley-station/test',
                user: { clientId: 'test-client' }
            };
            const mockRes = {
                set: sinon.stub(),
                status: sinon.stub().returnsThis(),
                json: sinon.stub()
            };
            const mockNext = sinon.stub();

            // Initialize client
            RateLimiterService._client = { status: 'ready' };

            await middleware(mockReq, mockRes, mockNext);

            // Should return 429 status
            expect(mockRes.status.calledWith(429)).to.be.true;
            
            // Should return proper error format
            expect(mockRes.json.calledOnce).to.be.true;
            const errorResponse = mockRes.json.firstCall.args[0];
            expect(errorResponse).to.have.property('error', 'rate_limited');
            expect(errorResponse).to.have.property('rateLimiter', 'test-error-format');
            expect(errorResponse).to.have.property('retryAfterMs');

            // Restore
            RateLimiterService._createRateLimiter = originalCreateRateLimiter;
        });
    });
});
