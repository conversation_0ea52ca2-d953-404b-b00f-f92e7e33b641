'use strict';

require('./teams-payments/refunds/__FullRefundService.spec');
require('./teams-payments/payments/us-bank-account/USBankAccountPayments.spec');
require('./teams-payments/payments/change-teams-payment-type/PaymentIntentChangeTeamsPaymentType.spec');

const eventRow 			= require('./fixture/TeamsPaymentService/event.row');
const divisionRows 		= require('./fixture/TeamsPaymentService/division.rows');
const rosterTeamRows 	= require('./fixture/TeamsPaymentService/roster_team.rows');
const purchaseRow 		= require('./fixture/TeamsPaymentService/purchase.row');
const purchaseTeamRows 	= require('./fixture/TeamsPaymentService/purchase_team.rows');
const {FEE_PAYER} = require("../../api/constants/payments");

function prepareDB () {
	return Promise.all([
		Db.query(squel.insert().into('event').setFields(eventRow).toString()),
		Db.query(squel.insert().into('division').setFieldsRows(divisionRows).toString()),
		Db.query(squel.insert().into('roster_team').setFieldsRows(rosterTeamRows).toString()),
		Db.query(squel.insert().into('purchase').setFields(purchaseRow).toString()),
		Db.query(squel.insert().into('purchase_team').setFieldsRows(purchaseTeamRows).toString())
	]);
}

function clearDB () {
	return Promise.all([
		Db.query('DELETE FROM "event" WHERE "event_id" = $1', [eventRow.event_id]),
		Db.query('DELETE FROM "division" WHERE "event_id" = $1', [eventRow.event_id]),
		Db.query('DELETE FROM "roster_team" WHERE "event_id" = $1', [eventRow.event_id]),
		Db.query('DELETE FROM "purchase" WHERE "event_id" = $1', [eventRow.event_id]),
		Db.query('DELETE FROM "purchase_team" WHERE "purchase_id" = $1', [purchaseRow.purchase_id]),
	]);
}

function findEventRow (eventID) {
	return Db.query('SELECT "teams_escrow_collected" FROM "event" WHERE "event_id" = $1', [eventID])
	.then(res => res.rows[0]);
}

function changeTeamsTargetBalance (eventID, targetBalance) {
	return Db.query(
		`UPDATE "event"
		 SET "teams_sw_target_balance" = $2
		 WHERE "event_id" = $1`,
		[eventID, targetBalance]
	).then(() => {})
}

describe('TeamsPaymentService', function () {
	let service;

	before(() => {
		service = global.sails.services.teamspaymentservice;

		return prepareDB();
	});

	after(() => clearDB());

	context('recountPrice()', function () {

		let defaultTeam = {
			id 				: 1,
			name 			: 'Default Team',
			reg_fee 		: 700,
			paid 			: 0,
			discount		: 0,
			sw_fee 			: 7,
			division_fee	: null,
			division_id 	: 1
		};

		let discountedTeam = {
			id 				: 2,
			name 			: 'Discounted Team',
			reg_fee 		: 700,
			paid 			: 0,
			discount		: 100,
			sw_fee 			: 7,
			division_fee	: null,
			division_id 	: 1
		};

		let paidTeam = {
			id 				: 3,
			name 			: 'Paid Team',
			reg_fee 		: 700,
			paid 			: 350,
			discount		: 50,
			sw_fee 			: 7,
			division_fee	: null,
			division_id 	: 1
		};

		let surchargePerTeam = 10;

		it('should count totals', () => {
			let res = service.recountPrice({teams: [defaultTeam, discountedTeam, paidTeam]}, [1, 2, 3], surchargePerTeam);

			// total, surcharge, netProfit, swFee, purchaseTeams	

			res.total.should.be.equal(1630);
			res.surcharge.should.be.equal(30);
			res.netProfit.should.be.equal(1609);
			res.swFee.should.be.equal(21);
			res.purchaseTeams.should.be.instanceof(Array);
			res.purchaseTeams.length.should.be.equal(3);	

			let firstPurchasedTeam = res.purchaseTeams[0];	

		    firstPurchasedTeam.name.should.be.equal(defaultTeam.name);
		    firstPurchasedTeam.amount.should.be.equal(700);
		    (firstPurchasedTeam.division_fee === null).should.be.true;
		    firstPurchasedTeam.division_id.should.be.equal(defaultTeam.division_id);
		    firstPurchasedTeam.roster_team_id.should.be.equal(defaultTeam.id);
		    firstPurchasedTeam.surcharge.should.be.equal(surchargePerTeam);
		    firstPurchasedTeam.discount.should.be.equal(defaultTeam.discount);
		    firstPurchasedTeam.team_sw_fee.should.be.equal(defaultTeam.sw_fee);
		});

        it('should count totals when SW Teams Fee payer is buyer', () => {
            const settings = {
                teams: [defaultTeam, discountedTeam, paidTeam],
                teams_sw_fee_payer: FEE_PAYER.BUYER
            };
            let res = service.recountPrice(settings, [1, 2, 3], surchargePerTeam);

            res.total.should.be.equal(1651);
            res.surcharge.should.be.equal(30);
            res.netProfit.should.be.equal(1630);
            res.swFee.should.be.equal(21);
            res.purchaseTeams.should.be.instanceof(Array);
            res.purchaseTeams.length.should.be.equal(3);

            let firstPurchasedTeam = res.purchaseTeams[0];

            firstPurchasedTeam.name.should.be.equal(defaultTeam.name);
            firstPurchasedTeam.amount.should.be.equal(700);
            (firstPurchasedTeam.division_fee === null).should.be.true;
            firstPurchasedTeam.division_id.should.be.equal(defaultTeam.division_id);
            firstPurchasedTeam.roster_team_id.should.be.equal(defaultTeam.id);
            firstPurchasedTeam.surcharge.should.be.equal(surchargePerTeam);
            firstPurchasedTeam.discount.should.be.equal(defaultTeam.discount);
            firstPurchasedTeam.team_sw_fee.should.be.equal(defaultTeam.sw_fee);
        });

		it('should throw error if empty teams list passed', () => {
			try {
				service.recountPrice([], [1], surchargePerTeam);
				throw new Error('Empty Teams List test passed');
			} catch (e) {
				e.should.be.instanceof(Error);
				e.message.should.be.equal('Teams list should not be empty');
			}
	 	});

	 	it('should throw error if receipt contains not existing team', () => {
	 		try {
	 			service.recountPrice({ teams: [defaultTeam] }, [7], surchargePerTeam);
	 			throw new Error('Not found team teast passed');
	 		} catch (e) {
	 			e.should.be.instanceof(Error);
	 			e.message.should.be.equal('Team #7 is not available for payment.')
	 		}
	 	});
	})

	context('Refund partial __covertToNum()', function () {

		it('should convert purchase values to number', () => {
			let res = service.refund.partial.__covertToNum({
				purchase_id 	: 1,
				amount 			: '100',
				type 			: 'card',
				charge_id 		: 'charge',
				connect_used 	: false,
				extra_fee 		: '10',
				stripe_secret  	: 'secret',
				stripe_percent  : '0.29',
				stripe_fixed 	: '0.3',
				ach_percent 	: '0.005',
                collected_sw_fee : '10',
                teams_entry_sw_fee: '5',
				stripe_fee 		: null,
				teams: [{
					roster_team_id 	: 1,
					reg_fee 		: '800',
					sw_fee 			: '10'
				}]
			});

			expect(res).to.eql({
				purchase_id 	: 1,
				amount 			: 100,
				type 			: 'card',
				charge_id 		: 'charge',
				connect_used 	: false,
				extra_fee 		: 10,
				stripe_secret  	: 'secret',
				stripe_percent  : 0.29,
				stripe_fixed 	: 0.3,
				ach_percent 	: 0.005,
                collected_sw_fee : 10,
                teams_entry_sw_fee: 5,
				stripe_fee 		: null,
				teams: [{
					roster_team_id 	: 1,
					reg_fee 		: 800,
					sw_fee 			: 10
				}]
			});
		})

	})

	context('Refund partial __getPaymentSettings()', function () {
		let numArrayToStringSpy, 
			covertToNumSpy,
			_tmpNumToArray,
            partialRefundService;

		before(() => {
            partialRefundService = service.refund.partial;
			numArrayToStringSpy = sinon.spy(partialRefundService.__Utils, 'numArrayToString');
			_tmpNumToArray = partialRefundService.__Utils;

			covertToNumSpy = sinon.spy(partialRefundService, '__covertToNum');
		})

		afterEach(() => {
			covertToNumSpy.resetHistory();
			numArrayToStringSpy.resetHistory();
		})

		after(() => {
			numArrayToStringSpy.restore();
            partialRefundService.__Utils = _tmpNumToArray;
		})

		it('should throw error if "items" countains no "roster_team_id"', () => { 
			let fn  = partialRefundService.__getPaymentSettings.bind(
			    partialRefundService, 1, 1, [{ roster_team_id: 'test' }, { roster_team_id: null }]
            );

			expect(fn).to.throw(Error, /Invalid items passed/);
		})

		it('should return null if purchase row not found', () =>
            partialRefundService.__getPaymentSettings(eventRow.event_id, purchaseRow.purchase_id * 100, rosterTeamRows)
			.then(purchase => {
				expect(purchase).to.be.null;

				expect(numArrayToStringSpy.calledOnce).to.be.true;
				expect(covertToNumSpy.callCount).to.equal(0);
			})
		)

        it('should return purchase row and event settings', () =>
            partialRefundService.__getPaymentSettings(eventRow.event_id, purchaseRow.purchase_id, rosterTeamRows)
            .then(purchase => {

                expect(purchase).to.be.a('object');

                expect(purchase).to.eql({
                    amount: 6050,
                    type: 'card',
                    charge_id: 'ch_1929AI2Yt0RbUG0q44kjiM9g',
                    stripe_payment_id: null,
                    stripe_account_id: 'acct_14jVq4EtHbmUxNyG',
                    connect_used: true,
                    do_not_collect_sw_fee: false,
                    extra_fee: null,
                    stripe_secret: null,
                    stripe_percent: 0.027,
                    stripe_fixed: null,
                    ach_percent: null,
					payment_hub_payment_id: null,
      				payment_hub_teams_fee_payer: null,
      				payment_provider: 'stripe',
      				provider_fee: '0',
      				provider_fixed: '0',
      				provider_percentage: '0',
                    stripe_fee: 163.65,
                    collected_sw_fee: 55,
					teams_sw_fee_payer: "seller",
                    teams_entry_sw_fee: 5,
                    total_paid_teams_qty: '11',
					stripe_teams_fee_payer: 'seller',
					teams: [
                        {
                            roster_team_id: 11616,
                            reg_fee: 550,
                            sw_fee: 5,
                            surcharge: 0,
                            amount: 550,
                            name: "Gulfside 12U Navy",
                            discount: 0,
                        },
                        {
                            roster_team_id: 11617,
                            reg_fee: 550,
                            sw_fee: 5,
                            surcharge: 0,
                            amount: 550,
                            name: "Gulfside 12U White",
                            discount: 0,
                        },
                        {
                            roster_team_id: 11618,
                            reg_fee: 550,
                            sw_fee: 5,
                            amount: 550,
                            surcharge: 0,
                            name: "Gulfside 13U Navy",
                            discount: 0,
                        },
                        {
                            roster_team_id: 11619,
                            reg_fee: 550,
                            sw_fee: 5,
                            amount: 550,
                            surcharge: 0,
                            name: "Gulfside 14U Navy",
                            discount: 0,
                        },
                        {
                            roster_team_id: 11620,
                            reg_fee: 550,
                            sw_fee: 5,
                            amount: 550,
                            surcharge: 0,
                            name: "Gulfside 14U Orange",
                            discount: 0,
                        },
                        {
                            roster_team_id: 11621,
                            reg_fee: 550,
                            sw_fee: 5,
                            amount: 550,
                            surcharge: 0,
                            name: "Gulfside 15U Navy",
                            discount: 0,
                        },
                        {
                            roster_team_id: 11622,
                            reg_fee: 550,
                            sw_fee: 5,
                            amount: 550,
                            surcharge: 0,
                            name: "Gulfside 15U White",
                            discount: 0,
                        },
                        {
                            roster_team_id: 11623,
                            reg_fee: 550,
                            sw_fee: 5,
                            amount: 550,
                            surcharge: 0,
                            name: "Gulfside 16U Navy",
                            discount: 0,
                        },
                        {
                            roster_team_id: 11624,
                            reg_fee: 550,
                            sw_fee: 5,
                            amount: 550,
                            surcharge: 0,
                            name: "Gulfside 16U Orange",
                            discount: 0,
                        },
                        {
                            roster_team_id: 11625,
                            reg_fee: 550,
                            sw_fee: 5,
                            amount: 550,
                            surcharge: 0,
                            name: "Gulfside 17U Navy",
                            discount: 0,
                        },
                        {
                            roster_team_id: 11626,
                            reg_fee: 550,
                            sw_fee: 5,
                            amount: 550,
                            surcharge: 0,
                            name: "Gulfside 18U Navy",
                            discount: 0,
                        },
                    ],
                });

                expect(covertToNumSpy.calledOnce).to.be.true;
                expect(numArrayToStringSpy.calledOnce).to.be.true;

            })
        )

    });

	context('_validatePaymentType()', function () {
		
		it('should throw error if payment type is not supported by SW System', () => {
			let fn = service._validatePaymentType.bind(service, 'cash', {});

			expect(fn).to.throw(Error, /Type "cash" is not supported for Teams Payments by SW System/)
		})

		it('should accept card payment when allowed', () => {
			let fn = service._validatePaymentType.bind(service, 'card', { allow_card_payments: true });

			expect(fn).not.to.throw(Error);
		})

		it('should accept check payment when allowed', () => {
			let fn = service._validatePaymentType.bind(service, 'check', { allow_check_payments: true });

			expect(fn).not.to.throw(Error);
		})

		it('should accept ACH payment when allowed', () => {
			let fn = service._validatePaymentType.bind(service, 'ach', { allow_ach_payments: true });

			expect(fn).not.to.throw(Error);
		})

		it('should reject card payment if not allowed', () => {
			let fn = service._validatePaymentType.bind(service, 'card', {});

			expect(fn).to.throw(Error, /Payment of type "card" is not allowed for the Event./);
		})

		it('should reject check payment if not allowed', () => {
			let fn = service._validatePaymentType.bind(service, 'check', {});

			expect(fn).to.throw(Error, /Payment of type "check" is not allowed for the Event./);
		})

		it('should reject ACH payment if not allowed', () => {
			let fn = service._validatePaymentType.bind(service, 'ach', {});

			expect(fn).to.throw(Error, /Payment of type "ach" is not allowed for the Event./);
		})

	})

	context('getSWFeeEscrowTarget()', function () {

		let disputesSpy;

		before(() => {
			disputesSpy = sinon.spy(service, 'countPossibleDisputesEscrow')
		})

		afterEach(() => {
			disputesSpy.resetHistory();
		})

		after(() => {
			disputesSpy.restore();
		})
		
		it('should reject if event id is invalid', () => {
			return service.getSWFeeEscrowTarget('test')
			.should.be.rejectedWith()
		})

		it('should reject if event data not found', () => {
			return service.getSWFeeEscrowTarget(eventRow.event_id * 100)
			.should.be.rejectedWith()
		})

		it('should count escrow target WITH disputes', () => {
			return service.getSWFeeEscrowTarget(eventRow.event_id)
			.then(fee => {
				expect(fee).to.equal(1705);

				expect(disputesSpy.calledOnce).to.be.true;
				expect(disputesSpy.calledWith(eventRow.reg_fee)).to.be.true;
			})
		})

		it('should count escrow target WITHOUT disputes', () => {
			return service.getSWFeeEscrowTarget(eventRow.event_id, true /* skip disputes */)
			.then(fee => {
				expect(fee).to.equal(55);

				expect(disputesSpy.callCount).to.equal(0);
			})
		})

		it('should return summands', () => {
			return service.getSWFeeEscrowTarget(eventRow.event_id, false /* include disputes */, true /* get summands*/)
			.then(res => {
				expect(res).to.be.a('object');
				expect(res.summands).to.be.a('object');

				expect(res.escrow).to.equal(1705);

				expect(res.summands.sw_fee).to.equal(5);
				expect(res.summands.teams_qty).to.equal(11);
				expect(res.summands.reg_fee).to.equal(550);
				expect(res.summands.disputes).to.equal(1650);
				expect(res.summands.additional_balance).to.equal(0);
			})
		})

		it('should take into account "teams_sw_target_balance" value', () => {
			let initialBalance 		= eventRow.teams_sw_target_balance;
			let newBalanceAmount 	= 100;

			return changeTeamsTargetBalance(eventRow.event_id, newBalanceAmount)
			.then(() => {
				return service.getSWFeeEscrowTarget(
							eventRow.event_id, false /* include disputes */, true /* get summands*/)
				.then(res => {
					expect(res).to.be.a('object');
					expect(res.summands).to.be.a('object');	

					expect(res.escrow).to.equal(1805);	

					expect(res.summands.sw_fee).to.equal(5);
					expect(res.summands.teams_qty).to.equal(11);
					expect(res.summands.reg_fee).to.equal(550);
					expect(res.summands.disputes).to.equal(1650);
					expect(res.summands.additional_balance).to.equal(newBalanceAmount);
				})
			})
			.then(() => changeTeamsTargetBalance(eventRow.event_id, initialBalance))
		})

        it('should add SW Fee of the current receipt (4th arg value)', () => {
            let {event_id:id}           = eventRow;
            let skipDisputesEscrow      = true;
            let getSummands             = false;
            let currentReceiptSWFee     = 30;

            return service.getSWFeeEscrowTarget(
                                    id, skipDisputesEscrow, getSummands, currentReceiptSWFee)
            .then(escrowTarget => {
                /* rosterTeamRows.length * eventRow + currentReceiptSWFee; */
                let escrowCheckValue = 55;

                expect(escrowTarget).to.equal(escrowCheckValue);

                expect(disputesSpy.callCount).to.equal(0);
            })
        })

        it('should return statuses information', () => {
            const eventID = eventRow.event_id;
            const skipDisputesEscrow = true;
            const getSummands = false;
            const balanceInformationMode = true;

            return service.getSWFeeEscrowTarget(eventID, skipDisputesEscrow, getSummands, 0, balanceInformationMode)
                .then(response => {
                    expect(response).to.be.a('object');
                    expect(response).has.all.keys('escrow', 'statusesInformation');
                    expect(response.statusesInformation).to.be.a('object');
                    expect(response.statusesInformation).has.all.keys(
                        'accepted_or_paid',
                        'accepted_and_paid',
                        'accepted_only',
                        'paid_only',
                    );
                })
        });

        it('should return statuses information and summands', () => {
            const eventID = eventRow.event_id;
            const skipDisputesEscrow = true;
            const getSummands = true;
            const balanceInformationMode = true;

            return service.getSWFeeEscrowTarget(eventID, skipDisputesEscrow, getSummands, 0, balanceInformationMode)
                .then(response => {
                    expect(response).to.be.a('object');
                    expect(response).has.all.keys('escrow', 'statusesInformation', 'summands');
                })
        })

	})

	context('getCollectedSWFee()', function () {
		
		it('should return collected sw fee amount', () => {
			return service.getCollectedSWFee(eventRow.event_id)
			.then(sw_fee => {
				expect(sw_fee).to.equal(55)
			})
		})

		it('should return collected without additional fee amount', () => {
			return service.getCollectedSWFee(eventRow.event_id, 'collected')
			.then(sw_fee => {
				expect(sw_fee).to.equal(55)
			})
		})

		it('should return additional fee only', () => {
			return service.getCollectedSWFee(eventRow.event_id, 'additional')
			.then(sw_fee => {
				expect(sw_fee).to.equal(0)
			})
		})

		it('should return 0 if no purchase rows found', () => {
			return service.getCollectedSWFee(eventRow.event_id * 100)
			.then(sw_fee => {
				expect(sw_fee).to.equal(0);
			})
		})

		it('should reject if event id is invalid', () => {
			return service.getCollectedSWFee('test')
			.should.be.rejectedWith()
		})

	})

	context('countSWFeeToTakeInPurchase()', function () {
        const feeCollectionSettings = { collect_extra_fee: true };

        it('should throw error if non-numeric target SW fee passed', () => {
			let fn = service.countSWFeeToTakeInPurchase.bind(service, 'test', 0, 1, 1);

			expect(fn).to.throw(Error, /Expecting target SW fee to be a number/);
		})

		it('should throw error if target SW fee < 0', () => {
			let fn = service.countSWFeeToTakeInPurchase.bind(service, -1, 0, 1, 1);

			expect(fn).to.throw(Error, /Expecting target SW fee to be greater or equal zero/);
		})

		it('should throw error if non-numeric collected SW fee passed', () => {
			let fn = service.countSWFeeToTakeInPurchase.bind(service, 0, 'test', 1, 1);

			expect(fn).to.throw(Error, /Expecting collected SW fee to be a number/);
		})

		it('should throw error if collected SW fee < 0', () => {
			let fn = service.countSWFeeToTakeInPurchase.bind(service, 0, -1, 1, 1);

			expect(fn).to.throw(Error, /Expecting collected SW fee to be greater or equal zero/);
		})

		it('should throw error if non-numeric suggested SW fee passed', () => {
			let fn = service.countSWFeeToTakeInPurchase.bind(service, 0, 0, 'test', 1);

			expect(fn).to.throw(Error, /Expecting suggested SW fee to be a number/);
		})

		it('should throw error if suggested SW fee < 0', () => {
			let fn = service.countSWFeeToTakeInPurchase.bind(service, 0, 0, -1, 1);

			expect(fn).to.throw(Error, /Expecting suggested SW fee to be greater than or equal to zero/);
		})

		it('should throw error if non-numeric EO Stripe fee passed', () => {
			let fn = service.countSWFeeToTakeInPurchase.bind(service, 0, 0, 1, 'test', 1);

			expect(fn).to.throw(Error, /Expecting EO Stripe fee to be a number/);
		})

		it('should throw error if EO Stripe fee < 0', () => {
			let fn = service.countSWFeeToTakeInPurchase.bind(service, 0, 0, 1, -1);

			expect(fn).to.throw(Error, /Expecting EO Stripe fee to be greater than zero/);
		})

		it('should throw error if EO Stripe fee is zero', () => {
			let fn = service.countSWFeeToTakeInPurchase.bind(service, 0, 0, 1, 0);

			expect(fn).to.throw(Error, /Expecting EO Stripe fee to be greater than zero/);
		})

        it('should throw error if non-numeric suggested SW Team fee passed', () => {
            let fn = service.countSWFeeToTakeInPurchase.bind(service, 1, 1, 1, 1, 'test', 1);

            expect(fn).to.throw(Error, /Expecting SW Team fee to be a number/);
        })

        it('should throw error if SW Team fee team < 0', () => {
            let fn = service.countSWFeeToTakeInPurchase.bind(service, 1, 1, 1, 1, -1, 1);

            expect(fn).to.throw(Error, /Expecting SW Team fee to be greater than zero/);
        })

        it('should throw error if non-numeric suggested amount passed', () => {
            let fn = service.countSWFeeToTakeInPurchase.bind(service, 1, 1, 1, 1, 1, 'test');

            expect(fn).to.throw(Error, /Expecting amount to be a number/);
        })

        it('should throw error if amount < 0', () => {
            let fn = service.countSWFeeToTakeInPurchase.bind(service, 1, 1, 1, 1, 1, -1);

            expect(fn).to.throw(Error, /Expecting amount to be greater than zero/);
        })

		it(
			'should return application fee equal to stripe fee if difference between target and collected is zero ', 
		 () => {
			let target  	= 10,
				collected  	= 10,
				suggested  	= 3,
				stripeFee  	= 2.3,
                feePerTeam  = 5,
                amount      = 100;

			let res = service.countSWFeeToTakeInPurchase(target, collected, suggested, stripeFee, feePerTeam, amount, feeCollectionSettings);

			expect(res).to.be.a('object');
			expect(res.application_fee).to.equal(stripeFee);
			expect(res.details).to.be.a('object');
			expect(res.details.sw_fee).to.equal(0);
			expect(res.details.extra_fee).to.equal(0);
			expect(res.details.provider_fee).to.equal(stripeFee);
		})

		it('should return application fee equal to stripe fee if difference between target and collected < 0 ', () => {
			let target  	= 10,
				collected  	= 15, /* !!! */
				suggested  	= 3,
				stripeFee  	= 2.3,
                feePerTeam  = 5,
                amount      = 100;

			let res = service.countSWFeeToTakeInPurchase(target, collected, suggested, stripeFee, feePerTeam, amount, feeCollectionSettings);

			expect(res).to.be.a('object');
			expect(res.application_fee).to.equal(stripeFee);
			expect(res.details).to.be.a('object');
			expect(res.details.sw_fee).to.equal(0);
			expect(res.details.extra_fee).to.equal(0);
			expect(res.details.provider_fee).to.equal(stripeFee);
		})

		it('should return correct result if difference between target and collected is less than suggested', () => {
			let target  	= 100,
				collected  	= 80,
				suggested  	= 40,
				stripeFee  	= 10,
                feePerTeam  = 5,
                amount      = 100;

			let res = service.countSWFeeToTakeInPurchase(target, collected, suggested, stripeFee, feePerTeam, amount, feeCollectionSettings);

			expect(res).to.be.a('object');
			expect(res.application_fee).to.equal(30);
			expect(res.details).to.be.a('object');
			expect(res.details.sw_fee).to.equal(20);
			expect(res.details.extra_fee).to.equal(0);
			expect(res.details.provider_fee).to.equal(stripeFee);
		})

		it('should return correct result if difference between target and collected is equal to suggested', () => {
			let target  	= 100,
				collected  	= 80,
				suggested  	= 40,
				stripeFee  	= 10,
                feePerTeam  = 5,
                amount      = 100;

			let res = service.countSWFeeToTakeInPurchase(target, collected, suggested, stripeFee, feePerTeam, amount, feeCollectionSettings);

			expect(res).to.be.a('object');
			expect(res.application_fee).to.equal(30);
			expect(res.details).to.be.a('object');
			expect(res.details.sw_fee).to.equal(20);
			expect(res.details.extra_fee).to.equal(0);
			expect(res.details.provider_fee).to.equal(stripeFee);
		})

		it(
			'should return correct result if difference between target and collected is greater ' + 
			'than suggested but less than extra_fee * MAX_EXTRA_FEE_COEF (0.8)',
		 () => {
			let target  	= 140,
				collected  	= 50,
				suggested  	= 40,
				stripeFee  	= 10,
                feePerTeam  = 5,
                amount      = 100;

			let res = service.countSWFeeToTakeInPurchase(target, collected, suggested, stripeFee, feePerTeam, amount, feeCollectionSettings);

			expect(res).to.be.a('object');
			expect(res.application_fee).to.equal(100);
			expect(res.details).to.be.a('object');
			expect(res.details.sw_fee).to.equal(suggested);
			expect(res.details.extra_fee).to.equal(50);
			expect(res.details.provider_fee).to.equal(stripeFee);
		})

		it(
			'should return correct result if difference between target and collected is greater ' + 
			'than suggested but more than extra_fee * MAX_EXTRA_FEE_COEF (0.8)' +
            'and (amount - extra_fee - stripe_fee - sw_fee) >= 0',
		 () => {
			let target  	= 300,
				collected  	= 50,
				suggested  	= 30,
				stripeFee  	= 10,
                feePerTeam  = 5,
                amount      = 200;
            
			let res = service.countSWFeeToTakeInPurchase(target, collected, suggested, stripeFee, feePerTeam, amount, feeCollectionSettings);

			expect(res).to.be.a('object');
			expect(res.application_fee).to.equal(200);
			expect(res.details).to.be.a('object');
			expect(res.details.sw_fee).to.equal(suggested);
			expect(res.details.extra_fee).to.equal(160);
			expect(res.details.provider_fee).to.equal(stripeFee);
		})

        it(
            'should return correct result if difference between target and collected is greater ' +
            'than suggested but more than extra_fee * MAX_EXTRA_FEE_COEF (0.8)' +
            'and (amount - extra_fee - stripe_fee - sw_fee) < 0 ' +
            'and ((amount - extra_fee - stripe_fee - sw_fee) - extra_fee) % feePerTeam === 0',
            () => {
                let target  	= 300,
                    collected  	= 50,
                    suggested  	= 40,
                    stripeFee  	= 10,
                    feePerTeam  = 5,
                    amount      = 200;

                let res = service.countSWFeeToTakeInPurchase(
                    target,
                    collected,
                    suggested,
                    stripeFee,
                    feePerTeam,
                    amount,
                    feeCollectionSettings
                );

                expect(res).to.be.a('object');
                expect(res.application_fee).to.equal(200);
                expect(res.details).to.be.a('object');
                expect(res.details.sw_fee).to.equal(suggested);
                expect(res.details.extra_fee).to.equal(150);
                expect(res.details.provider_fee).to.equal(stripeFee);
            })

        it(
            'should return correct result if difference between target and collected is greater ' +
            'than suggested but more than extra_fee * MAX_EXTRA_FEE_COEF (0.8)' +
            'and (amount - extra_fee - stripe_fee - sw_fee) < 0 ' +
            'and ((amount - extra_fee - stripe_fee - sw_fee) - extra_fee) % feePerTeam !== 0',
            () => {
                let target  	= 300,
                    collected  	= 50,
                    suggested  	= 40,
                    stripeFee  	= 10,
                    feePerTeam  = 5,
                    amount      = 211;
                
                let res = service.countSWFeeToTakeInPurchase(
                    target,
                    collected,
                    suggested,
                    stripeFee,
                    feePerTeam,
                    amount,
                    feeCollectionSettings
                );

                expect(res).to.be.a('object');
                expect(res.application_fee).to.equal(210);
                expect(res.details).to.be.a('object');
                expect(res.details.sw_fee).to.equal(suggested);
                expect(res.details.extra_fee).to.equal(160);
                expect(res.details.provider_fee).to.equal(stripeFee);
            })

        it(
            'should return correct values if collect_extra_fee is false',
            () => {
                let target  	= 10,
                    collected  	= 10,
                    suggested  	= 3,
                    stripeFee  	= 2.3,
                    feePerTeam  = 5,
                    amount      = 100,
                    settings= {...feeCollectionSettings, collect_extra_fee: false};


                let res = service.countSWFeeToTakeInPurchase(target, collected, suggested, stripeFee, feePerTeam, amount, settings);

                expect(res).to.be.a('object');
                expect(res.application_fee).to.equal(stripeFee);
                expect(res.details).to.be.a('object');
                expect(res.details.sw_fee).to.equal(0);
                expect(res.details.extra_fee).to.equal(0);
                expect(res.details.provider_fee).to.equal(stripeFee);
            })

        it(
            'should return correct values if do_not_collect_sw_fee is true',
            () => {
                let target  	= 10,
                    collected  	= 10,
                    suggested  	= 3,
                    stripeFee  	= 2.3,
                    feePerTeam  = 5,
                    amount      = 100,
                    settings= {...feeCollectionSettings, do_not_collect_sw_fee: true};

                let res = service.countSWFeeToTakeInPurchase(target, collected, suggested, stripeFee, feePerTeam, amount, settings);

                expect(res).to.be.a('object');
                expect(res.application_fee).to.equal(stripeFee);
                expect(res.details).to.be.a('object');
                expect(res.details.sw_fee).to.equal(0);
                expect(res.details.extra_fee).to.equal(0);
                expect(res.details.provider_fee).to.equal(stripeFee);
            })
	})

	context('Refund partial proceed()', function () {

		let _purchaseMock,
            partialRefundService;

		before(() => {
			_purchaseMock = {
				event_id 		: 1,
				purchase_id 	: 2,
				amount 			: 3
			}

            partialRefundService = service.refund.partial;
		})


		it('should reject if invalid "payment" object passed', () =>
            partialRefundService.proceed(null).should.be.rejectedWith({ validation: 'Invalid payment passed' })
		)

		it('should reject if non-integer "event_id" passed', () =>
            partialRefundService.proceed(_.defaults({ event_id: 'test' }, _purchaseMock))
			.should.be.rejectedWith({ validation: 'Event ID should be an integer' })
		)

		it('should reject if non-integer "purchase_id" passed', () =>
            partialRefundService.proceed(_.defaults({ purchase_id: 'test' }, _purchaseMock))
			.should.be.rejectedWith({ validation: 'Purchase ID should be an integer' })
		)

		it('should reject if non-number "amount" passed', () =>
            partialRefundService.proceed(_.defaults({ amount: 'test' }, _purchaseMock))
			.should.be.rejectedWith({ validation: 'Amount should be a number' })
		)

		it('should reject if non-array "teams" passed', () =>
            partialRefundService.proceed(_purchaseMock, null)
			.should.be.rejectedWith({ validation: 'Invalid Teams List passed' })
		)

		it('should reject if invalid "teams" passed', () =>
            partialRefundService.proceed(_purchaseMock, [])
			.should.be.rejectedWith({ validation: 'Empty Teams List passed' })
		)
		
		it('should reject if "purchase" row not found', () => {
			let mock = _.clone(_purchaseMock);
			mock.purchase_id *= 100;

            partialRefundService.proceed(mock, rosterTeamRows)
			.should.be.rejectedWith({ validation: 'Payment not found or already canceled' })
		})

		// TODO: create mocks for settings() results;
		it.skip('should reject if "purchase" row has type not "card" or "ach"', () =>
            partialRefundService.proceed()
			.should.be.rejectedWith({ validation: 'Empty Teams List passed' })
		)
		// TODO
		it.skip('should reject if purchase teams count differs from passed teams count', () => {})
		// TODO
		it.skip('should reject if wrong amount passed', () => {})
		// TODO
		it.skip('should make a refund', () => {})
	})

	context('Refund partial __recountRefund()', function () {
		it('should recount totals', () => {
			let res = service.refund.partial.__recountRefund([
				{ roster_team_id: 1, canceled: false , discount: 10 , sw_fee: 5, surcharge: 10, reg_fee: 200 },
				{ roster_team_id: 2, canceled: true  , discount: 0  , sw_fee: 5, surcharge: 10, reg_fee: 200 },
				{ roster_team_id: 3, canceled: false , discount: 0  , sw_fee: 5, surcharge: 10, reg_fee: 200 },
				{ roster_team_id: 4, canceled: true  , discount: 0  , sw_fee: 5, surcharge: 10, reg_fee: 200 }
			]);

			expect(res).to.be.a('object');
			expect(res.total).to.equal(410);
			expect(res.swFee).to.equal(10);
			expect(res.netProfit).to.equal(410);
			expect(res.items).to.eql([
				{ amount: 190 	, discount: 10  , roster_team_id: 1, canceled: false, surcharge: 10, 	},
				{ amount: 0 	, discount: 0 	, roster_team_id: 2, canceled: true, surcharge: 0, 	},
				{ amount: 200 	, discount: 0 	, roster_team_id: 3, canceled: false, surcharge: 10, 	},
				{ amount: 0 	, discount: 0 	, roster_team_id: 4, canceled: true, surcharge: 0, 	}
			]);
			expect(res.activeTeamsQty).to.equal(2);
		})

	})

	context('generateStripeFee()', function () {

		it('should count Stripe fee for card payment', () => {
			let stripeFee = service.generateStripeFee({
				type 			: 'card',
				total 			: 100,
				stripe_percent 	: 0.027,
				stripe_fixed 	: 0.3
			});

			expect(stripeFee).to.equal(3);
		})

        it('should count Stripe fee for card payment when buyer is a stripe fee payer', () => {
            let stripeFee = service.generateStripeFee({
                type: 'card',
                total: 100,
                stripe_percent: 0.027,
                stripe_fixed: 0.3,
                fee_payer: FEE_PAYER.BUYER,
            });

            expect(stripeFee).to.equal(3.08);
        })

		it('should count Stripe fee for ACH payment', () => {
			let stripeFee = service.generateStripeFee({
				type 			: 'ach',
				total 			: 100,
				ach_percent 	: 0.005,
				ach_max			: 5
			});

			expect(stripeFee).to.equal(0.5);
		})

        it('should count Stripe fee for ACH payment when buyer is a stripe fee payer', () => {
            let stripeFee = service.generateStripeFee({
                type 			: 'ach',
                total 			: 100,
                ach_percent 	: 0.005,
                ach_max			: 5,
                fee_payer: FEE_PAYER.BUYER,
            });

            expect(stripeFee).to.equal(0.5);
        })

		it('should throw exception on invalid payment type', () => {
			let fn = service.generateStripeFee.bind(service, {
				type 			: 'check',
				total 			: 100,
				stripe_percent 	: 0.027,
				stripe_fixed 	: 0.3
			});

			expect(fn).to.throw(Error, /Type "check" is not supported/);
		})
	})

	context('generatePaymentDescription()', function () {
		
		it('should generate description for user with phone', () => {
            let descr = service.generatePaymentDescription({
                    first 	: 'John',
                    last 	: 'Smith',
                    email 	: '<EMAIL>',
                    phone 	: '1111111111'
                }, {
                    amount 		 : 100,
                    namesOfTeams : 'Test Team name'
                }, 'Test Event Name'
            );

			let resStr = 'Event name: Test Event Name; Amount: $100.00; Name: John Smith; Email: <EMAIL>;' +
						 ' Phone: 1111111111; Teams: Test Team name'

			expect(descr).to.equal(resStr);
		})

		it('should generate description for user without phone', () => {
			let descr = service.generatePaymentDescription({
				first 	: 'John',
				last 	: 'Smith',
				email 	: '<EMAIL>'
			}, {
				amount 		 : 100,
				namesOfTeams : 'Test Team name'
			}, 'Test Event Name');

			let resStr = 'Event name: Test Event Name; Amount: $100.00; Name: John Smith; Email: <EMAIL>;' +
						 ' Teams: Test Team name'

			expect(descr).to.equal(resStr);
		})

	})

	context('getTeamsNamesStr()', function () {
		
		it('should generate string of teams\' names', () => {
			let result = service.getTeamsNamesStr(
				[
					{ name: 'Team 1' },
					{ name: 'Team 2' },
					{ name: 'Team 3' }
				]
			);

			expect(result).to.equal('"Team 1", "Team 2", "Team 3"');
		})

		it('should return empty string if no rows passed', () => {
			let result = service.getTeamsNamesStr([]);

			expect(result).to.equal('');
		})

	})

	context('modifyCollectedEscrowSWFeeAmount()', function () {
		
		it('should reject if non-integer event id passed', () => {
			return service.modifyCollectedEscrowSWFeeAmount(null, 'test')
			.should.be.rejectedWith({ message: 'Event ID should be a positive integer' });
		})

		it('should reject if event id is less than 0', () => {
			return service.modifyCollectedEscrowSWFeeAmount(null, -0.00001)
			.should.be.rejectedWith({ message: 'Event ID should be a positive integer' });
		})

		it('should reject if event id equals 0', () => {
			return service.modifyCollectedEscrowSWFeeAmount(null, 0)
			.should.be.rejectedWith({ message: 'Event ID should be a positive integer' });
		})

		it('should reject if non-numeric amount passed', () => {
			return service.modifyCollectedEscrowSWFeeAmount(null, eventRow.event_id, 'test')
			.should.be.rejectedWith({ message: 'Amount should be a number' });
		})

		it('should return false if event not found', () => {
			let amount = 10.5;

			return service.modifyCollectedEscrowSWFeeAmount(null, eventRow.event_id * 100, amount)
			.then(result => {
				expect(result).to.be.false;
			})
		})

		it('should increase escrow amount', () => {
			let amount = 10.5;

			return service.modifyCollectedEscrowSWFeeAmount(null, eventRow.event_id, amount)
			.then(result => {
				expect(result).to.be.true;

				return findEventRow(eventRow.event_id);
			})
			.then(eventRow => {
				let escrow = parseFloat(eventRow.teams_escrow_collected, 10);

				expect(escrow).to.equal(10.5);
			})
		})

		it('should decrease escrow amount', () => {
			let amount = -10.5;

			return service.modifyCollectedEscrowSWFeeAmount(null, eventRow.event_id, amount)
			.then(result => {
				expect(result).to.be.true;

				return findEventRow(eventRow.event_id);
			})
			.then(eventRow => {
				let escrow = parseFloat(eventRow.teams_escrow_collected, 10);

				expect(escrow).to.equal(0);
			})
		})

	})

	context('getPaymentSettings', () =>  {
		const eventRow = require('./fixture/TeamsPaymentService/getPaymentSettings/event.row.json');
		const divisionRow = require('./fixture/TeamsPaymentService/getPaymentSettings/division.row.json');
		const masterTeamRow = require('./fixture/TeamsPaymentService/getPaymentSettings/master_team.row.json');
		const masterClubRow = require('./fixture/TeamsPaymentService/getPaymentSettings/master_club.row.json');
		const rosterClubRow = require('./fixture/TeamsPaymentService/getPaymentSettings/roster_club.row.json');
		const rosterTeamRows = require('./fixture/TeamsPaymentService/getPaymentSettings/roster_team.rows.json');

		let eventID = null;
		let masterClubID = null;
		let masterTeamID = null;
		let divisionID = null;
		let rosterClubID = null;
		let rosterTeams = [];
		
		const ENTRY_STATUS = {
			DECLINED: 11,
		};

		before(async () => {
			masterClubID = await Db.query(
				knex('master_club').insert(masterClubRow).returning('master_club_id')
			).then(({ rows: [{ master_club_id }] }) => master_club_id)

			masterTeamID = await Db.query(
				knex('master_team').insert(Object.assign({}, masterTeamRow, { master_club_id: masterClubID})).returning('master_team_id')
			).then(({ rows: [{ master_team_id }] }) => master_team_id);

			eventID = await Db.query(
				knex('event').insert(eventRow).returning('event_id')
			).then(({ rows: [{ event_id }] }) => event_id);

			rosterClubID = await Db.query(
				knex('roster_club').insert(Object.assign({}, rosterClubRow, { event_id: eventID, master_club_id: masterClubID})).returning('roster_club_id')
			).then(({ rows: [{ roster_club_id }] }) => roster_club_id)

			divisionID  = await Db.query(
				knex('division').insert(Object.assign({}, divisionRow, { event_id: eventID })).returning('division_id')
			).then(({ rows: [{ division_id }] }) => division_id);


			rosterTeamRows.forEach(team => {
				team.event_id = eventID;
				team.division_id = divisionID;
				team.master_team_id = masterTeamID;
				team.roster_club_id = rosterClubID;
			});

			rosterTeams = await Db.query(
				knex('roster_team').insert(rosterTeamRows).returning('roster_team_id')
			).then(({ rows }) => rows);
		});

		after(async () => {
			await Db.query(knex('event').where('event_id', eventID).del());
			await Db.query(knex('division').where('division_id', divisionID).del());
			await Db.query(knex('master_team').where('master_team_id', masterTeamID).del());
			await Db.query(knex('master_club').where('master_club_id', masterClubID).del());
			await Db.query(knex('roster_club').where('roster_club_id', rosterClubID).del());
			await Db.query(knex('roster_team').whereIn('roster_team_id', rosterTeams.map(({ roster_team_id }) => roster_team_id)).del());
		});

		it(`shoud return correct object`, async () => {
			const res = await service.getPaymentSettings(eventID, masterClubID, 2020, false);

			expect(res).to.be.a('object');
			expect(res.event).to.be.a('object')
			expect(res.master_club).to.be.a('object')
			expect(res.teams).to.be.a('array')
		});

		if(`should return not empty teams array`, async () => {
			const res = await service.getPaymentSettings(eventID, masterClubID, 2020, false);

			expect(res.teams).to.not.equal([]);
		})

		it('should return not declined teams', async () => {
			const res = await service.getPaymentSettings(eventID, masterClubID, 2020, false);

			const isTeamsNotDeclined = res.teams
				.map(({ entry_status }) => entry_status)
				.every(status => status !== ENTRY_STATUS.DECLINED);

			expect(isTeamsNotDeclined).to.be.true;
		});

		it(`should throw error 'No teams available. Make sure you did assign teams to the event'`, () => {
			service.getPaymentSettings(0, 0, 0, false)
				.should.be.rejectedWith({ validation: 'No teams available. Make sure you did assign teams to the event' });
		});
	})
});
