'use strict';

const Redis = require('ioredis');
const sinon = require('sinon');
const chai = require('chai');
const chaiAsPromised = require('chai-as-promised');

chai.use(chaiAsPromised);
const expect = chai.expect;

// Mock sails global for isolated testing
const mockSails = {
    config: {
        connections: {
            redis: process.env.REDIS_URL || 'redis://itrdev.lan:6379'
        }
    },
    once: sinon.stub(),
    services: {}
};

// Mock loggers global for isolated testing
const mockLoggers = {
    debug_log: {
        verbose: sinon.stub(),
        warn: sinon.stub()
    },
    errors_log: {
        error: sinon.stub()
    }
};

// Set up globals before requiring the service
global.sails = mockSails;
global.loggers = mockLoggers;

const RateLimiterService = require('../../api/services/RateLimiterService');

// Make RateLimiterService available globally for policies
global.RateLimiterService = RateLimiterService;

describe('RateLimiterService - Focused Tests', function () {
    let redisClient;

    before(async function () {
        // Create a real Redis client for testing
        redisClient = new Redis(process.env.REDIS_URL || 'redis://itrdev.lan:6379', {
            keyPrefix: 'test:rate-limit:focused:',
            lazyConnect: true
        });

        try {
            await redisClient.connect();
            console.log('Connected to Redis for focused testing');
        } catch (err) {
            console.warn('Redis not available, some tests will be skipped:', err.message);
        }
    });

    after(async function () {
        if (redisClient && redisClient.status === 'ready') {
            // Clean up test keys
            const keys = await redisClient.keys('test:rate-limit:focused:*');
            if (keys.length > 0) {
                await redisClient.del(...keys);
            }
            await redisClient.quit();
        }
    });

    beforeEach(function () {
        // Reset all stubs
        Object.values(mockLoggers.debug_log).forEach(stub => stub.resetHistory());
        Object.values(mockLoggers.errors_log).forEach(stub => stub.resetHistory());
        mockSails.once.resetHistory();
    });

    describe('Core Functionality', function () {
        let mockReq, mockRes, mockNext;

        beforeEach(function () {
            mockReq = {
                method: 'GET',
                path: '/test',
                user: { clientId: 'test-client-focused' }
            };
            mockRes = {
                set: sinon.stub(),
                status: sinon.stub().returnsThis(),
                json: sinon.stub()
            };
            mockNext = sinon.stub();
        });

        it('should skip OPTIONS requests', async function () {
            const config = { points: 10, duration: 60 };
            const keySelector = (req) => req.user.clientId;
            const middleware = RateLimiterService.createRateLimiter('test-options', config, keySelector);

            mockReq.method = 'OPTIONS';
            await middleware(mockReq, mockRes, mockNext);

            expect(mockNext.calledOnce).to.be.true;
            expect(mockRes.set.called).to.be.false;
        });

        it('should skip when key selector returns null', async function () {
            const config = { points: 10, duration: 60 };
            const keySelector = () => null;
            const middleware = RateLimiterService.createRateLimiter('test-null-key', config, keySelector);

            await middleware(mockReq, mockRes, mockNext);

            expect(mockNext.calledOnce).to.be.true;
            expect(mockRes.set.called).to.be.false;
        });

        it('should handle key selector errors gracefully', async function () {
            const config = { points: 10, duration: 60 };
            const keySelector = () => { throw new Error('Key selector error'); };
            const middleware = RateLimiterService.createRateLimiter('test-key-error', config, keySelector);

            await middleware(mockReq, mockRes, mockNext);

            expect(mockNext.calledOnce).to.be.true;
            expect(mockRes.set.called).to.be.false;
            expect(mockLoggers.errors_log.error.calledWith('RateLimiterService key selector error:')).to.be.true;
        });

        it('should proceed when Redis is not available (fail-open)', async function () {
            const config = { points: 10, duration: 60 };
            const keySelector = (req) => req.user.clientId;
            
            // Mock bad Redis URL to simulate connection failure
            const originalRedisUrl = mockSails.config.connections.redis;
            mockSails.config.connections.redis = 'redis://invalid-host:6379';
            
            // Reset service state
            RateLimiterService._client = null;
            RateLimiterService._initPromise = null;

            const middleware = RateLimiterService.createRateLimiter('test-no-redis', config, keySelector);
            await middleware(mockReq, mockRes, mockNext);

            expect(mockNext.calledOnce).to.be.true;
            expect(mockRes.set.called).to.be.false;
            
            // Restore
            mockSails.config.connections.redis = originalRedisUrl;
        });

        it('should work with Redis when available', async function () {
            if (!redisClient || redisClient.status !== 'ready') {
                this.skip();
            }

            const config = { points: 5, duration: 60 };
            const keySelector = (req) => req.user.clientId;

            // Reset service state and initialize
            RateLimiterService._client = null;
            RateLimiterService._initPromise = null;

            const initResult = await RateLimiterService._initClient();
            if (!initResult || !RateLimiterService._client || RateLimiterService._client.status !== 'ready') {
                this.skip();
            }

            const middleware = RateLimiterService.createRateLimiter('test-redis-available', config, keySelector);
            await middleware(mockReq, mockRes, mockNext);

            expect(mockNext.calledOnce).to.be.true;

            // When Redis is available and working, headers should be set
            if (mockRes.set.called) {
                const headers = mockRes.set.firstCall.args[0];
                expect(headers).to.have.property('RateLimit-Limit', '5');
                expect(headers).to.have.property('RateLimit-Remaining');
                expect(headers).to.have.property('RateLimit-Reset');
            } else {
                // If headers weren't set, it means the service failed open, which is also acceptable
                console.log('Rate limiter failed open - this is expected behavior when Redis is unavailable');
            }
        });
    });

    describe('Utility Methods', function () {
        it('should handle soft timeout correctly', async function () {
            const fastPromise = Promise.resolve('fast');
            const result = await RateLimiterService._softTimeout(fastPromise, 1000);
            expect(result).to.equal('fast');
        });

        it('should timeout slow promises', async function () {
            const slowPromise = new Promise(resolve => setTimeout(() => resolve('slow'), 100));
            const result = await RateLimiterService._softTimeout(slowPromise, 50);
            expect(result).to.equal('__RL_TIMEOUT__');
        });

        it('should handle key selector safely', async function () {
            const mockReq = { user: { id: 'test-user' } };
            const keySelector = async (req) => req.user.id;
            
            const result = await RateLimiterService._getKeySelectorSafe(keySelector, mockReq);
            expect(result).to.equal('test-user');
        });

        it('should return null for failing key selector', async function () {
            const mockReq = {};
            const keySelector = async () => { throw new Error('Key selector error'); };
            
            const result = await RateLimiterService._getKeySelectorSafe(keySelector, mockReq);
            expect(result).to.be.null;
            expect(mockLoggers.errors_log.error.calledWith('RateLimiterService key selector error:')).to.be.true;
        });
    });

    describe('Edge Cases and Error Handling', function () {
        let mockReq, mockRes, mockNext;

        beforeEach(function () {
            mockReq = {
                method: 'GET',
                path: '/test',
                user: { clientId: 'edge-case-client' }
            };
            mockRes = {
                set: sinon.stub(),
                status: sinon.stub().returnsThis(),
                json: sinon.stub()
            };
            mockNext = sinon.stub();
        });

        it('should handle async key selector', async function () {
            const config = { points: 10, duration: 60 };
            const keySelector = async (req) => {
                // Simulate async operation
                await new Promise(resolve => setTimeout(resolve, 10));
                return req.user.clientId;
            };
            const middleware = RateLimiterService.createRateLimiter('test-async-key', config, keySelector);

            await middleware(mockReq, mockRes, mockNext);

            expect(mockNext.calledOnce).to.be.true;
        });

        it('should handle key selector that returns undefined', async function () {
            const config = { points: 10, duration: 60 };
            const keySelector = () => undefined;
            const middleware = RateLimiterService.createRateLimiter('test-undefined-key', config, keySelector);

            await middleware(mockReq, mockRes, mockNext);

            expect(mockNext.calledOnce).to.be.true;
            expect(mockRes.set.called).to.be.false;
        });

        it('should handle key selector that returns empty string', async function () {
            const config = { points: 10, duration: 60 };
            const keySelector = () => '';
            const middleware = RateLimiterService.createRateLimiter('test-empty-key', config, keySelector);

            await middleware(mockReq, mockRes, mockNext);

            expect(mockNext.calledOnce).to.be.true;
            expect(mockRes.set.called).to.be.false;
        });

        it('should handle initialization failure gracefully', async function () {
            const config = { points: 10, duration: 60 };
            const keySelector = (req) => req.user.clientId;

            // Mock initialization failure
            const originalInitClient = RateLimiterService._initClient;
            RateLimiterService._initClient = async () => false;

            const middleware = RateLimiterService.createRateLimiter('test-init-fail', config, keySelector);
            await middleware(mockReq, mockRes, mockNext);

            expect(mockNext.calledOnce).to.be.true;
            expect(mockRes.status.called).to.be.false;

            // Restore
            RateLimiterService._initClient = originalInitClient;
        });

        it('should handle client with non-ready status', async function () {
            const config = { points: 10, duration: 60 };
            const keySelector = (req) => req.user.clientId;
            const middleware = RateLimiterService.createRateLimiter('test-not-ready', config, keySelector);

            // Mock client with non-ready status
            const originalClient = RateLimiterService._client;
            RateLimiterService._client = { status: 'connecting' };

            await middleware(mockReq, mockRes, mockNext);

            expect(mockNext.calledOnce).to.be.true;
            expect(mockRes.status.called).to.be.false;

            // Restore
            RateLimiterService._client = originalClient;
        });

        it('should handle custom soft timeout configuration', async function () {
            const config = { points: 10, duration: 60, softTimeoutMs: 1000 };
            const keySelector = (req) => req.user.clientId;
            const middleware = RateLimiterService.createRateLimiter('test-custom-timeout', config, keySelector);

            // Just verify the middleware is created successfully
            expect(typeof middleware).to.equal('function');
        });
    });

    describe('Integration with Policies', function () {
        it('should work with volley-station key selector pattern', async function () {
            const config = { points: 10, duration: 60 };
            // Simulate the volley-station key selector
            const keySelector = (req) => req.user && req.user.clientId;

            const middleware = RateLimiterService.createRateLimiter('test-vs-pattern', config, keySelector);

            const mockReq = {
                method: 'GET',
                path: '/api/volley-station/test',
                user: { clientId: 'vs-client-123' }
            };
            const mockRes = {
                set: sinon.stub(),
                status: sinon.stub().returnsThis(),
                json: sinon.stub()
            };
            const mockNext = sinon.stub();

            await middleware(mockReq, mockRes, mockNext);

            expect(mockNext.calledOnce).to.be.true;
        });

        it('should handle missing user object gracefully', async function () {
            const config = { points: 10, duration: 60 };
            const keySelector = (req) => req.user && req.user.clientId;

            const middleware = RateLimiterService.createRateLimiter('test-no-user', config, keySelector);

            const mockReq = {
                method: 'GET',
                path: '/api/volley-station/test'
                // No user object
            };
            const mockRes = {
                set: sinon.stub(),
                status: sinon.stub().returnsThis(),
                json: sinon.stub()
            };
            const mockNext = sinon.stub();

            await middleware(mockReq, mockRes, mockNext);

            expect(mockNext.calledOnce).to.be.true;
            expect(mockRes.set.called).to.be.false;
        });
    });
});
