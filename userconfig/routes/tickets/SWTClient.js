module.exports = {
    /**
     *
     * @api {get} /api/tickets/events Events List
     * @apiDescription Returns events list with opened tickets / camps sales
     * @apiGroup Tickets SWTClient
     *
     */
    'get /api/tickets/events': apiPath('EventController.eventsList'),

    /**
     *
     * @api {get} /api/tickets/events/:event Event Tickets List
     * @apiDescription Returns event tickets list
     * @apiGroup Tickets SWTClient
     *
     */
    'get /api/tickets/events/:event': apiPath('EventController.eventTickets'),

    /**
     *
     * @api {get} /api/tickets/events/:event/types Event Ticket Types List
     * @apiDescription Returns event ticket types list
     * @apiGroup Tickets SWTClient
     *
     */
    'get /api/tickets/events/:event/types': apiPath('EventController.reloadTicketTypes'),

    /**
     *
     * @api {get} /api/tickets/events/:event/types Invoice Data
     * @apiDescription Returns invoice payment
     * @apiGroup Tickets SWTClient
     *
     */
    'get /api/tickets/events/:event/invoice/:invoice': apiPath('EventController.loadInvoice'),

    /**
     *
     * @api {get} /api/tickets/events/:event/invoice/:invoice/type-change Payment Data for Type Change
     * @apiDescription Returns purchase data for further payment type change
     * @apiGroup Tickets SWTClient
     *
     */
    'get /api/tickets/events/:event/invoice/:invoice/type-change': apiPath('EventController.loadPaymentForTypeChange'),

    /**
     *
     * @api {post} /api/tickets/user/check Check User Email Uniqueness
     * @apiDescription Checks if user email is unique
     * @apiGroup Tickets SWTClient
     *
     */
    'post /api/tickets/user/check': {
        controller: apiPath('UserController'),
        action: 'userEmailCheck',
        csrf: false,
    },

    /**
     *
     * @api {post} /api/tickets/usav/check Validate Junior USAV Code
     * @apiDescription Checks if junior's USAV code is eligible and its data is valid
     * @apiGroup Tickets SWTClient
     *
     */
    'POST /api/tickets/usav/check': {
        controller: apiPath('UserController'),
        action: 'participantUSAVCheck',
        csrf: false,
    },

    /**
     *
     * @api {post} /api/tickets/buy/:type Buy Tickets
     * @apiDescription Tickets purchase
     * @apiGroup Tickets SWTClient
     *
     */
    'post /api/tickets/buy/:type': {
        controller: apiPath('PaymentController'),
        action: 'buyTickets',
        csrf: false,
    },

    /**
     *
     * @api {get} /api/tickets/user/purchases User Purchases List
     * @apiDescription Returns user purchases list
     * @apiGroup Tickets SWTClient
     *
     */
    'get /api/tickets/user/purchases': apiPath('PaymentController.getUserPurchases'),

    /**
     *
     * @api {post} /api/tickets/event/:event/invoice/:invoice/pay Pay Waitlisted Camp
     * @apiDescription Creates a payment for waitlisted camp
     * @apiGroup Tickets SWTClient
     *
     */
    'post /api/tickets/event/:event/invoice/:invoice/pay': {
        controller: apiPath('PaymentController'),
        action: 'payWaitlist',
        csrf: false
    },

    /**
     *
     * @api {get} /tickets/receipt/:code Render Receipt (by ticket hash)
     * @apiDescription Returns rendered payment receipt (by ticket hash)
     * @apiGroup Tickets SWTClient
     *
     */
    'get /tickets/receipt/:code': apiPath('ReceiptController.viewReceipt'),

    /**
     *
     * @api {get} /event/:event/ticket/:barcode/receipt Render Receipt (by ticket barcode)
     * @apiDescription Returns rendered payment receipt (by ticket barcode)
     * @apiGroup Tickets SWTClient
     *
     */
    'get /event/:event/ticket/:barcode/receipt': apiPath('ReceiptController.viewBarcodeReceipt'),

    /**
     *
     * @api {get} /r/:barcode Render Receipt (by short link)
     * @apiDescription Returns rendered payment receipt (by short link)
     * @apiGroup Tickets SWTClient
     *
     */
    'GET /r/:barcode': apiPath('ReceiptController.shortLinkReceipt'),

    /**
     *
     * @api {post} /api/tickets/coupon/check Check Discount Coupon
     * @apiDescription Validates ticket discount coupon
     * @apiGroup Tickets SWTClient
     *
     */
    'post /api/tickets/coupon/check': {
        controller: apiPath('CouponController'),
        action: 'checkCoupon',
        csrf: false,
    },

    /**
     *
     * @api {get} /api/tickets/discounts/resend Resend Discount Coupon
     * @apiDescription Sends discount coupon to user
     * @apiGroup Tickets SWTClient
     *
     */
    'get /api/tickets/discounts/resend': apiPath('CouponController.findAndResend'),

    /**
     *
     * @api {post} /api/tickets/event/:event/change-type/:invoice Change Payment Type to Card
     * @apiDescription Changes payment type to card
     * @apiGroup Tickets SWTClient
     *
     */
    'post /api/tickets/event/:event/change-type/:invoice': {
        controller: apiPath('PaymentController'),
        action: 'changeTypeToCard',
        csrf: false,
    },

    /**
     *
     * @api {post} /api/tickets/events/:event/payment-discount Event Payment Discounts List
     * @apiDescription Returns discounts list created on event
     * @apiGroup Tickets SWTClient
     *
     */
    'POST /api/tickets/events/:event/payment-discount': {
        controller: apiPath('PaymentDiscountController'),
        action: 'getEventPaymentDiscount',
        csrf: false,
    },

    /**
     *
     * @api {get} /api/kiosk/events/:event Event Tickets List (for kiosk)
     * @apiDescription Returns event ticket types for kiosk
     * @apiGroup Tickets SWTClient
     *
     */
    'get /api/kiosk/events/:event': apiPath('EventController.kioskTickets'),

    /**
     *
     * @api {post} /api/kiosk/buy Buy Tickets (kiosk)
     * @apiDescription Creates tickets payment (kiosk)
     * @apiGroup Tickets SWTClient
     *
     */
    'POST /api/kiosk/buy': {
        controller: apiPath('PaymentController'),
        action: 'buyTicketsInKiosk',
        csrf: false
    },

    /**
     *
     * @api {post} /api/tickets/duplicates/validation Find Purchase Duplicates
     * @apiDescription Checks if the user made the exact same purchase
     * @apiGroup Tickets SWTClient
     *
     */
    'POST /api/tickets/duplicates/validation': {
        controller: apiPath('PaymentDuplicateController'),
        action: 'findPaymentDuplicates',
        csrf: false
    },

    /**
     *
     * @api {get} /api/tickets/events/:event/coupons Tickets List Allowed to Buy with Coupon
     * @apiDescription Returns ticket types list allowed to buy with selected coupon(s)
     * @apiGroup Tickets SWTClient
     *
     */
    'GET /api/tickets/events/:event/coupons': apiPath('EventController.getAllowedTicketsToBuy'),

    /**
     *
     * @api {post} /api/point-of-sales/:pointOfSalesId/validate-entry-code Ticket Coupon Codes Validation By PosID
     * @apiDescription Validates ticket coupon codes by PosID
     * @apiGroup Tickets SWTClient
     *
     */
    'POST /api/point-of-sales/:pointOfSalesId/validate-coupon-codes': {
        action: 'v2/API/other/validate-coupon-codes-by-pos-id',
        csrf: false
    },

    /**
     *
     * @api {get} /pt/:hash Purchase Tickets List Page
     * @apiDescription Returns ejs template with purchase's ticket list
     * @apiGroup Tickets SWTClient
     *
     */
    'GET /pt/:hash': {
        controller: apiPath('ReceiptController'),
        action: 'viewPurchaseTickets',
        csrf: false
    }
};

function apiPath (ctrl) {
    return 'API/Tickets/' + ctrl;
}

