'use strict';

module.exports = {
    /**
     *
     * @api {post} /api/eo/stripe-acc Event Owner Stripe Account Upsert
     * @apiDescription Upsert EO's stripe account
     * @apiGroup Event Owner
     *
     */
	'POST /api/eo/stripe-acc': 'Event/EventOwnerController.saveStripeAccount',
    
    /**
     *
     * @api {put} /api/eo/stripe-acc/account/:id/visibility/:visibility(hidden|visible)  Event Owner Stripe Account Change Visibility 
     * @apiDescription Change stripe account visibility
     * @apiGroup Event Owner
     *
     */
	'PUT /api/eo/stripe-acc/account/:id/visibility/:visibility': 'Event/EventOwnerController.toggleStripeAccountVisibility',

    /**
     *
     * @api {get} /api/eo/tilled-accounts/:type Event Owner Tilled Accounts List
     * @apiDescription Returns EO's tilled accounts list
     * @apiGroup Event Owner
     *
     */
	'GET /api/eo/tilled-accounts/:type': {
        action: 'v2/tilled/accounts'
    },

    /**
     *
     * @api {get} /api/eo/stripe-accounts/:type Event Owner Stripe Accounts List
     * @apiDescription Returns EO's stripe accounts list
     * @apiGroup Event Owner
     *
     */
	'GET /api/eo/stripe-accounts/:type': 'Event/EventOwnerController.getStripeAccounts',

    /**
     *
     * @api {put} /api/eo/stripe-acc/save_platform_client_id Event Owner Stripe Account Platform Key Save
     * @apiDescription Saves stripe account platform key
     * @apiGroup Event Owner
     *
     */
	'PUT /api/eo/stripe-acc/save_platform_client_id': 'Event/EventOwnerController.savePlatformClientId',

    /**
     *
     * @api {get} /api/eo/:event/stripe-accounts/has_platform Event Owner Stripe Account "Is Platform" Check
     * @apiDescription Returns TRUE if EO's account is a Stripe Platform
     * @apiGroup Event Owner
     *
     */
	'GET /api/eo/:event/stripe-accounts/has_platform': 'Event/EventOwnerController.hasEventPlatform'
};
