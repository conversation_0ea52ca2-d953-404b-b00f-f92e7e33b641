- hosts: all
  vars:
    deploy_folder: /home/<USER>/sw-main

  tasks:
  - name: Copy files to the server
    synchronize:
      src: ../../
      dest: "{{ deploy_folder }}"
      rsync_opts:
      - "--no-motd"
      - "--exclude=.git"
      - "--exclude=package.json"
      - "--exclude=package-lock.json"
      - "--exclude=.gitlab-ci.yml"
      - "--exclude=node_modules"
      - "--exclude=.tmp"
      - "--exclude=Dockerfile"
      - "--exclude=.dockerignore"
      - "--delete"
